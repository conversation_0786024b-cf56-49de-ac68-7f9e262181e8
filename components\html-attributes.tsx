'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { languages, isRtlLang } from '@/lib/i18n/languages';

/**
 * Client component that sets HTML attributes based on the current URL path
 * This ensures the correct language and direction are set on the HTML element
 */
export function HtmlAttributes() {
  const pathname = usePathname();

  useEffect(() => {
    // Extract language from URL
    const urlLang = pathname.split('/')[1];
    const isValidLang = languages.some((lang) => lang.code === urlLang);
    
    if (isValidLang) {
      // Set language attribute
      document.documentElement.lang = urlLang;
      
      // Set direction attribute
      const direction = isRtlLang(urlLang) ? 'rtl' : 'ltr';
      document.documentElement.dir = direction;
      
      console.log(`[HtmlAttributes] Set HTML attributes: lang="${urlLang}", dir="${direction}"`);
    }
  }, [pathname]);

  // This component doesn't render anything
  return null;
}
