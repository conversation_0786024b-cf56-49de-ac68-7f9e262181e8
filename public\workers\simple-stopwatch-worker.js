let intervalId = null;
let startTime = 0; // Timestamp when the stopwatch started
let accumulatedTime = 0; // Accumulated time in milliseconds from previous runs
let isRunning = false;

self.onmessage = function(event) {
  const { command, value } = event.data;

  if (command === 'start') {
    if (!isRunning) {
      isRunning = true;
      startTime = Date.now(); // Record the current timestamp
      
      // Clear any existing interval before starting a new one
      if (intervalId) {
        clearInterval(intervalId);
      }

      intervalId = setInterval(() => {
        if (!isRunning) return;
        
        const now = Date.now();
        const currentRunTime = now - startTime;
        const totalTime = accumulatedTime + currentRunTime;
        
        // Send the total elapsed time in milliseconds
        self.postMessage({ 
          type: 'tick', 
          timeElapsed: totalTime 
        });
      }, 50); // Update every 50ms for smooth display
    }
  } else if (command === 'pause') {
    if (isRunning && intervalId) {
      // Calculate and store the accumulated time
      const now = Date.now();
      const currentRunTime = now - startTime;
      accumulatedTime += currentRunTime;
      
      // Stop the timer
      clearInterval(intervalId);
      intervalId = null;
      isRunning = false;
      startTime = 0;
      
      // Send final time
      self.postMessage({ 
        type: 'paused', 
        timeElapsed: accumulatedTime 
      });
    }
  } else if (command === 'reset') {
    // Stop the timer
    if (intervalId) {
      clearInterval(intervalId);
      intervalId = null;
    }
    
    // Reset all state
    isRunning = false;
    startTime = 0;
    accumulatedTime = 0;
    
    // Send reset confirmation
    self.postMessage({ 
      type: 'reset', 
      timeElapsed: 0 
    });
  }
};
