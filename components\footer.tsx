"use client"

import { getTranslations } from "@/lib/i18n/translations"
import { getTranslatedRoute } from "@/lib/i18n/route-translations"
import Link from "next/link"
import { Clock, Timer, Globe, ListTodo, Activity } from "lucide-react"

interface FooterProps {
  lang?: string
}

export function Footer({ lang = "en" }: FooterProps) {
  const t = getTranslations(lang)

  // Helper function to get localized links
  const getLocalizedLink = (route: string) => {
    const translatedRoute = getTranslatedRoute(lang, route)
    return `/${lang}/${translatedRoute}`
  }

  const timeTools = [
    {
      name: t.timerStopwatch,
      href: getLocalizedLink("timer"),
      icon: <Timer className="h-4 w-4" />
    },
    {
      name: t.countdown,
      href: getLocalizedLink("countdown"),
      icon: <Clock className="h-4 w-4" />
    },
    {
      name: t.worldClock,
      href: getLocalizedLink("world-clock"),
      icon: <Globe className="h-4 w-4" />
    },
    {
      name: t.intervals,
      href: getLocalizedLink("intervals"),
      icon: <Activity className="h-4 w-4" />
    },
    {
      name: t.pomodoro,
      href: getLocalizedLink("pomodoro"),
      icon: <Timer className="h-4 w-4" />
    }
  ]

  const productivityTools = [
    {
      name: t.todoList,
      href: getLocalizedLink("todo"),
      icon: <ListTodo className="h-4 w-4" />
    },
    {
      name: t.timeTracking,
      href: getLocalizedLink("time-tracking"),
      icon: <Clock className="h-4 w-4" />
    }
  ]

  const fitnessTools = [
    {
      name: t.workoutIntervals,
      href: getLocalizedLink("workout-intervals"),
      icon: <Activity className="h-4 w-4" />
    },
    {
      name: t.exerciseTemplates,
      href: getLocalizedLink("exercise-templates"),
      icon: <ListTodo className="h-4 w-4" />
    }
  ]

  return (
    <footer className="bg-muted/30 border-t mt-12">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="space-y-4">
            <Link href={`/${lang}`} className="text-xl font-bold text-primary">
              {t.appName}
            </Link>
            <p className="text-sm text-muted-foreground">
              {t.homePageDescription}
            </p>
            <div className="flex flex-wrap gap-2 text-xs text-muted-foreground">
              <span>{t.multiLanguage}</span>
              <span>•</span>
              <span>{t.pwaOffline}</span>
              <span>•</span>
              <span>{t.freeToUse || "Gratuit"}</span>
            </div>
          </div>

          {/* Time Tools */}
          <div className="space-y-4">
            <h3 className="font-semibold text-sm uppercase tracking-wide">
              {t.timeTools}
            </h3>
            <ul className="space-y-2">
              {timeTools.map((tool, index) => (
                <li key={index}>
                  <Link
                    href={tool.href}
                    className="flex items-center gap-2 text-sm text-muted-foreground hover:text-primary transition-colors"
                  >
                    {tool.icon}
                    {tool.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Productivity Tools */}
          <div className="space-y-4">
            <h3 className="font-semibold text-sm uppercase tracking-wide">
              {t.productivitySuite}
            </h3>
            <ul className="space-y-2">
              {productivityTools.map((tool, index) => (
                <li key={index}>
                  <Link
                    href={tool.href}
                    className="flex items-center gap-2 text-sm text-muted-foreground hover:text-primary transition-colors"
                  >
                    {tool.icon}
                    {tool.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Fitness Tools */}
          <div className="space-y-4">
            <h3 className="font-semibold text-sm uppercase tracking-wide">
              {t.fitnessTools}
            </h3>
            <ul className="space-y-2">
              {fitnessTools.map((tool, index) => (
                <li key={index}>
                  <Link
                    href={tool.href}
                    className="flex items-center gap-2 text-sm text-muted-foreground hover:text-primary transition-colors"
                  >
                    {tool.icon}
                    {tool.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t mt-8 pt-8 flex flex-col md:flex-row justify-between items-center gap-4">
          <div className="text-sm text-muted-foreground">
            © 2025 {t.appName}. {t.allRightsReserved || "Tous droits réservés"}.
          </div>
          <div className="flex items-center gap-4 text-sm flex-wrap justify-center">
            <Link
              href={`/${lang}`}
              className="text-muted-foreground hover:text-primary transition-colors"
            >
              {t.home || "Accueil"}
            </Link>
            <span className="text-muted-foreground">•</span>
            <Link
              href={getLocalizedLink("sitemap")}
              className="text-muted-foreground hover:text-primary transition-colors"
            >
              {t.sitemap || "Plan du site"}
            </Link>
            <span className="text-muted-foreground">•</span>
            <a
              href="/terms"
              className="text-muted-foreground hover:text-primary transition-colors"
            >
              Terms
            </a>
            <span className="text-muted-foreground">•</span>
            <a
              href="/privacy-policy"
              className="text-muted-foreground hover:text-primary transition-colors"
            >
              Privacy
            </a>
            <span className="text-muted-foreground">•</span>
            <span className="text-muted-foreground">
              {t.madeWithLove || "Fait avec ❤️"}
            </span>
          </div>
        </div>
      </div>
    </footer>
  )
}
