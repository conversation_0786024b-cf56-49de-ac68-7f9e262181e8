"use client"

import { ReactNode, useEffect } from "react"
import { createPortal } from "react-dom"
import { But<PERSON> } from "@/components/ui/button"
import { X, Volume2, VolumeX } from "lucide-react"
import { useLanguage } from "@/components/language-provider"
import { useSound } from "@/components/sound-provider"
import { useRTL } from "@/hooks/useRTL"
import { getTranslations } from "@/lib/i18n/translations"

interface ToolFullscreenWrapperProps {
  isFullscreen: boolean
  onClose: () => void
  children: ReactNode
  toolName?: string
}

export function ToolFullscreenWrapper({
  isFullscreen,
  onClose,
  children,
  toolName
}: ToolFullscreenWrapperProps) {
  const { language } = useLanguage()
  const { isSoundEnabled, toggleSound } = useSound()
  const isRTL = useRTL(language)
  const t = getTranslations(language)

  // Gérer la touche Échap
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFullscreen) {
        onClose()
      }
    }

    if (isFullscreen) {
      document.addEventListener('keydown', handleKeyDown)
      // Empêcher le scroll du body
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      document.body.style.overflow = 'unset'
    }
  }, [isFullscreen, onClose])

  if (!isFullscreen) return null

  const fullscreenContent = (
    <div className="fixed inset-0 z-50 bg-background" dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Header avec contrôles - adapté RTL */}
      <div className={`absolute top-4 z-10 flex items-center gap-2 fullscreen-controls ${
        isRTL ? 'left-4' : 'right-4'
      }`}>
        {/* Bouton Son */}
        <Button
          variant="outline"
          size="icon"
          onClick={toggleSound}
          title={isSoundEnabled ? t.soundOff : t.soundOn}
          className="bg-background/80 backdrop-blur-sm"
        >
          {isSoundEnabled ?
            <Volume2 className="h-4 w-4" /> :
            <VolumeX className="h-4 w-4" />
          }
          <span className="sr-only">{isSoundEnabled ? t.soundOff : t.soundOn}</span>
        </Button>

        {/* Bouton Fermer */}
        <Button
          variant="outline"
          size="icon"
          onClick={onClose}
          title={t.exitFullscreen}
          className="bg-background/80 backdrop-blur-sm"
        >
          <X className="h-4 w-4" />
          <span className="sr-only">{t.exitFullscreen}</span>
        </Button>
      </div>

      {/* Titre de l'outil (optionnel) - adapté RTL */}
      {toolName && (
        <div className={`absolute top-4 z-10 ${
          isRTL ? 'right-4' : 'left-4'
        }`}>
          <h2 className="text-lg font-semibold bg-background/80 backdrop-blur-sm px-3 py-1 rounded fullscreen-title">
            {toolName}
          </h2>
        </div>
      )}

      {/* Contenu de l'outil en plein écran avec défilement vertical - adapté RTL */}
      <div className={`h-full w-full overflow-y-auto fullscreen-scrollable ${
        isRTL ? 'rtl:text-right' : ''
      }`}>
        <div className="min-h-full flex flex-col">
          {/* Espace pour les contrôles en haut */}
          <div className="h-16 flex-shrink-0"></div>

          {/* Contenu principal avec défilement - adapté RTL */}
          <div className={`flex-1 p-4 ${
            isRTL ? 'rtl:pr-4 rtl:pl-4' : ''
          }`}>
            <div className="w-full max-w-4xl mx-auto">
              {children}
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  // Utiliser un portail pour rendre en dehors de la hiérarchie normale
  return typeof window !== 'undefined'
    ? createPortal(fullscreenContent, document.body)
    : null
}
