'use client';

import { useEffect, Suspense } from 'react';
import Script from 'next/script';
import { usePathname, useSearchParams } from 'next/navigation';

// Votre ID de mesure Google Analytics
const GA_MEASUREMENT_ID = 'G-5VKKJZ9FCF'; // Remplacez par votre véritable ID de mesure

// Component that uses useSearchParams wrapped in Suspense
function AnalyticsPageTracker() {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    if (pathname && window.gtag) {
      // Envoyer une vue de page à Google Analytics lors des changements de route
      window.gtag('config', GA_MEASUREMENT_ID, {
        page_path: pathname + (searchParams?.toString() ? `?${searchParams.toString()}` : ''),
      });
    }
  }, [pathname, searchParams]);

  return null;
}

export function GoogleAnalytics() {
  return (
    <>
      {/* Script Google Analytics - Global Site Tag (gtag.js) */}
      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`}
      />
      <Script
        id="gtag-init"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${GA_MEASUREMENT_ID}', {
              page_path: window.location.pathname + window.location.search,
            });
          `,
        }}
      />
      <Suspense fallback={null}>
        <AnalyticsPageTracker />
      </Suspense>
    </>
  );
}

// Fonctions utilitaires pour tracker les événements PWA
export const trackPWAEvent = (eventName: string, parameters?: Record<string, any>) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, {
      event_category: 'PWA',
      ...parameters
    });
    console.log(`[GA] PWA Event tracked: ${eventName}`, parameters);
  }
};

// Événements spécifiques PWA
export const trackPWAInstall = () => {
  trackPWAEvent('pwa_install', {
    event_label: 'App Installed',
    value: 1
  });
};

export const trackPWALaunch = (displayMode: string) => {
  trackPWAEvent('pwa_launch', {
    event_label: `Launched in ${displayMode} mode`,
    custom_parameter_1: displayMode
  });
};

export const trackPWAOfflineUsage = () => {
  trackPWAEvent('pwa_offline_usage', {
    event_label: 'App used while offline'
  });
};

export const trackToolUsage = (toolName: string, action: string) => {
  trackPWAEvent('tool_usage', {
    event_label: `${toolName} - ${action}`,
    tool_name: toolName,
    tool_action: action
  });
};

export const trackTimerEvent = (timerType: string, duration: number, action: string) => {
  trackPWAEvent('timer_event', {
    event_label: `${timerType} - ${action}`,
    timer_type: timerType,
    timer_action: action,
    duration_seconds: Math.round(duration / 1000)
  });
};

// Déclaration pour TypeScript
declare global {
  interface Window {
    gtag: (
      command: string,
      target: string,
      config?: Record<string, any> | string
    ) => void;
    dataLayer: any[];
  }
}
