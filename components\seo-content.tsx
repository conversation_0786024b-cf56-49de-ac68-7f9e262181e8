"use client"

import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card"
import { getTranslations } from "@/lib/i18n/translations"
import { getTranslatedRoute } from "@/lib/i18n/route-translations"
import Link from "next/link"
import Script from "next/script"

interface SeoContentProps {
  lang?: string
}

export function SeoContent({ lang = "fr" }: SeoContentProps) {
  const t = getTranslations(lang)

  // Helper function to get localized links
  const getLocalizedLink = (route: string) => {
    const translatedRoute = getTranslatedRoute(lang, route)
    return `/${lang}/${translatedRoute}`
  }

  // Create structured data for FAQ
  const faqStructuredData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": t.faqQuestion1,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": t.faqAnswer1
        }
      },
      {
        "@type": "Question",
        "name": t.faqQuestion2,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": t.faqAnswer2
        }
      },
      {
        "@type": "Question",
        "name": t.faqQuestion3,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": t.faqAnswer3
        }
      },
      {
        "@type": "Question",
        "name": t.faqQuestion4,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": t.faqAnswer4
        }
      },
      {
        "@type": "Question",
        "name": t.faqQuestion5,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": t.faqAnswer5
        }
      },
      {
        "@type": "Question",
        "name": t.faqQuestion6,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": t.faqAnswer6
        }
      },
      {
        "@type": "Question",
        "name": t.faqQuestion7,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": t.faqAnswer7
        }
      },
      {
        "@type": "Question",
        "name": t.faqQuestion8,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": t.faqAnswer8
        }
      },
      {
        "@type": "Question",
        "name": t.faqQuestion9,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": t.faqAnswer9
        }
      },
      {
        "@type": "Question",
        "name": t.faqQuestion10,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": t.faqAnswer10
        }
      },
      {
        "@type": "Question",
        "name": t.faqQuestion11,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": t.faqAnswer11
        }
      },
      {
        "@type": "Question",
        "name": t.faqQuestion12,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": t.faqAnswer12
        }
      },
      {
        "@type": "Question",
        "name": t.faqQuestion13,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": t.faqAnswer13
        }
      },
      {
        "@type": "Question",
        "name": t.faqQuestion14,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": t.faqAnswer14
        }
      },
      {
        "@type": "Question",
        "name": t.faqQuestion15,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": t.faqAnswer15
        }
      }
    ]
  }

  // Create structured data for the website
  const websiteStructuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": t.appName,
    "applicationCategory": "UtilityApplication",
    "operatingSystem": "Web",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "description": t.homePageDescription
  }

  return (
    <div className="py-12">
      {/* Add structured data */}
      <Script
        id="faq-structured-data"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(faqStructuredData) }}
      />
      <Script
        id="website-structured-data"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(websiteStructuredData) }}
      />

      <div className="prose dark:prose-invert max-w-none">
        <h2 className="text-3xl font-bold mb-8">{t.seoTitle}</h2>
        <p className="text-xl mb-8">{t.seoIntro}</p>

        <div className="space-y-8">
          <Card>
            <CardHeader>
              <h3 className="text-xl font-semibold">{t.whatIsTimer}</h3>
            </CardHeader>
            <CardContent>
              <p>{t.timerExplanation}</p>
              <p className="mt-4">{t.timerDeviceInfo}</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <h3 className="text-xl font-semibold">{t.benefitsOfTimers}</h3>
            </CardHeader>
            <CardContent>
              <ul className="list-disc pl-6 space-y-2">
                <li>{t.benefit1}</li>
                <li>{t.benefit2}</li>
                <li>{t.benefit3}</li>
                <li>{t.benefit4}</li>
              </ul>
              <p className="mt-4">{t.benefitsExplanation}</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <h3 className="text-xl font-semibold">{t.howToUse}</h3>
            </CardHeader>
            <CardContent>
              <p>{t.howToUseExplanation}</p>
              <ol className="list-decimal pl-6 space-y-2 mt-4">
                <li>{t.step1}</li>
                <li>{t.step2}</li>
                <li>{t.step3}</li>
                <li>{t.step4}</li>
              </ol>
              <p className="mt-4">{t.toolsOfflineInfo}</p>
              <div className="mt-6 text-center">
                <Link
                  href={getLocalizedLink("sitemap")}
                  className="inline-flex items-center gap-2 text-primary hover:underline font-medium"
                >
                  {t.sitemap || "Plan du site"} - {t.exploreOurTools || "Explorez nos outils"} →
                </Link>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <h3 className="text-xl font-semibold">{t.faq}</h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* General Questions */}
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-primary border-b pb-2">{t.GeneralQuestions}</h4>
                  <div>
                    <h5 className="font-semibold">{t.faqQuestion1}</h5>
                    <p className="text-muted-foreground">{t.faqAnswer1}</p>
                  </div>
                  <div>
                    <h5 className="font-semibold">{t.faqQuestion2}</h5>
                    <p className="text-muted-foreground">{t.faqAnswer2}</p>
                  </div>
                  <div>
                    <h5 className="font-semibold">{t.faqQuestion3}</h5>
                    <p className="text-muted-foreground">{t.faqAnswer3}</p>
                  </div>
                  <div>
                    <h5 className="font-semibold">{t.faqQuestion4}</h5>
                    <p className="text-muted-foreground">{t.faqAnswer4}</p>
                  </div>
                </div>

                {/* Tools Questions */}
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-primary border-b pb-2">{t.ToolsQuestions}</h4>
                  <div>
                    <h5 className="font-semibold">{t.faqQuestion5}</h5>
                    <p className="text-muted-foreground">{t.faqAnswer5}</p>
                  </div>
                  <div>
                    <h5 className="font-semibold">{t.faqQuestion6}</h5>
                    <p className="text-muted-foreground">{t.faqAnswer6}</p>
                  </div>
                  <div>
                    <h5 className="font-semibold">{t.faqQuestion7}</h5>
                    <p className="text-muted-foreground">{t.faqAnswer7}</p>
                  </div>
                  <div>
                    <h5 className="font-semibold">{t.faqQuestion8}</h5>
                    <p className="text-muted-foreground">{t.faqAnswer8}</p>
                  </div>
                  <div>
                    <h5 className="font-semibold">{t.faqQuestion9}</h5>
                    <p className="text-muted-foreground">{t.faqAnswer9}</p>
                  </div>
                  <div>
                    <h5 className="font-semibold">{t.faqQuestion10}</h5>
                    <p className="text-muted-foreground">{t.faqAnswer10}</p>
                  </div>
                </div>

                {/* Technical Questions */}
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-primary border-b pb-2">{t.TechnicalQuestions}</h4>
                  <div>
                    <h5 className="font-semibold">{t.faqQuestion11}</h5>
                    <p className="text-muted-foreground">{t.faqAnswer11}</p>
                  </div>
                  <div>
                    <h5 className="font-semibold">{t.faqQuestion12}</h5>
                    <p className="text-muted-foreground">{t.faqAnswer12}</p>
                  </div>
                  <div>
                    <h5 className="font-semibold">{t.faqQuestion13}</h5>
                    <p className="text-muted-foreground">{t.faqAnswer13}</p>
                  </div>
                  <div>
                    <h5 className="font-semibold">{t.faqQuestion14}</h5>
                    <p className="text-muted-foreground">{t.faqAnswer14}</p>
                  </div>
                  <div>
                    <h5 className="font-semibold">{t.faqQuestion15}</h5>
                    <p className="text-muted-foreground">{t.faqAnswer15}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

