// Fonction pour gérer les cartes (cards) en mode RTL
export function adjustCardElements(toRTL: boolean) {
  // Sélectionner toutes les cartes
  const cards = document.querySelectorAll('.card, [role="card"], .box, .panel, .tile');
  
  cards.forEach(card => {
    if (toRTL) {
      card.classList.add('rtl-card');
      if (card instanceof HTMLElement) {
        card.style.direction = 'rtl';
        card.style.textAlign = 'right';
      }
      
      // Inverser l'ordre des éléments flexibles à l'intérieur des cartes
      const flexContainers = card.querySelectorAll('.flex, [style*="display: flex"], [style*="display:flex"]');
      flexContainers.forEach(container => {
        if (container instanceof HTMLElement) {
          const currentDirection = window.getComputedStyle(container).flexDirection;
          if (currentDirection === 'row') {
            container.style.flexDirection = 'row-reverse';
          }
        }
      });
      
      // Ajuster les marges et paddings internes
      const cardChildren = card.children;
      Array.from(cardChildren).forEach(child => {
        if (child instanceof HTMLElement) {
          // Échanger les marges et paddings gauche/droite
          const computedStyle = window.getComputedStyle(child);
          const paddingLeft = computedStyle.paddingLeft;
          const paddingRight = computedStyle.paddingRight;
          const marginLeft = computedStyle.marginLeft;
          const marginRight = computedStyle.marginRight;
          
          if (paddingLeft !== paddingRight) {
            child.style.paddingLeft = paddingRight;
            child.style.paddingRight = paddingLeft;
          }
          
          if (marginLeft !== marginRight) {
            child.style.marginLeft = marginRight;
            child.style.marginRight = marginLeft;
          }
        }
      });
    } else {
      card.classList.remove('rtl-card');
      if (card instanceof HTMLElement) {
        card.style.direction = '';
        card.style.textAlign = '';
      }
      
      // Restaurer l'ordre des éléments flexibles
      const flexContainers = card.querySelectorAll('.flex, [style*="display: flex"], [style*="display:flex"]');
      flexContainers.forEach(container => {
        if (container instanceof HTMLElement) {
          container.style.flexDirection = '';
        }
      });
      
      // Restaurer les marges et paddings
      const cardChildren = card.children;
      Array.from(cardChildren).forEach(child => {
        if (child instanceof HTMLElement) {
          child.style.paddingLeft = '';
          child.style.paddingRight = '';
          child.style.marginLeft = '';
          child.style.marginRight = '';
        }
      });
    }
  });
}
