"use client"

import { useState, useEffect } from "react"
import { useLanguage } from "@/components/language-provider"
import { Button } from "@/components/ui/button"
import { languages } from "@/lib/i18n/languages"
import { Globe, Search } from "lucide-react"
import { getTranslations } from "@/lib/i18n/translations"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"

export function LanguageSelector() {
  const { language, setLanguage } = useLanguage()
  const [open, setOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [filteredLanguages, setFilteredLanguages] = useState(languages)
  const t = getTranslations(language)

  const currentLanguage = languages.find((lang) => lang.code === language)

  useEffect(() => {
    if (searchQuery) {
      const filtered = languages.filter(
        (lang) =>
          lang.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          lang.code.toLowerCase().includes(searchQuery.toLowerCase())
      )
      setFilteredLanguages(filtered)
    } else {
      setFilteredLanguages(languages)
    }
  }, [searchQuery])

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Globe className="h-4 w-4" />
          <span className="hidden sm:inline">{currentLanguage?.name || "English"}</span>
          <span className="sm:hidden">{currentLanguage?.code.toUpperCase() || "EN"}</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[550px] max-h-[80vh] overflow-y-auto">
        <DialogTitle className="text-lg font-semibold mb-2">{t.selectLanguage || "Select Language"}</DialogTitle>
        <div className="relative mb-4">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t.searchLanguages}
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
          {filteredLanguages.map((lang) => (
            <Button
              key={lang.code}
              variant={language === lang.code ? "default" : "outline"}
              className="justify-start gap-2 h-auto py-2"
              onClick={() => {
                setLanguage(lang.code)
                setOpen(false)
              }}
            >
              <div className="flex flex-col items-start">
                <span className="font-medium">{lang.name}</span>
                <span className="text-xs text-muted-foreground">{lang.code.toUpperCase()}</span>
              </div>
            </Button>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  )
}

