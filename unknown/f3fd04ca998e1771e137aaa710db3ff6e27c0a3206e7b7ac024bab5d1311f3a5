import type { Config } from "tailwindcss"

const config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
    "*.{js,ts,jsx,tsx,mdx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    require("@tailwindcss/typography"),
    function ({ addVariant, matchUtilities, theme }: { 
      addVariant: (name: string, definition: string) => void,
      matchUtilities: Function,
      theme: Function
    }) {
      // Ajouter des variantes pour RTL
      addVariant('rtl', '[dir="rtl"] &');
      addVariant('ltr', '[dir="ltr"] &');
      
      // Variantes pour les classes plus spécifiques
      addVariant('rtl-start', '[dir="rtl"] &.start, [dir="rtl"]&:start');
      addVariant('rtl-end', '[dir="rtl"] &.end, [dir="rtl"]&:end');
      
      // Variantes pour les éléments enfants
      addVariant('rtl-children', '[dir="rtl"] & > *');
      addVariant('ltr-children', '[dir="ltr"] & > *');
      
      // Utilitaires spécifiques pour la direction
      matchUtilities(
        {
          'float-start': (value: string) => ({
            '[dir="ltr"] &': { float: 'left' },
            '[dir="rtl"] &': { float: 'right' },
          }),
          'float-end': (value: string) => ({
            '[dir="ltr"] &': { float: 'right' },
            '[dir="rtl"] &': { float: 'left' },
          }),
          'text-align-start': (value: string) => ({
            '[dir="ltr"] &': { textAlign: 'left' },
            '[dir="rtl"] &': { textAlign: 'right' },
          }),
          'text-align-end': (value: string) => ({
            '[dir="ltr"] &': { textAlign: 'right' },
            '[dir="rtl"] &': { textAlign: 'left' },
          }),
        },
        { values: theme('empty', {}) }
      );
    },
  ],
} satisfies Config

export default config

