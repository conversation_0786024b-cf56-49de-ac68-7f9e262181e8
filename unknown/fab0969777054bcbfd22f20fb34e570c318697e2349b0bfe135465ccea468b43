import en from './locales/en';
import fr from './locales/fr';
import es from './locales/es';
import de from './locales/de';
import it from './locales/it';
import pt from './locales/pt';
import nl from './locales/nl';
import pl from './locales/pl';
import uk from './locales/uk';
import tr from './locales/tr';
import ru from './locales/ru';
import ar from './locales/ar';
import he from './locales/he';
import fa from './locales/fa';
import hi from './locales/hi';
import bn from './locales/bn';
import te from './locales/te';
import ta from './locales/ta';
import mr from './locales/mr';
import gu from './locales/gu';
import kn from './locales/kn';
import ml from './locales/ml';
import pa from './locales/pa';
import ur from './locales/ur';
import id from './locales/id';
import ms from './locales/ms';
import th from './locales/th';
import vi from './locales/vi';
import km from './locales/km';
import my from './locales/my';
import zh from './locales/zh';
import ja from './locales/ja';
import ko from './locales/ko';
import el from './locales/el';
import bg from './locales/bg';
import cs from './locales/cs';
import sk from './locales/sk';
import hu from './locales/hu';
import ro from './locales/ro';
import hr from './locales/hr';
import sr from './locales/sr';
import bs from './locales/bs';
import sl from './locales/sl';
import mk from './locales/mk';
import et from './locales/et';
import lv from './locales/lv';
import lt from './locales/lt';
import da from './locales/da';
import fi from './locales/fi';
import nb from './locales/nb';
import sv from './locales/sv';
import ca from './locales/ca';
import gl from './locales/gl';
import eu from './locales/eu';
import af from './locales/af';
import sw from './locales/sw';
import am from './locales/am';
import ka from './locales/ka';
import hy from './locales/hy';
import az from './locales/az';
import uz from './locales/uz';
import kk from './locales/kk';
import tg from './locales/tg';
import tk from './locales/tk';
import ky from './locales/ky';

type Translations = {
  [key: string]: string
}

const allTranslations: { [lang: string]: Translations } = {
  en, fr, es, de, it, pt, nl, pl, uk, tr, ru, ar, he, fa, hi, bn, te, ta, mr, gu,
  kn, ml, pa, ur, id, ms, th, vi, km, my, zh, ja, ko, el, bg, cs, sk, hu, ro, hr,
  sr, bs, sl, mk, et, lv, lt, da, fi, nb, sv, ca, gl, eu, af, sw, am, ka, hy, az,
  uz, kk, tg, tk, ky
};


export function getTranslations(lang: string): Translations {
  const baseTranslations = allTranslations[lang] || allTranslations.en;
  // Retourne un Proxy pour gérer les clés manquantes et potentiellement le formatage
  return new Proxy(baseTranslations, {
    get(target, prop: string) {
      // Retourne la traduction ou la clé elle-même si non trouvée
      return target[prop] || prop;
    }
  });
}

export function formatTranslation(translation: string, params: Record<string, string | number>): string {
  let formatted = translation;
  for (const key in params) {
    // Utilise une regex globale pour remplacer toutes les occurrences
    const regex = new RegExp(`\\{${key}\\}`, 'g');
    formatted = formatted.replace(regex, String(params[key]));
  }
  return formatted;
}
