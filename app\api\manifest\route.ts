import { NextRequest, NextResponse } from 'next/server';
import { getTranslations } from '@/lib/i18n/translations';

export async function GET(request: NextRequest) {
  // Récupérer la langue depuis les headers ou query params
  const url = new URL(request.url);
  const lang = url.searchParams.get('lang') || 
               request.headers.get('accept-language')?.split(',')[0]?.split('-')[0] || 
               'en';

  // Obtenir les traductions pour cette langue
  const t = getTranslations(lang);

  // Générer le manifest avec les traductions
  const manifest = {
    name: t.homePageTitle,
    short_name: t.appName,
    description: t.homePageDescription,
    start_url: `/${lang}`,
    scope: "/",
    display: "standalone",
    orientation: "any",
    background_color: "#ffffff",
    theme_color: "#3b82f6",
    categories: ["productivity", "utilities", "tools"],
    lang: lang,
    dir: ["ar", "he", "fa", "ur"].includes(lang) ? "rtl" : "ltr",
    prefer_related_applications: false,
    icons: [
      {
        src: "/icons/icon-192x192.png",
        sizes: "192x192",
        type: "image/png",
        purpose: "any"
      },
      {
        src: "/icons/icon-192x192.png",
        sizes: "192x192",
        type: "image/png",
        purpose: "maskable"
      },
      {
        src: "/icons/icon-512x512.png",
        sizes: "512x512",
        type: "image/png",
        purpose: "any"
      },
      {
        src: "/icons/icon-512x512.png",
        sizes: "512x512",
        type: "image/png",
        purpose: "maskable"
      }
    ],
    screenshots: [
      {
        src: "/screenshots/screenshot-desktop.png",
        sizes: "1600x713",
        type: "image/png",
        form_factor: "wide",
        label: t.appScreenshotDesktop 
      },
      {
        src: "/screenshots/screenshot-mobile.png",
        sizes: "359x635",
        type: "image/png",
        form_factor: "narrow",
        label: t.appScreenshotMobile 
      }
    ]
  };

  return NextResponse.json(manifest, {
    headers: {
      'Content-Type': 'application/manifest+json',
      'Cache-Control': 'public, max-age=3600', // Cache pendant 1 heure
    },
  });
}
