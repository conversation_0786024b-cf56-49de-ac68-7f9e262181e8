"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useTimerAudio } from "@/hooks/use-robust-audio"
import { Clock, Play, Pause, RotateCcw, Volume2, VolumeX, Users } from "lucide-react"

export function MeetingTimerAudioTest() {
  const [testResults, setTestResults] = useState<string[]>([])
  const [isTestRunning, setIsTestRunning] = useState(false)
  const [simulatedMeetingTime, setSimulatedMeetingTime] = useState(0)
  const [simulatedInterval, setSimulatedInterval] = useState<NodeJS.Timeout | null>(null)
  
  const {
    playTimerSound,
    soundEnabled,
    toggleSound,
    isAudioReady,
    isPlaying,
    hasWakeLock,
    audioState,
    lastError
  } = useTimerAudio()

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`])
  }

  // Simuler un meeting timer
  const startMeetingSimulation = () => {
    setIsTestRunning(true)
    setSimulatedMeetingTime(0)
    addTestResult("🎬 Simulation de meeting timer démarrée")

    const interval = setInterval(() => {
      setSimulatedMeetingTime(prev => {
        const newTime = prev + 1
        
        // Simuler des événements de meeting à intervalles réguliers
        if (newTime % 30 === 0) { // Toutes les 30 secondes
          addTestResult(`⏰ Événement meeting à ${newTime}s`)
          if (soundEnabled) {
            playTimerSound("bell").then((success) => {
              if (success) {
                addTestResult(`✅ Son de meeting joué avec succès à ${newTime}s`)
              } else {
                addTestResult(`❌ Échec du son de meeting à ${newTime}s`)
              }
            }).catch((error) => {
              addTestResult(`❌ Erreur son meeting à ${newTime}s: ${error}`)
            })
          }
        }

        // Arrêter après 2 minutes
        if (newTime >= 120) {
          clearInterval(interval)
          setIsTestRunning(false)
          addTestResult("🏁 Simulation de meeting terminée")
        }

        return newTime
      })
    }, 1000)

    setSimulatedInterval(interval)
  }

  const stopMeetingSimulation = () => {
    if (simulatedInterval) {
      clearInterval(simulatedInterval)
      setSimulatedInterval(null)
    }
    setIsTestRunning(false)
    addTestResult("⏹️ Simulation de meeting arrêtée")
  }

  const testMeetingScenarios = async () => {
    addTestResult("🧪 Test des scénarios de meeting...")

    // Test 1: Son de début de meeting
    addTestResult("Test 1: Son de début de meeting")
    if (soundEnabled) {
      const success = await playTimerSound("bell")
      addTestResult(success ? "✅ Son de début OK" : "❌ Son de début échoué")
    }

    // Test 2: Son d'item d'agenda
    setTimeout(async () => {
      addTestResult("Test 2: Son d'item d'agenda")
      if (soundEnabled) {
        const success = await playTimerSound("notification")
        addTestResult(success ? "✅ Son d'item OK" : "❌ Son d'item échoué")
      }
    }, 1000)

    // Test 3: Son de fin de meeting
    setTimeout(async () => {
      addTestResult("Test 3: Son de fin de meeting")
      if (soundEnabled) {
        const success = await playTimerSound("alarm")
        addTestResult(success ? "✅ Son de fin OK" : "❌ Son de fin échoué")
      }
    }, 2000)
  }

  const clearResults = () => {
    setTestResults([])
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  // Nettoyer l'intervalle au démontage
  useEffect(() => {
    return () => {
      if (simulatedInterval) {
        clearInterval(simulatedInterval)
      }
    }
  }, [simulatedInterval])

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Test Audio Meeting Timer
        </CardTitle>
        <CardDescription>
          Test spécialisé pour le système audio robuste du meeting timer
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* État du système audio */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <Badge variant={isAudioReady ? "default" : "destructive"}>
              {isAudioReady ? "Prêt" : "Non prêt"}
            </Badge>
            <p className="text-xs text-muted-foreground mt-1">Audio Ready</p>
          </div>
          
          <div className="text-center">
            <Badge variant={isPlaying ? "default" : "secondary"}>
              {isPlaying ? "En cours" : "Arrêté"}
            </Badge>
            <p className="text-xs text-muted-foreground mt-1">Playing</p>
          </div>
          
          <div className="text-center">
            <Badge variant={hasWakeLock ? "default" : "secondary"}>
              {hasWakeLock ? "Actif" : "Inactif"}
            </Badge>
            <p className="text-xs text-muted-foreground mt-1">Wake Lock</p>
          </div>
          
          <div className="text-center">
            <Badge variant={audioState === "running" ? "default" : "destructive"}>
              {audioState}
            </Badge>
            <p className="text-xs text-muted-foreground mt-1">Context State</p>
          </div>
        </div>

        {/* Simulation de meeting */}
        <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-medium text-blue-900">Simulation Meeting Timer</h3>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-blue-600" />
              <span className="font-mono text-lg text-blue-800">
                {formatTime(simulatedMeetingTime)}
              </span>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button
              onClick={startMeetingSimulation}
              disabled={isTestRunning}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <Play className="h-4 w-4" />
              Démarrer Simulation
            </Button>
            <Button
              onClick={stopMeetingSimulation}
              disabled={!isTestRunning}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <Pause className="h-4 w-4" />
              Arrêter
            </Button>
          </div>
          
          <p className="text-xs text-blue-700 mt-2">
            La simulation joue un son toutes les 30 secondes pendant 2 minutes
          </p>
        </div>

        {/* Tests manuels */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="font-medium">Tests Manuels</h3>
            <Button
              variant="outline"
              size="sm"
              onClick={toggleSound}
              className="flex items-center gap-2"
            >
              {soundEnabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
              {soundEnabled ? "Désactiver" : "Activer"}
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            <Button
              onClick={testMeetingScenarios}
              disabled={!soundEnabled}
              variant="outline"
            >
              Test Scénarios Meeting
            </Button>
            <Button
              onClick={() => playTimerSound("bell")}
              disabled={!soundEnabled}
              variant="outline"
            >
              Test Son Immédiat
            </Button>
          </div>
        </div>

        {/* Instructions spécifiques meeting */}
        <div className="p-4 bg-green-50 rounded-lg border border-green-200">
          <h4 className="font-medium text-green-900 mb-2">Instructions Meeting Timer :</h4>
          <ol className="text-sm text-green-800 space-y-1">
            <li>1. Activez le son et testez les scénarios</li>
            <li>2. Démarrez la simulation de meeting</li>
            <li>3. <strong>Verrouillez l'écran mobile</strong> pendant la simulation</li>
            <li>4. Vérifiez que les sons se jouent toutes les 30s même écran verrouillé</li>
            <li>5. Déverrouillez et vérifiez les résultats</li>
          </ol>
        </div>

        {/* Erreurs */}
        {lastError && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-800">
              <strong>Erreur :</strong> {lastError}
            </p>
          </div>
        )}

        {/* Résultats des tests */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h3 className="font-medium">Résultats des tests</h3>
            <Button
              onClick={clearResults}
              variant="ghost"
              size="sm"
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Effacer
            </Button>
          </div>
          
          <div className="max-h-48 overflow-y-auto p-3 bg-muted rounded-lg">
            {testResults.length === 0 ? (
              <p className="text-sm text-muted-foreground">Aucun test effectué</p>
            ) : (
              <div className="space-y-1">
                {testResults.map((result, index) => (
                  <p key={index} className="text-xs font-mono">
                    {result}
                  </p>
                ))}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
