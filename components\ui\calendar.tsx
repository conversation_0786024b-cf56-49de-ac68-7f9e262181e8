"use client"

import * as React from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import {
  fr, es, enUS, de, it, pt, nl, pl, uk, tr, ru, ar, he,
  id, ms, th, vi, zhCN, ja, ko, el, bg, cs, sk, hu, ro,
  hr, sr, bs, sl, et, lv, lt, da, fi, nb, sv, ca, af
} from "date-fns/locale"
import {
  format,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  isSameMonth,
  isSameDay,
  startOfWeek,
  addDays,
  endOfWeek
} from "date-fns"
import { Button } from "./button"
import { cn } from "@/lib/utils"

interface CalendarProps {
  mode?: "single"
  selected?: Date | null
  onSelect?: (date: Date | null) => void
  className?: string
  required?: boolean
  initialFocus?: boolean
  lang?: string
}

export function Calendar({
  mode = "single",
  selected,
  onSelect,
  className,
  required,
  initialFocus,
  lang = "en"
}: CalendarProps) {
  const [currentDate, setCurrentDate] = React.useState(selected || new Date())
  
  // Check if the language is RTL
  const isRTL = React.useMemo(() => ['ar', 'he', 'fa', 'ur'].includes(lang), [lang])

  const getLocale = React.useCallback(() => {
    switch (lang) {
      case "fr": return fr;
      case "es": return es;
      case "de": return de;
      case "it": return it;
      case "pt": return pt;
      case "nl": return nl;
      case "pl": return pl;
      case "uk": return uk;
      case "tr": return tr;
      case "ru": return ru;
      case "ar": return ar;
      case "he": return he;
      case "id": return id;
      case "ms": return ms;
      case "th": return th;
      case "vi": return vi;
      case "zh": return zhCN;
      case "ja": return ja;
      case "ko": return ko;
      case "el": return el;
      case "bg": return bg;
      case "cs": return cs;
      case "sk": return sk;
      case "hu": return hu;
      case "ro": return ro;
      case "hr": return hr;
      case "sr": return sr;
      case "bs": return bs;
      case "sl": return sl;
      case "et": return et;
      case "lv": return lv;
      case "lt": return lt;
      case "da": return da;
      case "fi": return fi;
      case "nb": return nb;
      case "sv": return sv;
      case "ca": return ca;
      case "af": return af;
      default: return enUS;
    }
  }, [lang])

  // Get days for the current month view, including padding for complete weeks
  const getDaysInMonth = React.useCallback(() => {
    const start = startOfWeek(startOfMonth(currentDate))
    const end = endOfWeek(endOfMonth(currentDate))
    return eachDayOfInterval({ start, end })
  }, [currentDate])

  // Get weekday names
  const getDayNames = React.useCallback(() => {
    const locale = getLocale()
    const firstDayOfWeek = startOfWeek(new Date())
    return Array.from({ length: 7 }, (_, i) =>
      format(addDays(firstDayOfWeek, i), "EEEEEE", { locale })
    )
  }, [getLocale])

  const days = getDaysInMonth()
  const dayNames = getDayNames()
  const locale = getLocale()

  // Navigation handlers
  const previousMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1))
  }

  const nextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1))
  }

  // Handle date selection
  const handleDateSelect = (date: Date) => {
    if (required && !date) return
    onSelect?.(date)
  }

  return (
    <div className={cn("w-full p-3", className, isRTL && "rtl")}>
      {/* Calendar header */}
      <div className="flex items-center justify-between mb-4">
        <Button
          variant="outline"
          className="h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"
          onClick={isRTL ? nextMonth : previousMonth}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        <h2 className={cn("text-sm font-medium", isRTL && "text-right")}>
          {format(currentDate, "MMMM yyyy", { locale })}
        </h2>
        <Button
          variant="outline"
          className="h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"
          onClick={isRTL ? previousMonth : nextMonth}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Calendar grid */}
      <div className="grid grid-cols-7 gap-2 bg-muted/50 rounded-lg p-3">
        {/* Day headers */}
        {dayNames.map((day) => (
          <div
            key={day}
            className={cn(
              "h-9 text-[0.8rem] font-medium text-muted-foreground flex items-center justify-center",
              isRTL && "justify-end"
            )}
          >
            {day}
          </div>
        ))}

        {/* Calendar days */}
        {days.map((day, index) => {
          const isSelected = selected && isSameDay(day, selected)
          const isToday = isSameDay(day, new Date())
          const isCurrentMonth = isSameMonth(day, currentDate)

          return (
            <Button
              key={day.toISOString()}
              variant={isSelected ? "default" : "ghost"}
              className={cn(
                "h-9 w-full p-0 font-normal aria-selected:opacity-100 rounded-md",
                isToday && "bg-primary/10 text-primary font-semibold ring-2 ring-primary ring-offset-2",
                !isCurrentMonth && "text-muted-foreground opacity-50",
                isSelected && "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
                !isSelected && !isToday && "hover:bg-accent hover:text-accent-foreground",
                isRTL && "text-right"
              )}
              onClick={() => handleDateSelect(day)}
              tabIndex={initialFocus && index === 0 ? 0 : -1}
              disabled={!isCurrentMonth && required}
            >
              <time dateTime={format(day, "yyyy-MM-dd")} className={cn(isRTL && "flex justify-end px-2")}>
                {format(day, "d")}
              </time>
            </Button>
          )
        })}
      </div>
    </div>
  )
}
