"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { getTranslations } from "@/lib/i18n/translations"
import { TimerStopwatch } from "@/components/timer-stopwatch"
import { CountdownTimer } from "@/components/countdown-timer"
import { TimerIcon, Clock } from "lucide-react"

interface TimerDashboardProps {
  lang?: string
}

export function TimerDashboard({ lang = "fr" }: TimerDashboardProps) {
  const t = getTranslations(lang)
  const [activeTab, setActiveTab] = useState("timer")

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Section title - H2 */}
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold mb-2">{t.timeTools}</h2>
        <p className="text-muted-foreground">{t.timeToolsDesc}</p>
      </div>

      <Tabs defaultValue="timer" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-2 w-full mb-6">
          <TabsTrigger value="timer" className="flex items-center gap-2">
            <TimerIcon className="h-4 w-4" />
            {t.timerStopwatch}
          </TabsTrigger>
          <TabsTrigger value="countdown" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            {t.countdown}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="timer" className="mt-0">
          <TimerStopwatch lang={lang} />
        </TabsContent>

        <TabsContent value="countdown" className="mt-0">
          <CountdownTimer lang={lang} />
        </TabsContent>
      </Tabs>
    </div>
  )
}

