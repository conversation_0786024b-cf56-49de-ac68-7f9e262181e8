// Simple storage utility to save data to localStorage
export const saveToLocalStorage = <T>(key: string, data: T): void => {
  try {
    if (typeof window !== 'undefined') {
      const serializedData = JSON.stringify(data);
      localStorage.setItem(key, serializedData);
    }
  } catch (error) {
    console.error('Error saving to localStorage:', error);
  }
};

// Get data from localStorage
export const getFromLocalStorage = <T>(key: string, defaultValue: T): T => {
  try {
    if (typeof window !== 'undefined') {
      const serializedData = localStorage.getItem(key);
      if (serializedData === null) {
        return defaultValue;
      }
      return JSON.parse(serializedData) as T;
    }
    return defaultValue;
  } catch (error) {
    console.error('Error getting from localStorage:', error);
    return defaultValue;
  }
};

// Remove data from localStorage
export const removeFromLocalStorage = (key: string): void => {
  try {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(key);
    }
  } catch (error) {
    console.error('Error removing from localStorage:', error);
  }
};

// Storage keys
export const STORAGE_KEYS = {
  TODO_LIST: 'timetools_todo_list',
  TIME_ENTRIES: 'timetools_time_entries',
  ACTIVE_ENTRY: 'timetools_active_entry',
  BILLING_ENTRIES: 'timetools_billing_entries',
  WORKOUT_TEMPLATES: 'timetools_workout_templates',
  EXERCISE_TEMPLATES: 'timetools_exercise_templates',
  INTERVALS: 'timetools_intervals',
  WORLD_CLOCK_TIMEZONES: 'timetools_world_clock_timezones',
  POMODORO_SETTINGS: 'timetools_pomodoro_settings',
  MEETING_TEMPLATES: 'timetools_meeting_templates',
  MEETING_TEMPLATE: 'timetools_meeting_template',
};

