"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { useTimerAudio } from "@/hooks/use-robust-audio"

export function SimpleAudioTest() {
  const [testLog, setTestLog] = useState<string[]>([])
  const { playTimerSound, soundEnabled, toggleSound } = useTimerAudio()

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setTestLog(prev => [...prev, `${timestamp}: ${message}`])
    console.log(`[SimpleAudioTest] ${message}`)
  }

  const testBasicSound = async () => {
    addLog("Testing basic sound...")
    try {
      const result = await playTimerSound("bell")
      addLog(`Basic sound result: ${result ? "SUCCESS" : "FAILED"}`)
    } catch (error) {
      addLog(`Basic sound error: ${error}`)
    }
  }

  const testMeetingTimerScenario = async () => {
    addLog("Testing meeting timer scenario...")
    
    // Simuler ce qui se passe dans le meeting timer
    if (soundEnabled) {
      try {
        const result = await playTimerSound("bell")
        if (result) {
          addLog("Meeting timer sound played successfully")
        } else {
          addLog("Failed to play meeting timer sound")
        }
      } catch (error) {
        addLog(`Meeting timer sound error: ${error}`)
      }
    } else {
      addLog("Sound disabled, skipping meeting timer test")
    }
  }

  const testExerciseTimerScenario = async () => {
    addLog("Testing exercise timer scenario...")
    
    // Simuler ce qui se passe dans exercise-templates
    if (soundEnabled) {
      try {
        const result = await playTimerSound("bell")
        if (result) {
          addLog("Exercise rest timer completion sound played successfully")
        } else {
          addLog("Failed to play exercise rest timer completion sound")
        }
      } catch (error) {
        addLog(`Exercise timer sound error: ${error}`)
      }
    } else {
      addLog("Sound disabled, skipping exercise timer test")
    }
  }

  const clearLog = () => {
    setTestLog([])
  }

  return (
    <div className="p-4 border rounded-lg bg-white shadow">
      <h3 className="font-bold mb-4">Simple Audio Test</h3>
      
      <div className="space-y-2 mb-4">
        <p>Sound Enabled: {soundEnabled ? "✅ YES" : "❌ NO"}</p>
        <p>playTimerSound type: {typeof playTimerSound}</p>
      </div>

      <div className="flex flex-wrap gap-2 mb-4">
        <Button onClick={toggleSound} variant="outline" size="sm">
          {soundEnabled ? "Disable" : "Enable"} Sound
        </Button>
        <Button onClick={testBasicSound} variant="outline" size="sm">
          Test Basic Sound
        </Button>
        <Button onClick={testMeetingTimerScenario} variant="outline" size="sm">
          Test Meeting Timer
        </Button>
        <Button onClick={testExerciseTimerScenario} variant="outline" size="sm">
          Test Exercise Timer
        </Button>
        <Button onClick={clearLog} variant="ghost" size="sm">
          Clear Log
        </Button>
      </div>

      <div className="bg-gray-50 p-3 rounded max-h-40 overflow-y-auto">
        <h4 className="font-medium mb-2">Test Log:</h4>
        {testLog.length === 0 ? (
          <p className="text-gray-500 text-sm">No tests run yet</p>
        ) : (
          <div className="space-y-1">
            {testLog.map((log, index) => (
              <p key={index} className="text-xs font-mono">
                {log}
              </p>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
