import { WorldClock } from "@/components/world-clock"
import { ToolSeoContent } from "@/components/tool-seo-content"
import { PageBreadcrumbs } from "@/components/page-breadcrumbs"
import { getTranslations } from "@/lib/i18n/translations"
import { Metadata } from "next"
import { generatePageMetadata } from "@/lib/i18n/metadata-utils"

export async function generateMetadata({ params }: { params: Promise<{ lang: string }> }): Promise<Metadata> {
  const { lang } = await params;
  // Utiliser la fonction utilitaire pour générer les métadonnées avec les routes traduites
  return generatePageMetadata(lang, "world-clock");
}

export default async function WorldClockPage({ params }: { params: Promise<{ lang: string }> }) {
  const { lang } = await params;
  const t = getTranslations(lang);

  return (
    <div className="container mx-auto py-8">
      <PageBreadcrumbs lang={lang} currentPage="world-clock" />
      <h1 className="text-2xl md:text-3xl font-bold mb-2 text-center">{t.worldClock}</h1>
      <p className="text-center text-muted-foreground mb-8">{t.worldClockDescription}</p>
      <WorldClock lang={lang} />

      {/* Add SEO content at the bottom of the page */}
      <ToolSeoContent lang={lang} toolType="worldClock" />
    </div>
  );
}
