"use client"

import type React from "react"
import { createContext, useContext, useState } from "react"
import { useRobustAudio, type SoundType } from "@/hooks/use-robust-audio"

interface SoundContextType {
  playSound: (type: SoundType) => Promise<boolean>
  isSoundEnabled: boolean
  toggleSound: () => void
  isAudioReady: boolean
  audioState: string
  hasWakeLock: boolean
  lastError: string | null
  forceUnlock: () => Promise<boolean>
}

const SoundContext = createContext<SoundContextType | null>(null)

export function SoundProvider({ children }: { children: React.ReactNode }) {
  const [isSoundEnabled, setIsSoundEnabled] = useState(true)

  // Utiliser le nouveau système audio robuste
  const robustAudio = useRobustAudio()

  // Fonction pour jouer un son avec le nouveau système
  const playSound = async (type: SoundType): Promise<boolean> => {
    if (!isSoundEnabled) {
      console.log(`[SoundProvider] Sound disabled, skipping ${type}`)
      return false
    }

    console.log(`[SoundProvider] Playing ${type} sound with robust audio`)
    return await robustAudio.playSound(type)
  }

  const toggleSound = () => {
    setIsSoundEnabled(!isSoundEnabled)
    console.log(`[SoundProvider] Sound ${!isSoundEnabled ? 'enabled' : 'disabled'}`)
  }

  return (
    <SoundContext.Provider
      value={{
        playSound,
        isSoundEnabled,
        toggleSound,
        isAudioReady: robustAudio.isAudioReady,
        audioState: robustAudio.audioState,
        hasWakeLock: robustAudio.hasWakeLock,
        lastError: robustAudio.lastError,
        forceUnlock: robustAudio.forceUnlock
      }}
    >
      {children}
    </SoundContext.Provider>
  )
}

export function useSound() {
  const context = useContext(SoundContext)
  if (!context) {
    throw new Error("useSound must be used within a SoundProvider")
  }
  return context
}

