"use client"

import type React from "react"
import { createContext, useContext, useState } from "react"
import { useTimerAudio, type SoundType } from "@/hooks/use-robust-audio"

interface SoundContextType {
  playSound: (type: SoundType) => Promise<boolean>
  isSoundEnabled: boolean
  toggleSound: () => void
  isAudioReady: boolean
  audioState: string
  hasWakeLock: boolean
  lastError: string | null
  forceUnlock: () => Promise<boolean>
}

const SoundContext = createContext<SoundContextType | null>(null)

export function SoundProvider({ children }: { children: React.ReactNode }) {
  // Utiliser le hook timer audio pour éviter les instances multiples
  const timerAudio = useTimerAudio()

  // Fonction pour jouer un son avec le nouveau système
  const playSound = async (type: SoundType): Promise<boolean> => {
    console.log(`[SoundProvider] Playing ${type} sound with timer audio`)
    return await timerAudio.playTimerSound(type)
  }

  const toggleSound = () => {
    timerAudio.toggleSound()
    console.log(`[SoundProvider] Sound toggled`)
  }

  return (
    <SoundContext.Provider
      value={{
        playSound,
        isSoundEnabled: timerAudio.soundEnabled,
        toggleSound,
        isAudioReady: timerAudio.isAudioReady,
        audioState: timerAudio.audioState,
        hasWakeLock: timerAudio.hasWakeLock,
        lastError: timerAudio.lastError,
        forceUnlock: timerAudio.forceUnlock
      }}
    >
      {children}
    </SoundContext.Provider>
  )
}

export function useSound() {
  const context = useContext(SoundContext)
  if (!context) {
    throw new Error("useSound must be used within a SoundProvider")
  }
  return context
}

