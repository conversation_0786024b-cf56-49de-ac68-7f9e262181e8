"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect, useRef } from "react"

type SoundType = "bell" | "alarm" | "notification"

interface SoundContextType {
  playSound: (type: SoundType) => void
  isSoundEnabled: boolean
  toggleSound: () => void
}

const SoundContext = createContext<SoundContextType | null>(null)

export function SoundProvider({ children }: { children: React.ReactNode }) {
  const [isSoundEnabled, setIsSoundEnabled] = useState(true)
  const audioContextRef = useRef<AudioContext | null>(null)

  // Initialiser le contexte audio
  const initAudioContext = () => {
    if (typeof window === "undefined" || audioContextRef.current) return

    try {
      const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext
      if (AudioContextClass) {
        audioContextRef.current = new AudioContextClass()
        console.log("AudioContext initialized successfully")
      } else {
        console.warn("AudioContext not supported in this browser")
      }
    } catch (error) {
      console.error("Failed to initialize AudioContext:", error)
    }
  }

  // Initialiser le contexte audio
  useEffect(() => {
    initAudioContext()

    // Nettoyer le contexte audio lors du démontage
    return () => {
      if (audioContextRef.current) {
        try {
          if (audioContextRef.current.state !== "closed") {
            audioContextRef.current.close()
          }
        } catch (error) {
          console.error("Error closing AudioContext:", error)
        }
      }
    }
  }, [])

  // Débloquer l'audio sur interaction utilisateur
  useEffect(() => {
    if (typeof window === "undefined") return

    const unlockAudio = () => {
      // Initialiser le contexte audio si ce n'est pas déjà fait
      if (!audioContextRef.current) {
        initAudioContext()
      }

      // Débloquer le contexte audio
      if (audioContextRef.current && audioContextRef.current.state === "suspended") {
        audioContextRef.current.resume().catch((err) => {
          console.error("Failed to resume AudioContext:", err)
        })
      }

      // Jouer un son silencieux pour débloquer l'audio sur iOS
      if (audioContextRef.current) {
        try {
          const oscillator = audioContextRef.current.createOscillator()
          const gainNode = audioContextRef.current.createGain()

          gainNode.gain.value = 0.001 // Presque silencieux
          oscillator.connect(gainNode)
          gainNode.connect(audioContextRef.current.destination)

          oscillator.start(0)
          oscillator.stop(0.001)
        } catch (e) {
          console.warn("Failed to play silent sound:", e)
        }
      }
    }

    // Ajouter des écouteurs d'événements pour débloquer l'audio
    const unlockEvents = ["touchstart", "touchend", "mousedown", "keydown", "click"]
    unlockEvents.forEach((event) => {
      document.addEventListener(event, unlockAudio, { once: true })
    })

    // Essayer de débloquer immédiatement (pour les navigateurs de bureau)
    unlockAudio()

    return () => {
      unlockEvents.forEach((event) => {
        document.removeEventListener(event, unlockAudio)
      })
    }
  }, [])

  // Fonction pour générer un son de type "bell"
  const generateBellSound = () => {
    if (!audioContextRef.current) return

    try {
      const ctx = audioContextRef.current
      const now = ctx.currentTime

      // Créer les nœuds audio
      const oscillator = ctx.createOscillator()
      const gainNode = ctx.createGain()

      // Configurer l'oscillateur
      oscillator.type = "sine"
      oscillator.frequency.value = 600

      // Configurer l'enveloppe de volume
      gainNode.gain.setValueAtTime(0, now)
      gainNode.gain.linearRampToValueAtTime(0.3, now + 0.02)
      gainNode.gain.linearRampToValueAtTime(0.2, now + 0.1)
      gainNode.gain.linearRampToValueAtTime(0, now + 0.3)

      // Connecter les nœuds
      oscillator.connect(gainNode)
      gainNode.connect(ctx.destination)

      // Jouer le son
      oscillator.start(now)
      oscillator.stop(now + 0.3)

      // Nettoyer les ressources
      setTimeout(() => {
        oscillator.disconnect()
        gainNode.disconnect()
      }, 300)

      console.log("Bell sound played")
    } catch (error) {
      console.error("Error generating bell sound:", error)
    }
  }

  // Fonction pour générer un son de type "alarm"
  const generateAlarmSound = () => {
    if (!audioContextRef.current) return

    try {
      const ctx = audioContextRef.current
      const now = ctx.currentTime

      // Créer les nœuds audio
      const oscillator = ctx.createOscillator()
      const gainNode = ctx.createGain()

      // Configurer l'oscillateur
      oscillator.type = "sawtooth"
      oscillator.frequency.value = 800

      // Créer un effet de pulsation pour l'alarme
      gainNode.gain.setValueAtTime(0, now)
      gainNode.gain.linearRampToValueAtTime(0.4, now + 0.05)
      gainNode.gain.linearRampToValueAtTime(0.1, now + 0.25)
      gainNode.gain.linearRampToValueAtTime(0.4, now + 0.5)
      gainNode.gain.linearRampToValueAtTime(0, now + 0.6)

      // Connecter les nœuds
      oscillator.connect(gainNode)
      gainNode.connect(ctx.destination)

      // Jouer le son
      oscillator.start(now)
      oscillator.stop(now + 0.6)

      // Nettoyer les ressources
      setTimeout(() => {
        oscillator.disconnect()
        gainNode.disconnect()
      }, 600)

      console.log("Alarm sound played")
    } catch (error) {
      console.error("Error generating alarm sound:", error)
    }
  }

  // Fonction pour générer un son de type "notification"
  const generateNotificationSound = () => {
    if (!audioContextRef.current) return

    try {
      const ctx = audioContextRef.current
      const now = ctx.currentTime

      // Créer les nœuds audio
      const oscillator = ctx.createOscillator()
      const gainNode = ctx.createGain()

      // Configurer l'oscillateur avec un glissement de fréquence
      oscillator.type = "sine"
      oscillator.frequency.setValueAtTime(1200, now)
      oscillator.frequency.exponentialRampToValueAtTime(500, now + 0.2)

      // Configurer l'enveloppe de volume
      gainNode.gain.setValueAtTime(0, now)
      gainNode.gain.linearRampToValueAtTime(0.2, now + 0.02)
      gainNode.gain.linearRampToValueAtTime(0.2, now + 0.1)
      gainNode.gain.linearRampToValueAtTime(0, now + 0.2)

      // Connecter les nœuds
      oscillator.connect(gainNode)
      gainNode.connect(ctx.destination)

      // Jouer le son
      oscillator.start(now)
      oscillator.stop(now + 0.2)

      // Nettoyer les ressources
      setTimeout(() => {
        oscillator.disconnect()
        gainNode.disconnect()
      }, 200)

      console.log("Notification sound played")
    } catch (error) {
      console.error("Error generating notification sound:", error)
    }
  }

  // Fonction pour jouer un son
  const playSound = (type: SoundType) => {
    if (!isSoundEnabled) return

    console.log(`Playing ${type} sound`)

    // Essayer de jouer le fichier audio pour le son de cloche
    if (type === "bell") {
      try {
        // Créer un nouvel élément audio à chaque fois pour éviter les problèmes de lecture multiple
        const audio = new Audio("/sound/bell.mp3")

        // Gérer les erreurs de lecture
        audio.onerror = (e) => {
          console.error("Error playing bell sound file, falling back to synthetic sound:", e)
          generateBellSound()
        }

        // Jouer le son
        const playPromise = audio.play()
        if (playPromise !== undefined) {
          playPromise.catch((error) => {
            console.error("Error playing bell sound:", error)
            generateBellSound()
          })
        }

        return
      } catch (error) {
        console.error("Error setting up bell sound:", error)
        // Continuer avec le son synthétique en cas d'erreur
      }
    }

    // Utiliser les sons synthétiques
    switch (type) {
      case "bell":
        generateBellSound()
        break
      case "alarm":
        generateAlarmSound()
        break
      case "notification":
        generateNotificationSound()
        break
    }
  }

  const toggleSound = () => {
    setIsSoundEnabled(!isSoundEnabled)
  }

  return <SoundContext.Provider value={{ playSound, isSoundEnabled, toggleSound }}>{children}</SoundContext.Provider>
}

export function useSound() {
  const context = useContext(SoundContext)
  if (!context) {
    throw new Error("useSound must be used within a SoundProvider")
  }
  return context
}

