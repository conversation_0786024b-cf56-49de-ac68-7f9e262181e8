import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import { languages } from "./lib/i18n/languages"
import { getTranslatedRoute, getOriginalRoute } from "./lib/i18n/route-translations"

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname

  // Exclure explicitement les fichiers statiques et les dossiers spécifiques
  if (
    pathname.startsWith("/_next") ||
    pathname.startsWith("/api") ||
    pathname.startsWith("/sounds") ||
    pathname === "/terms" ||
    pathname === "/privacy-policy" ||
    pathname.includes(".") // Exclure tous les fichiers avec extension
  ) {
    return NextResponse.next()
  }

  // Vérifier si le chemin commence par une langue valide
  const pathnameIsMissingLocale = languages.every(
    (lang) => !pathname.startsWith(`/${lang.code}/`) && pathname !== `/${lang.code}`,
  )

  // Rediriger vers la langue par défaut si aucune langue n'est spécifiée
  if (pathnameIsMissingLocale) {
    // Déterminer la langue préférée de l'utilisateur à partir de l'en-tête Accept-Language
    const acceptLanguage = request.headers.get("accept-language") || ""
    const preferredLanguage =
      acceptLanguage
        .split(",")
        .map((lang) => lang.split(";")[0].trim().substring(0, 2))
        .find((lang) => languages.some((l) => l.code === lang)) || "en"

    return NextResponse.redirect(new URL(`/${preferredLanguage}${pathname === "/" ? "" : pathname}`, request.url))
  }

  // Vérifier si l'URL contient une route traduite et la rediriger en interne vers la route originale
  const segments = pathname.split("/")
  if (segments.length > 2) {
    const langCode = segments[1]
    const routePart = segments[2]

    if (routePart) {
      try {
        // Décoder la route pour gérer les caractères spéciaux
        const decodedRoutePart = decodeURIComponent(routePart)

        // Liste des routes originales disponibles
        const availableRoutes = [
          'timer', 'countdown', 'todo', 'time-tracking', 'world-clock',
          'intervals', 'pomodoro', 'meeting-timer', 'time-billing',
          'workout-intervals', 'exercise-templates', 'sitemap'
        ];

        // Vérifier si c'est une route originale qui doit être redirigée vers sa traduction
        if (availableRoutes.includes(decodedRoutePart)) {
          // Obtenir la route traduite pour cette langue
          const translatedRoute = getTranslatedRoute(langCode, decodedRoutePart);

          // Si la route traduite est différente de la route originale, rediriger
          if (translatedRoute !== decodedRoutePart) {
            const newUrl = new URL(request.url);
            newUrl.pathname = `/${langCode}/${translatedRoute}`;

            // Redirection 301 vers la route traduite
            return NextResponse.redirect(newUrl, 301);
          }

          // Si la route traduite est identique (comme en allemand "sitemap" = "sitemap"), continuer
          return NextResponse.next();
        }

        // Vérifier si c'est une route traduite qui doit être rewritée vers la route originale
        const originalRoute = getOriginalRoute(langCode, decodedRoutePart);

        if (originalRoute !== decodedRoutePart && availableRoutes.includes(originalRoute)) {
          // Créer une nouvelle URL avec la route originale pour le rewrite interne
          const newUrl = new URL(request.url);
          newUrl.pathname = `/${langCode}/${originalRoute}`;

          // Utiliser rewrite pour charger le contenu de la route originale tout en gardant l'URL traduite
          return NextResponse.rewrite(newUrl);
        }
      } catch (error) {
        console.error("Error in middleware:", error);
      }
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    // Exclure les fichiers statiques et les API routes
    "/((?!api|_next/static|_next/image|favicon.ico|sounds|.*\\.png$|.*\\.jpg$|.*\\.svg$|.*\\.mp3$).*)",
  ],
}