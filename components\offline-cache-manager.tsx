"use client";

import { useEffect, useRef } from "react";
import { usePathname } from "next/navigation";

export function OfflineCacheManager() {
  const pathname = usePathname();
  const previousLangRef = useRef<string | null>(null);

  useEffect(() => {
    // Enhanced offline cache manager with on-demand caching
    if (typeof window === 'undefined') return;

    // Service worker registration and cache management
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistration()
        .then((registration) => {
          if (registration && registration.active) {
            // Extract language from current path
            const pathSegments = pathname.split('/').filter(Boolean);
            const currentLang = pathSegments[0];

            // Check if this is a valid language code (basic validation)
            const isValidLanguage = currentLang && currentLang.length === 2 && /^[a-z]{2}$/.test(currentLang);

            // Check if PWA is installed before triggering any caching
            const isStandalone = window.matchMedia("(display-mode: standalone)").matches;
            const isInWebAppiOS = (window.navigator as any).standalone === true;
            const isPWAInstalled = isStandalone || isInWebAppiOS;

            // For testing: force PWA detection in development
            const isDevelopment = window.location.hostname === 'localhost';
            const forcePWA = isDevelopment || isPWAInstalled;

            console.log(`[Cache Manager] PWA Detection - Standalone: ${isStandalone}, iOS: ${isInWebAppiOS}, Development: ${isDevelopment}, Final: ${forcePWA}`);

            if (forcePWA && isValidLanguage) {
              console.log(`[Cache Manager] PWA installed and valid language: ${currentLang}`);

              // Ensure service worker knows PWA is installed
              registration.active?.postMessage({
                type: 'PWA_INSTALLED'
              });

              // Check if language has changed
              const previousLang = previousLangRef.current;
              if (previousLang && previousLang !== currentLang) {
                console.log(`[Cache Manager] Language changed from ${previousLang} to ${currentLang}`);
                // Notify service worker about language change
                registration.active?.postMessage({
                  type: 'LANGUAGE_CHANGED',
                  newLang: currentLang,
                  oldLang: previousLang
                });
              } else if (!previousLang) {
                console.log(`[Cache Manager] First visit, caching current language: ${currentLang}`);
                // First time visiting, cache current language
                registration.active?.postMessage({
                  type: 'CACHE_LANGUAGE',
                  lang: currentLang
                });
              } else {
                console.log(`[Cache Manager] Same language ${currentLang}, no action needed`);
              }

              // Update previous language reference
              previousLangRef.current = currentLang;
            } else {
              console.log(`[Cache Manager] Conditions not met - PWA: ${forcePWA}, Valid Lang: ${isValidLanguage}, Current Lang: ${currentLang}`);
            }
          }
        })
        .catch((error) => {
          console.warn('[Cache Manager] Service Worker check failed:', error);
        });
    }
  }, [pathname]);

  // For testing and debugging
  const forceCacheLanguage = (lang: string) => {
    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({
        type: 'FORCE_CACHE_LANGUAGE',
        lang: lang
      });
      console.log(`[Cache Manager] Sent FORCE_CACHE_LANGUAGE message for ${lang}`);
    } else {
      console.warn('[Cache Manager] Service worker not active, cannot force cache.');
    }
  };

  const triggerCacheDiagnosis = () => {
    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({
        type: 'DIAGNOSE_CACHE'
      });
      console.log('[Cache Manager] Sent DIAGNOSE_CACHE message');
    } else {
      console.warn('[Cache Manager] Service worker not active, cannot diagnose cache.');
    }
  };

  // You could add UI elements to trigger these during development, e.g.:
  // <button onClick={() => forceCacheLanguage('fr')}>Force Cache FR</button>
  // <button onClick={() => triggerCacheDiagnosis()}>Diagnose Cache</button>
  // Or, trigger these from the browser console:
  // offlineCacheManagerRef.current.forceCacheLanguage('fr');
  // offlineCacheManagerRef.current.triggerCacheDiagnosis();
  // (if you expose these functions using useRef and useImperativeHandle)

  // Add useRef and useImperativeHandle if you want to trigger these from the console or other components:
  // const offlineCacheManagerRef = useRef(null);
  // useImperativeHandle(offlineCacheManagerRef, () => ({
  //   forceCacheLanguage,
  //   triggerCacheDiagnosis,
  // }));
  return null; // This component doesn't render anything
}
