import { AudioTestPanel } from "@/components/audio-test-panel"
import { MeetingTimerAudioTest } from "@/components/meeting-timer-audio-test"
import { getTranslations } from "@/lib/i18n/translations"
import type { Language } from "@/lib/i18n/types"

interface TestAudioPageProps {
  params: {
    lang: Language
  }
}

export default function TestAudioPage({ params }: TestAudioPageProps) {
  const t = getTranslations(params.lang)

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Test du Système Audio Robuste</h1>
          <p className="text-muted-foreground">
            Cette page permet de tester le nouveau système de gestion audio robuste 
            pour les minuteries sur mobile.
          </p>
        </div>
        
        <AudioTestPanel />
        
        <MeetingTimerAudioTest />
        
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h2 className="font-semibold text-yellow-900 mb-3">
            🔧 Système Audio Robuste - Fonctionnalités
          </h2>
          <ul className="text-sm text-yellow-800 space-y-2">
            <li>• <strong>Gestion avancée du contexte audio</strong> - Déverrouillage automatique et gestion des états</li>
            <li>• <strong>Wake Lock API</strong> - Maintient l'activité audio quand l'écran est verrouillé</li>
            <li>• <strong>Retry automatique</strong> - Plusieurs tentatives en cas d'échec</li>
            <li>• <strong>Préchargement des sons</strong> - Sons prêts à être joués instantanément</li>
            <li>• <strong>Fallback synthétique</strong> - Sons générés si les fichiers échouent</li>
            <li>• <strong>Gestion de la visibilité</strong> - Adaptation selon l'état de la page</li>
            <li>• <strong>Volume optimisé mobile</strong> - Volume augmenté pour les appareils mobiles</li>
            <li>• <strong>Compatibilité iOS/Android</strong> - Gestion des restrictions spécifiques</li>
          </ul>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h2 className="font-semibold text-blue-900 mb-3">
            📱 Instructions de Test Mobile
          </h2>
          <div className="text-sm text-blue-800 space-y-3">
            <div>
              <h3 className="font-medium">Test 1: Écran verrouillé</h3>
              <ol className="list-decimal list-inside space-y-1 ml-4">
                <li>Activez le son et testez un type de son</li>
                <li>Verrouillez l'écran de votre mobile</li>
                <li>Attendez 5-10 secondes</li>
                <li>Testez un son (le son devrait se jouer même écran verrouillé)</li>
                <li>Déverrouillez et vérifiez les résultats</li>
              </ol>
            </div>
            
            <div>
              <h3 className="font-medium">Test 2: Onglet en arrière-plan</h3>
              <ol className="list-decimal list-inside space-y-1 ml-4">
                <li>Ouvrez un autre onglet ou une autre app</li>
                <li>Revenez sur cette page</li>
                <li>Vérifiez que l'état "Était cachée" apparaît</li>
                <li>Testez les sons après le retour</li>
              </ol>
            </div>

            <div>
              <h3 className="font-medium">Test 3: Wake Lock</h3>
              <ol className="list-decimal list-inside space-y-1 ml-4">
                <li>Cliquez sur "Test Wake Lock"</li>
                <li>Vérifiez que le Wake Lock devient "Actif"</li>
                <li>L'écran ne devrait pas se mettre en veille pendant 3 secondes</li>
              </ol>
            </div>

            <div>
              <h3 className="font-medium">Test 4: Meeting Timer</h3>
              <ol className="list-decimal list-inside space-y-1 ml-4">
                <li>Utilisez le panneau "Test Audio Meeting Timer"</li>
                <li>Démarrez la simulation de meeting</li>
                <li>Verrouillez l'écran mobile pendant la simulation</li>
                <li>Vérifiez que les sons se jouent toutes les 30 secondes même écran verrouillé</li>
                <li>Déverrouillez et vérifiez les résultats</li>
              </ol>
            </div>
          </div>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
          <h2 className="font-semibold text-green-900 mb-3">
            ✅ Résultats Attendus
          </h2>
          <ul className="text-sm text-green-800 space-y-2">
            <li>• Les sons doivent se jouer même quand l'écran est verrouillé</li>
            <li>• Le Wake Lock doit s'activer automatiquement quand nécessaire</li>
            <li>• Les erreurs doivent être gérées avec des tentatives automatiques</li>
            <li>• Le contexte audio doit se rétablir après suspension</li>
            <li>• Les sons de fallback doivent fonctionner si les fichiers échouent</li>
            <li>• Les sons de meeting doivent se jouer régulièrement en arrière-plan</li>
          </ul>
        </div>

        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <h2 className="font-semibold text-red-900 mb-3">
            🚨 Points Importants
          </h2>
          <ul className="text-sm text-red-800 space-y-2">
            <li>• <strong>HTTPS requis</strong> - Le Wake Lock nécessite HTTPS en production</li>
            <li>• <strong>Interaction utilisateur</strong> - Un clic est requis avant le premier son</li>
            <li>• <strong>Permissions</strong> - Le navigateur peut demander des permissions</li>
            <li>• <strong>iOS Safari</strong> - Peut avoir des restrictions supplémentaires</li>
            <li>• <strong>Mode silencieux</strong> - Vérifiez que le mode silencieux est désactivé</li>
          </ul>
        </div>

        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
          <h2 className="font-semibold text-gray-900 mb-3">
            🔧 Composants Intégrés
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-700">
            <div>
              <h4 className="font-medium mb-2">Minuteries avec Audio Robuste :</h4>
              <ul className="space-y-1">
                <li>✅ Compte à rebours</li>
                <li>✅ Intervalles</li>
                <li>✅ Pomodoro</li>
                <li>✅ Intervalles d'entraînement</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Autres Composants :</h4>
              <ul className="space-y-1">
                <li>✅ Templates d'exercices</li>
                <li>✅ Meeting timer</li>
                <li>✅ Chronomètre</li>
                <li>✅ Tous les timers</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
