"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Volume2, VolumeX } from "lucide-react"
import { useSound } from "@/components/sound-provider"
import { useLanguage } from "@/components/language-provider"
import { getTranslations } from "@/lib/i18n/translations"

export function SoundToggle() {
  const { isSoundEnabled, toggleSound } = useSound()
  const { language } = useLanguage()
  const t = getTranslations(language)

  return (
    <Button variant="outline" size="icon" onClick={toggleSound} title={isSoundEnabled ? t.soundOff : t.soundOn}>
      {isSoundEnabled ? <Volume2 className="h-[1.2rem] w-[1.2rem]" /> : <VolumeX className="h-[1.2rem] w-[1.2rem]" />}
      <span className="sr-only">{isSoundEnabled ? t.soundOff : t.soundOn}</span>
    </Button>
  )
}

