"use client"

import { Label } from "@/components/ui/label"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON>, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { <PERSON><PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { getTranslations } from "@/lib/i18n/translations"
import {
  Plus,
  Trash2,
  Clock,
  CalendarIcon,
  Search,
  Filter,
  SortAsc,
  Edit,
  Save,
  X,
  ChevronDown,
  ChevronUp,
  Star,
  StarOff,
} from "lucide-react"
import { ToolControls } from "@/components/tool-controls"
import { ToolFullscreenWrapper } from "@/components/tool-fullscreen-wrapper"
import { format } from "date-fns"
import {
  fr, enUS, es, de, it, pt, nl, pl, uk, tr, ru, ar, he,
  id, ms, th, vi, zhCN as zh, ja, ko, el, bg, cs, sk, hu, ro,
  hr, sr, bs, sl, et, lv, lt, da, fi, nb, sv, ca, af
} from "date-fns/locale"

// Ajouter ces fonctions utilitaires pour le stockage local
const saveToLocalStorage = <T,>(key: string, data: T): void => {
  try {
    if (typeof window !== "undefined") {
      const serializedData = JSON.stringify(data)
      localStorage.setItem(key, serializedData)
    }
  } catch (error) {
    console.error("Error saving to localStorage:", error)
  }
}

const getFromLocalStorage = <T,>(key: string, defaultValue: T): T => {
  try {
    if (typeof window !== "undefined") {
      const serializedData = localStorage.getItem(key)
      if (serializedData === null) {
        return defaultValue
      }
      return JSON.parse(serializedData) as T
    }
    return defaultValue
  } catch (error) {
    console.error("Error getting from localStorage:", error)
    return defaultValue
  }
}

interface TodoListProps {
  lang: string
}

type Priority = "high" | "medium" | "low" | "none"
type Category = "work" | "personal" | "shopping" | "health" | "other"

interface Todo {
  id: string
  text: string
  completed: boolean
  createdAt: Date
  dueDate?: Date | null
  notes?: string
  priority: Priority
  category: Category
}

const STORAGE_KEY = "timetools_todo_list"

export function TodoList({ lang }: TodoListProps) {
  const translationsRef = useRef(getTranslations(lang));
  const t = translationsRef.current;
  const [todos, setTodos] = useState<Todo[]>([])
  const [newTodo, setNewTodo] = useState("")
  const [searchQuery, setSearchQuery] = useState("")
  const [activeFilter, setActiveFilter] = useState<"all" | "active" | "completed">("all")
  const [categoryFilter, setCategoryFilter] = useState<Category | "all">("all")
  const [priorityFilter, setPriorityFilter] = useState<Priority | "all">("all")
  const [sortBy, setSortBy] = useState<"date" | "priority" | "alphabetical">("date")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc")
  const [editingTodo, setEditingTodo] = useState<string | null>(null)
  const [editText, setEditText] = useState("")
  const [editNotes, setEditNotes] = useState("")
  const [editDueDate, setEditDueDate] = useState<Date | null>(null)
  const [editPriority, setEditPriority] = useState<Priority>("none")
  const [editCategory, setEditCategory] = useState<Category>("other")
  const [stats, setStats] = useState({ total: 0, completed: 0, active: 0 })
  const [isToolFullscreen, setIsToolFullscreen] = useState(false)

  // Locale pour date-fns
  const getLocale = () => {
    switch (lang) {
      case "fr": return fr;
      case "es": return es;
      case "de": return de;
      case "it": return it;
      case "pt": return pt;
      case "nl": return nl;
      case "pl": return pl;
      case "uk": return uk;
      case "tr": return tr;
      case "ru": return ru;
      case "ar": return ar;
      case "he": return he;
      case "id": return id;
      case "ms": return ms;
      case "th": return th;
      case "vi": return vi;
      case "zh": return zh;
      case "ja": return ja;
      case "ko": return ko;
      case "el": return el;
      case "bg": return bg;
      case "cs": return cs;
      case "sk": return sk;
      case "hu": return hu;
      case "ro": return ro;
      case "hr": return hr;
      case "sr": return sr;
      case "bs": return bs;
      case "sl": return sl;
      case "et": return et;
      case "lv": return lv;
      case "lt": return lt;
      case "da": return da;
      case "fi": return fi;
      case "nb": return nb;
      case "sv": return sv;
      case "ca": return ca;
      case "af": return af;
      default: return enUS;
    }
  }

  // Charger les todos depuis localStorage
  useEffect(() => {
    const savedTodos = getFromLocalStorage<any[]>(STORAGE_KEY, [])
    if (savedTodos && savedTodos.length > 0) {
      const parsedTodos = savedTodos.map((todo) => ({
        ...todo,
        createdAt: new Date(todo.createdAt),
        dueDate: todo.dueDate ? new Date(todo.dueDate) : null,
      }))
      setTodos(parsedTodos)
    } else {
      // Exemples de tâches
      setTodos([
        {
          id: "1",
          text: t.exampleTask1 || "Exemple de tâche 1",
          completed: false,
          createdAt: new Date(),
          priority: "high",
          category: "work",
          dueDate: new Date(Date.now() + 86400000), // demain
          notes: "",
        },
        {
          id: "2",
          text: t.exampleTask2 || "Exemple de tâche 2",
          completed: true,
          createdAt: new Date(Date.now() - 86400000), // hier
          priority: "medium",
          category: "personal",
          notes: "",
        },
      ])
    }
  }, [t])

  // Sauvegarder les todos dans localStorage
  useEffect(() => {
    if (todos.length > 0) {
      saveToLocalStorage(STORAGE_KEY, todos)
    }
  }, [todos])

  // Mettre à jour les statistiques
  useEffect(() => {
    setStats({
      total: todos.length,
      completed: todos.filter((todo) => todo.completed).length,
      active: todos.filter((todo) => !todo.completed).length,
    })
  }, [todos])

  const addTodo = () => {
    if (newTodo.trim() === "") return

    const todo: Todo = {
      id: Date.now().toString(),
      text: newTodo,
      completed: false,
      createdAt: new Date(),
      dueDate: null,
      notes: "",
      priority: "none",
      category: "other",
    }

    setTodos([todo, ...todos])
    setNewTodo("")
  }

  const toggleTodo = (id: string) => {
    setTodos(todos.map((todo) => (todo.id === id ? { ...todo, completed: !todo.completed } : todo)))
  }

  const deleteTodo = (id: string) => {
    setTodos(todos.filter((todo) => todo.id !== id))
  }

  const startEditing = (todo: Todo) => {
    setEditingTodo(todo.id)
    setEditText(todo.text)
    setEditNotes(todo.notes || "")
    setEditDueDate(todo.dueDate ?? null)
    setEditPriority(todo.priority)
    setEditCategory(todo.category)
  }

  const saveEdit = (id: string) => {
    setTodos(
      todos.map((todo) =>
        todo.id === id
          ? {
              ...todo,
              text: editText,
              notes: editNotes,
              dueDate: editDueDate,
              priority: editPriority,
              category: editCategory,
            }
          : todo,
      ),
    )
    setEditingTodo(null)
  }

  const cancelEdit = () => {
    setEditingTodo(null)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      addTodo()
    }
  }

  const formatDate = (date: Date) => {
    const now = new Date()
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24))

    if (diffInDays === 0) {
      return t.today
    } else if (diffInDays === 1) {
      return t.yesterday
    } else {
      return format(date, "PPP", { locale: getLocale() })
    }
  }

  const formatDueDate = (date: Date | null | undefined) => {
    if (!date) return null

    const now = new Date()
    const diffInDays = Math.floor((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

    if (diffInDays < 0) {
      return <Badge variant="destructive">{t.overdue}</Badge>
    } else if (diffInDays === 0) {
      return (
        <Badge variant="outline" className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
          {t.dueToday}
        </Badge>
      )
    } else if (diffInDays === 1) {
      return (
        <Badge variant="outline" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
          {t.dueTomorrow}
        </Badge>
      )
    } else {
      return <Badge variant="outline">{format(date, "PPP", { locale: getLocale() })}</Badge>
    }
  }

  const getPriorityBadge = (priority: Priority) => {
    switch (priority) {
      case "high":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
            {t.highPriority}
          </Badge>
        )
      case "medium":
        return (
          <Badge variant="outline" className="bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
            {t.mediumPriority}
          </Badge>
        )
      case "low":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            {t.lowPriority}
          </Badge>
        )
      default:
        return null
    }
  }

  const getCategoryBadge = (category: Category) => {
    switch (category) {
      case "work":
        return (
          <Badge variant="outline" className="bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
            {t.workCategory}
          </Badge>
        )
      case "personal":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
            {t.personalCategory}
          </Badge>
        )
      case "shopping":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            {t.shoppingCategory}
          </Badge>
        )
      case "health":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
            {t.healthCategory}
          </Badge>
        )
      case "other":
        return <Badge variant="outline">{t.otherCategory}</Badge>
      default:
        return null
    }
  }

  const getPriorityIcon = (priority: Priority) => {
    switch (priority) {
      case "high":
        return <Star className="h-4 w-4 text-red-500" />
      case "medium":
        return <Star className="h-4 w-4 text-orange-500" />
      case "low":
        return <Star className="h-4 w-4 text-green-500" />
      default:
        return <StarOff className="h-4 w-4 text-muted-foreground" />
    }
  }

  // Filtrer et trier les todos
  const filteredAndSortedTodos = todos
    .filter((todo) => {
      // Filtre de recherche
      const matchesSearch =
        todo.text.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (todo.notes && todo.notes.toLowerCase().includes(searchQuery.toLowerCase()))

      // Filtre d'état
      const matchesStateFilter =
        activeFilter === "all" ||
        (activeFilter === "active" && !todo.completed) ||
        (activeFilter === "completed" && todo.completed)

      // Filtre de catégorie
      const matchesCategoryFilter = categoryFilter === "all" || todo.category === categoryFilter

      // Filtre de priorité
      const matchesPriorityFilter = priorityFilter === "all" || todo.priority === priorityFilter

      return matchesSearch && matchesStateFilter && matchesCategoryFilter && matchesPriorityFilter
    })
    .sort((a, b) => {
      // Tri
      if (sortBy === "date") {
        return sortDirection === "asc"
          ? new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          : new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      } else if (sortBy === "priority") {
        const priorityOrder = { high: 3, medium: 2, low: 1, none: 0 }
        const priorityA = priorityOrder[a.priority] || 0
        const priorityB = priorityOrder[b.priority] || 0
        return sortDirection === "asc" ? priorityA - priorityB : priorityB - priorityA
      } else {
        // alphabetical
        return sortDirection === "asc" ? a.text.localeCompare(b.text) : b.text.localeCompare(a.text)
      }
    })

  const clearCompletedTasks = () => {
    setTodos(todos.filter((todo) => !todo.completed))
  }

  const todoContent = (
    <>
      <div className="flex flex-col sm:flex-row gap-2 mb-6">
        <Input
          placeholder={t.addTask}
          value={newTodo}
          onChange={(e) => setNewTodo(e.target.value)}
          onKeyDown={handleKeyDown}
          className="flex-1"
        />
        <Button onClick={addTodo} className="w-full sm:w-auto">
          <Plus className="h-4 w-4 mr-2" />
          {t.add}
        </Button>
      </div>

      <div className="mb-6">
        <div className="flex flex-col sm:flex-row gap-2 mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t.searchTasks}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8"
            />
          </div>

          <div className="flex gap-2">
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" size="sm" className="h-10">
                  <Filter className="h-4 w-4 mr-2" />
                  {t.filter}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">{t.status}</h4>
                    <Tabs defaultValue={activeFilter} onValueChange={(value) => setActiveFilter(value as any)}>
                      <TabsList className="grid grid-cols-3">
                        <TabsTrigger value="all">{t.all}</TabsTrigger>
                        <TabsTrigger value="active">{t.active}</TabsTrigger>
                        <TabsTrigger value="completed">{t.completed}</TabsTrigger>
                      </TabsList>
                    </Tabs>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">{t.category}</h4>
                    <Select value={categoryFilter} onValueChange={(value) => setCategoryFilter(value as any)}>
                      <SelectTrigger>
                        <SelectValue placeholder={t.selectCategory} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">{t.all}</SelectItem>
                        <SelectItem value="work">{t.workCategory}</SelectItem>
                        <SelectItem value="personal">{t.personalCategory}</SelectItem>
                        <SelectItem value="shopping">{t.shoppingCategory}</SelectItem>
                        <SelectItem value="health">{t.healthCategory}</SelectItem>
                        <SelectItem value="other">{t.otherCategory}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">{t.priority}</h4>
                    <Select value={priorityFilter} onValueChange={(value) => setPriorityFilter(value as any)}>
                      <SelectTrigger>
                        <SelectValue placeholder={t.selectPriority} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">{t.all}</SelectItem>
                        <SelectItem value="high">{t.highPriority}</SelectItem>
                        <SelectItem value="medium">{t.mediumPriority}</SelectItem>
                        <SelectItem value="low">{t.lowPriority}</SelectItem>
                        <SelectItem value="none">{t.noPriority}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </PopoverContent>
            </Popover>

            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" size="sm" className="h-10">
                  <SortAsc className="h-4 w-4 mr-2" />
                  {t.sort}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-56">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">{t.sortBy}</h4>
                    <Select value={sortBy} onValueChange={(value) => setSortBy(value as any)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="date">{t.date}</SelectItem>
                        <SelectItem value="priority">{t.priority}</SelectItem>
                        <SelectItem value="alphabetical">{t.alphabetical}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">{t.direction}</h4>
                    <div className="flex gap-2">
                      <Button
                        variant={sortDirection === "asc" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSortDirection("asc")}
                        className="flex-1"
                      >
                        <ChevronUp className="h-4 w-4 mr-2" />
                        {t.ascending}
                      </Button>
                      <Button
                        variant={sortDirection === "desc" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSortDirection("desc")}
                        className="flex-1"
                      >
                        <ChevronDown className="h-4 w-4 mr-2" />
                        {t.descending}
                      </Button>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row justify-between items-center mb-2">
          <div className="flex gap-2">
            <Badge variant="outline">
              {t.total}: {stats.total}
            </Badge>
            <Badge variant="outline" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
              {t.completed}: {stats.completed}
            </Badge>
            <Badge variant="outline" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
              {t.active}: {stats.active}
            </Badge>
          </div>

          {stats.completed > 0 && (
            <Button variant="ghost" size="sm" onClick={clearCompletedTasks}>
              {t.clearCompleted}
            </Button>
          )}
        </div>
      </div>

      <AnimatePresence>
        {filteredAndSortedTodos.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">{t.noTasksYet}</div>
        ) : (
          <ul className="space-y-3">
            {filteredAndSortedTodos.map((todo) => (
              <motion.li
                key={todo.id}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.2 }}
                className={`border rounded-md overflow-hidden ${todo.completed ? "bg-muted/30" : ""}`}
              >
                {editingTodo === todo.id ? (
                  <div className="p-4 space-y-3">
                    <div className="flex gap-2">
                      <Input
                        value={editText}
                        onChange={(e) => setEditText(e.target.value)}
                        placeholder={t.taskTitle}
                        className="flex-1"
                      />
                      <Button variant="ghost" size="icon" onClick={() => saveEdit(todo.id)}>
                        <Save className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={cancelEdit}>
                        <X className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      <div className="space-y-2">
                        <Label>{t.notes}</Label>
                        <Textarea
                          value={editNotes}
                          onChange={(e) => setEditNotes(e.target.value)}
                          placeholder={t.addNotes}
                          className="h-20"
                        />
                      </div>

                      <div className="space-y-3">
                        <div className="space-y-2">
                          <Label>{t.dueDate}</Label>
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button variant="outline" className="w-full justify-start text-left font-normal">
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {editDueDate ? (
                                  format(editDueDate, "PPP", { locale: getLocale() })
                                ) : (
                                  <span>{t.pickDate}</span>
                                )}
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                              <Calendar
                                mode="single"
                                selected={editDueDate || undefined}
                                onSelect={setEditDueDate}
                                initialFocus
                                lang={lang} // Pass the language prop here
                              />
                            </PopoverContent>
                          </Popover>
                        </div>

                        <div className="grid grid-cols-2 gap-3">
                          <div className="space-y-2">
                            <Label>{t.priority}</Label>
                            <Select
                              value={editPriority}
                              onValueChange={(value) => setEditPriority(value as Priority)}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="high">{t.highPriority}</SelectItem>
                                <SelectItem value="medium">{t.mediumPriority}</SelectItem>
                                <SelectItem value="low">{t.lowPriority}</SelectItem>
                                <SelectItem value="none">{t.noPriority}</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label>{t.category}</Label>
                            <Select
                              value={editCategory}
                              onValueChange={(value) => setEditCategory(value as Category)}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="work">{t.workCategory}</SelectItem>
                                <SelectItem value="personal">{t.personalCategory}</SelectItem>
                                <SelectItem value="shopping">{t.shoppingCategory}</SelectItem>
                                <SelectItem value="health">{t.healthCategory}</SelectItem>
                                <SelectItem value="other">{t.otherCategory}</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="p-3">
                    <div className="flex items-start gap-3">
                      <Checkbox
                        checked={todo.completed}
                        onCheckedChange={() => toggleTodo(todo.id)}
                        id={`todo-${todo.id}`}
                        className="mt-1"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex flex-wrap gap-2 items-center mb-1">
                          <label
                            htmlFor={`todo-${todo.id}`}
                            className={`font-medium ${todo.completed ? "line-through text-muted-foreground" : ""}`}
                          >
                            {todo.text}
                          </label>
                          {getPriorityIcon(todo.priority)}
                        </div>

                        {todo.notes && (
                          <p className="text-sm text-muted-foreground mb-2 line-clamp-2">{todo.notes}</p>
                        )}

                        <div className="flex flex-wrap gap-2 mt-2">
                          {getCategoryBadge(todo.category)}
                          {todo.dueDate && formatDueDate(todo.dueDate)}
                          <span className="text-xs text-muted-foreground flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {formatDate(todo.createdAt)}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        <Button variant="ghost" size="icon" onClick={() => startEditing(todo)} className="h-8 w-8">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => deleteTodo(todo.id)}
                          className="h-8 w-8 text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </motion.li>
            ))}
          </ul>
        )}
      </AnimatePresence>
    </>
  )

  return (
    <>
      <Card className="w-full max-w-5xl mx-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>{t.todoList}</CardTitle>
            <ToolControls
              onFullscreenToggle={setIsToolFullscreen}
              variant="compact"
            />
          </div>
        </CardHeader>
        <CardContent>
          {todoContent}
        </CardContent>
        <CardFooter className="flex justify-between border-t pt-6">
          <div className="text-sm text-muted-foreground">
            {filteredAndSortedTodos.length} {filteredAndSortedTodos.length === 1 ? t.task : t.tasks} {t.displayed}
          </div>
        </CardFooter>
      </Card>

      {/* Wrapper plein écran */}
      <ToolFullscreenWrapper
        isFullscreen={isToolFullscreen}
        onClose={() => setIsToolFullscreen(false)}
        toolName={t.todoList}
      >
        <Card className="w-full border-0 shadow-none bg-transparent">
          <CardContent>
            {todoContent}
          </CardContent>
          <CardFooter className="flex justify-between border-t pt-6">
            <div className="text-sm text-muted-foreground">
              {filteredAndSortedTodos.length} {filteredAndSortedTodos.length === 1 ? t.task : t.tasks} {t.displayed}
            </div>
          </CardFooter>
        </Card>
      </ToolFullscreenWrapper>
    </>
  )
}
