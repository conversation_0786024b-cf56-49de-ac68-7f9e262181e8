"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { getTranslations } from "@/lib/i18n/translations"
import { getTranslatedRoute } from "@/lib/i18n/route-translations"
import Link from "next/link"
import { ArrowRight, Clock, Timer, Globe, ListTodo, Activity } from "lucide-react"

interface ContextualNavigationProps {
  lang?: string
  currentTool?: string
  context?: "productivity" | "time-management" | "fitness" | "general"
}

export function ContextualNavigation({ 
  lang = "en", 
  currentTool, 
  context = "general" 
}: ContextualNavigationProps) {
  const t = getTranslations(lang)

  // Helper function to get localized links
  const getLocalizedLink = (route: string) => {
    const translatedRoute = getTranslatedRoute(lang, route)
    return `/${lang}/${translatedRoute}`
  }

  // Define contextual suggestions based on the context
  const getContextualSuggestions = () => {
    switch (context) {
      case "productivity":
        return [
          {
            title: t.improveProductivity || "Améliorez votre productivité",
            description: t.productivityToolsDesc || "Découvrez nos outils de productivité",
            links: [
              { name: t.pomodoro, href: getLocalizedLink("pomodoro"), icon: <Timer className="h-4 w-4" /> },
              { name: t.todoList, href: getLocalizedLink("todo"), icon: <ListTodo className="h-4 w-4" /> },
              { name: t.timeTracking, href: getLocalizedLink("time-tracking"), icon: <Clock className="h-4 w-4" /> }
            ]
          }
        ]
      
      case "time-management":
        return [
          {
            title: t.timeManagementTools || "Outils de gestion du temps",
            description: t.timeToolsDesc || "Maîtrisez votre temps avec nos outils",
            links: [
              { name: t.timerStopwatch, href: getLocalizedLink("timer"), icon: <Timer className="h-4 w-4" /> },
              { name: t.countdown, href: getLocalizedLink("countdown"), icon: <Clock className="h-4 w-4" /> },
              { name: t.worldClock, href: getLocalizedLink("world-clock"), icon: <Globe className="h-4 w-4" /> },
              { name: t.intervals, href: getLocalizedLink("intervals"), icon: <Activity className="h-4 w-4" /> }
            ]
          }
        ]
      
      case "fitness":
        return [
          {
            title: t.fitnessTools || "Outils de fitness",
            description: t.fitnessFeaturesDesc || "Optimisez vos entraînements",
            links: [
              { name: t.workoutIntervals, href: getLocalizedLink("workout-intervals"), icon: <Activity className="h-4 w-4" /> },
              { name: t.exerciseTemplates, href: getLocalizedLink("exercise-templates"), icon: <ListTodo className="h-4 w-4" /> },
              { name: t.intervals, href: getLocalizedLink("intervals"), icon: <Activity className="h-4 w-4" /> }
            ]
          }
        ]
      
      default:
        return [
          {
            title: t.exploreMoreTools || "Explorez plus d'outils",
            description: t.discoverAllFeatures || "Découvrez toutes nos fonctionnalités",
            links: [
              { name: t.timerStopwatch, href: getLocalizedLink("timer"), icon: <Timer className="h-4 w-4" /> },
              { name: t.pomodoro, href: getLocalizedLink("pomodoro"), icon: <Timer className="h-4 w-4" /> },
              { name: t.todoList, href: getLocalizedLink("todo"), icon: <ListTodo className="h-4 w-4" /> },
              { name: t.worldClock, href: getLocalizedLink("world-clock"), icon: <Globe className="h-4 w-4" /> }
            ]
          }
        ]
    }
  }

  const suggestions = getContextualSuggestions()

  // Filter out current tool from suggestions
  const filteredSuggestions = suggestions.map(suggestion => ({
    ...suggestion,
    links: suggestion.links.filter(link => {
      const linkRoute = link.href.split('/').pop()
      return linkRoute !== currentTool
    })
  })).filter(suggestion => suggestion.links.length > 0)

  if (filteredSuggestions.length === 0) {
    return null
  }

  return (
    <div className="my-8">
      {filteredSuggestions.map((suggestion, index) => (
        <Card key={index} className="bg-gradient-to-r from-primary/5 to-secondary/5 border-primary/20">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <ArrowRight className="h-5 w-5 text-primary" />
              {suggestion.title}
            </CardTitle>
            <p className="text-sm text-muted-foreground">{suggestion.description}</p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
              {suggestion.links.map((link, linkIndex) => (
                <Link
                  key={linkIndex}
                  href={link.href}
                  className="group flex items-center gap-2 p-3 rounded-lg border border-border hover:border-primary hover:bg-primary/5 transition-all duration-200"
                >
                  <div className="p-1 rounded-md bg-primary/10 text-primary group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                    {link.icon}
                  </div>
                  <span className="text-sm font-medium group-hover:text-primary transition-colors">
                    {link.name}
                  </span>
                </Link>
              ))}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
