export const languages = [
  { code: "en", name: "English" },
  { code: "fr", name: "Français" },
  { code: "es", name: "<PERSON><PERSON><PERSON><PERSON><PERSON>" },
  { code: "de", name: "<PERSON><PERSON><PERSON>" },
  { code: "it", name: "<PERSON><PERSON>" },
  { code: "pt", name: "Português" },
  { code: "nl", name: "Nederlands" },
  { code: "pl", name: "<PERSON><PERSON>" },
  { code: "uk", name: "Українська" },
  { code: "tr", name: "<PERSON><PERSON><PERSON><PERSON><PERSON>" },
  { code: "ru", name: "Русский" },
  { code: "ar", name: "العربية", rtl: true },
  { code: "he", name: "עברית", rtl: true },
  { code: "fa", name: "فارسی", rtl: true },
  { code: "hi", name: "हिन्दी" },
  { code: "bn", name: "বাংলা" },
  { code: "te", name: "తెలుగు" },
  { code: "ta", name: "தமிழ்" },
  { code: "mr", name: "मराठी" },
  { code: "gu", name: "ગુજરાતી" },
  { code: "kn", name: "ಕನ್ನಡ" },
  { code: "ml", name: "മലയാളം" },
  { code: "pa", name: "ਪੰਜਾਬੀ" },
  { code: "ur", name: "اردو", rtl: true },
  { code: "id", name: "Bahasa Indonesia" },
  { code: "ms", name: "Bahasa Melayu" },
  { code: "th", name: "ไทย" },
  { code: "vi", name: "Tiếng Việt" },
  { code: "km", name: "ខ្មែរ" },
  { code: "my", name: "မြန်မာ" },
  { code: "zh", name: "中文" },
  { code: "ja", name: "日本語" },
  { code: "ko", name: "한국어" },
  { code: "el", name: "Ελληνικά" },
  { code: "bg", name: "Български" },
  { code: "cs", name: "Čeština" },
  { code: "sk", name: "Slovenčina" },
  { code: "hu", name: "Magyar" },
  { code: "ro", name: "Română" },
  { code: "hr", name: "Hrvatski" },
  { code: "sr", name: "Српски" },
  { code: "bs", name: "Bosanski" },
  { code: "sl", name: "Slovenščina" },
  { code: "mk", name: "Македонски" },
  { code: "et", name: "Eesti" },
  { code: "lv", name: "Latviešu" },
  { code: "lt", name: "Lietuvių" },
  { code: "da", name: "Dansk" },
  { code: "fi", name: "Suomi" },
  { code: "nb", name: "Norsk bokmål" },
  { code: "sv", name: "Svenska" },
  { code: "ca", name: "Català" },
  { code: "gl", name: "Galego" },
  { code: "eu", name: "Euskara" },
  { code: "af", name: "Afrikaans" },
  { code: "sw", name: "Kiswahili" },
  { code: "am", name: "አማርኛ" },
  { code: "ka", name: "ქართული" },
  { code: "hy", name: "Հայերեն" },
  { code: "az", name: "Azərbaycan" },
  { code: "uz", name: "O'zbek" },
  { code: "kk", name: "Қазақ" },
  { code: "tg", name: "Тоҷикӣ" },
  { code: "tk", name: "Türkmençe" },
  { code: "ky", name: "Кыргызча" },
];

export function isRtlLang(langCode: string): boolean {
  const langConfig = languages.find(l => l.code === langCode);
  return !!langConfig?.rtl;
}

