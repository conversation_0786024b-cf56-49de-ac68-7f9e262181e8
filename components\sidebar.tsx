"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { useLanguage } from "@/components/language-provider"
import { Button } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { LanguageSelector } from "@/components/language-selector"
import { ThemeToggle } from "@/components/theme-toggle"
import { getTranslations } from "@/lib/i18n/translations"
import { getTranslatedRoute } from "@/lib/i18n/route-translations"
import { Clock, Timer, Globe, ListTodo, Users, FileText, Activity, Menu, X } from "lucide-react"
import { PWAInstaller } from "@/components/pwa-installer"
import { getOriginalRoute } from "@/lib/i18n/route-translations"

export function Sidebar() {
  const { language } = useLanguage()
  const pathname = usePathname()
  // Get the language from the URL path for immediate rendering
  const pathLang = pathname.split('/')[1]
  const isValidPathLang = pathLang && pathLang.length === 2

  // Use the path language for initial render to prevent flicker
  const effectiveLanguage = isValidPathLang ? pathLang : language
  const t = getTranslations(effectiveLanguage)
  const [open, setOpen] = useState(false)

  const getLocalizedLink = (route: string) => {
    const translatedRoute = getTranslatedRoute(effectiveLanguage, route)
    return `/${effectiveLanguage}/${translatedRoute}`
  }

  const isLinkActive = (route: string) => {
    // Get current language and route segment from the pathname
    const pathSegments = pathname.split("/");
    const currentLangFromPath = pathSegments[1] || "";
    // Decode segment in case of special characters in route (e.g., non-ASCII)
    const currentRouteSegmentFromPath = decodeURIComponent(pathSegments[2] || "");

    // Get the translated version of the link's route (e.g., "timer" -> "المؤقت" if lang is "ar")
    const translatedRouteForLink = getTranslatedRoute(effectiveLanguage, route);

    // Construct the expected full path for the link (e.g., /ar/المؤقت)
    const expectedFullPath = `/${effectiveLanguage}/${translatedRouteForLink}`;

    // Normalize for comparison
    // Decode the full pathname as well, then convert to lower case and trim
    const normalizedPathname = decodeURIComponent(pathname).toLowerCase().trim();
    const normalizedExpectedFullPath = expectedFullPath.toLowerCase().trim();

    // Match 1: Direct full path comparison
    const fullPathMatch = normalizedPathname === normalizedExpectedFullPath;

    // Match 2: Original route comparison (fallback)
    // Get the original English route key from the current URL's translated segment
    // e.g., if current URL is /ar/المؤقت, this should give "timer"
    const originalRouteFromCurrentURL = getOriginalRoute(effectiveLanguage, currentRouteSegmentFromPath);
    const originalRouteMatch = originalRouteFromCurrentURL.toLowerCase().trim() === route.toLowerCase().trim();


    // --- End Logging ---

    return fullPathMatch || originalRouteMatch;
  };

  const menuItems = [
    {
      category: t.timeTools,
      items: [
        {
          name: t.timerStopwatch,
          href: getLocalizedLink("timer"),
          icon: <Timer className="h-5 w-5" />,
          route: "timer",
        },
        {
          name: t.countdown,
          href: getLocalizedLink("countdown"),
          icon: <Clock className="h-5 w-5" />,
          route: "countdown",
        },
        {
          name: t.worldClock,
          href: getLocalizedLink("world-clock"),
          icon: <Globe className="h-5 w-5" />,
          route: "world-clock",
        },
        {
          name: t.intervals,
          href: getLocalizedLink("intervals"),
          icon: <Activity className="h-5 w-5" />,
          route: "intervals",
        },
        {
          name: t.pomodoro,
          href: getLocalizedLink("pomodoro"),
          icon: <Timer className="h-5 w-5" />,
          route: "pomodoro",
        },
      ],
    },
    {
      category: t.productivitySuite,
      items: [
        { name: t.todoList, href: getLocalizedLink("todo"), icon: <ListTodo className="h-5 w-5" />, route: "todo" },
        {
          name: t.timeTracking,
          href: getLocalizedLink("time-tracking"),
          icon: <Clock className="h-5 w-5" />,
          route: "time-tracking",
        },
      ],
    },
    {
      category: t.professionalTools,
      items: [
        {
          name: t.meetingTimer,
          href: getLocalizedLink("meeting-timer"),
          icon: <Users className="h-5 w-5" />,
          route: "meeting-timer",
        },
        {
          name: t.timeBilling,
          href: getLocalizedLink("time-billing"),
          icon: <FileText className="h-5 w-5" />,
          route: "time-billing",
        },
      ],
    },
    {
      category: t.fitnessTools,
      items: [
        {
          name: t.workoutIntervals,
          href: getLocalizedLink("workout-intervals"),
          icon: <Activity className="h-5 w-5" />,
          route: "workout-intervals",
        },
        {
          name: t.exerciseTemplates,
          href: getLocalizedLink("exercise-templates"),
          icon: <ListTodo className="h-5 w-5" />,
          route: "exercise-templates",
        },
      ],
    },
  ]

  return (
    <header className="border-b w-full">
      <div className="max-w-7xl mx-auto px-4 py-4 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Sheet open={open} onOpenChange={setOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="md:hidden">
                <Menu className="h-6 w-6" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-[300px] sm:w-[400px]">
              <div className="flex flex-col h-full">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <Image
                      src="/logo.png"
                      alt={`${t.appName} Logo`}
                      width={32}
                      height={32}
                      className="w-8 h-8"
                    />
                    <h2 className="text-2xl font-bold">{t.appName}</h2>
                  </div>
                  <Button variant="ghost" size="icon" onClick={() => setOpen(false)}>
                    <X className="h-6 w-6" />
                    <span className="sr-only">Close menu</span>
                  </Button>
                </div>
                <nav className="flex-1 overflow-auto">
                  {menuItems.map((category, i) => (
                    <div key={i} className="mb-6">
                      <h3 className="text-sm font-medium text-muted-foreground mb-2">{category.category}</h3>
                      <ul className="space-y-1">
                        {category.items.map((item, j) => (
                          <li key={j}>
                            <Link
                              key={`${item.href}-${language}`} // Add language to key for potential re-renders on lang change
                              href={item.href}
                              className={`flex items-center gap-3 px-3 py-2 rounded-md text-sm ${ // Reverted to Tailwind classes for active state
                                isLinkActive(item.route) ? "bg-primary text-primary-foreground" : "hover:bg-muted"
                              }`}
                              // Removed inline style for active state, relying on Tailwind classes
                              onClick={() => setOpen(false)}
                            >
                              {item.icon}
                              {item.name}
                            </Link>
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </nav>
                <div className="border-t pt-4 space-y-4">
                  <div className="flex justify-center">
                    <PWAInstaller />
                  </div>
                  <div className="flex items-center justify-between">
                    <LanguageSelector />
                    <div className="flex items-center gap-2">
                      <ThemeToggle />
                    </div>
                  </div>
                </div>
              </div>
            </SheetContent>
          </Sheet>

          <Link href={`/${effectiveLanguage}`} className="flex items-center gap-3 text-2xl font-bold">
            <Image
              src="/logo.png"
              alt={`${t.appName} Logo`}
              width={32}
              height={32}
              className="w-8 h-8"
            />
            {t.appName}
          </Link>

          <nav className="hidden md:flex items-center space-x-4">
            {menuItems.map((category, i) => (
              <div key={i} className="relative group">
                <Button variant="ghost" className="px-3 py-2 text-sm">
                  {category.category}
                </Button>
                <div className="absolute left-0 rtl:left-auto rtl:right-0 top-full z-50 hidden group-hover:block bg-background border rounded-md shadow-md p-2 min-w-[200px]">
                  <div className="flex flex-col space-y-1">
                    {category.items.map((item, j) => (
                      <Link
                        key={j}
                        href={item.href}
                        className={`flex items-center gap-3 px-3 py-2 rounded-md text-sm ${ // Reverted to Tailwind classes for active state
                          isLinkActive(item.route) ? "bg-primary text-primary-foreground" : "hover:bg-muted"
                        }`}
                        // Removed inline style for active state, relying on Tailwind classes
                      >
                        {item.icon}
                        {item.name}
                      </Link>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </nav>
        </div>

        <div className="flex items-center gap-2">
          <PWAInstaller variant="compact" />
          <LanguageSelector />
          <ThemeToggle />
        </div>
      </div>
    </header>
  )
}
