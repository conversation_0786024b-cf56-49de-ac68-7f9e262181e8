import { TimerDashboard } from "@/components/timer-dashboard"
import { FeatureHighlights } from "@/components/feature-highlights"
import { SeoContent } from "@/components/seo-content"
import { StructuredData } from "@/components/structured-data"
import { getTranslations } from "@/lib/i18n/translations"
import { Metadata } from "next"
import { generatePageMetadata } from "@/lib/i18n/metadata-utils"

export async function generateMetadata({ params }: { params: Promise<{ lang: string }> }): Promise<Metadata> {
  const { lang } = await params;
  // Utiliser la fonction utilitaire pour générer les métadonnées de la page d'accueil
  return generatePageMetadata(lang);
}

export default async function LocalizedHome({ params }: { params: Promise<{ lang: string }> }) {
  const { lang } = await params;
  const t = getTranslations(lang);

  return (
    <div className="container mx-auto space-y-8">
      {/* Add structured data */}
      <StructuredData type="website" />
      <StructuredData type="organization" />

      {/* Main page title - H1 */}
      <div className="text-center py-6">
        <h1 className="text-2xl md:text-3xl font-bold mb-3">{t.homePageTitle}</h1>
        <p className="text-lg text-muted-foreground max-w-3xl mx-auto">{t.homePageDescription}</p>
      </div>

      <TimerDashboard lang={lang} />
      <FeatureHighlights lang={lang} />
      <SeoContent lang={lang} />
    </div>
  );
}
