"use client"

import { useState, useRef, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { useTimerAudio } from "@/hooks/use-robust-audio"

export function ExerciseTimerDebug() {
  const [testLog, setTestLog] = useState<string[]>([])
  const [restTimer, setRestTimer] = useState(0)
  const [restTimerActive, setRestTimerActive] = useState(false)
  const countdownWorkerRef = useRef<Worker | null>(null)
  const soundPlayedRef = useRef(false)
  
  const { playTimerSound, soundEnabled, toggleSound } = useTimerAudio()

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setTestLog(prev => [...prev, `${timestamp}: ${message}`])
    console.log(`[ExerciseTimerDebug] ${message}`)
  }

  // Reproduire la logique d'exercise-templates
  useEffect(() => {
    if (restTimerActive && restTimer > 0) {
      addLog(`Starting countdown worker for ${restTimer} seconds`)
      
      if (countdownWorkerRef.current) {
        countdownWorkerRef.current.terminate()
      }

      countdownWorkerRef.current = new Worker("/workers/countdown-worker.js")
      soundPlayedRef.current = false

      countdownWorkerRef.current.onmessage = (event) => {
        const { type, timeLeft } = event.data
        addLog(`Worker message: ${type}, timeLeft: ${timeLeft}`)

        if (type === "tick") {
          setRestTimer(timeLeft)
          
          if (timeLeft <= 0 && !soundPlayedRef.current) {
            addLog("Timer reached zero, stopping and playing sound")
            setRestTimerActive(false)
            soundPlayedRef.current = true

            // Reproduire exactement le code d'exercise-templates
            if (soundEnabled) {
              playTimerSound("bell").then((success) => {
                if (success) {
                  addLog("Exercise rest timer completion sound played successfully")
                } else {
                  addLog("Failed to play exercise rest timer completion sound")
                }
              }).catch((error) => {
                addLog(`Could not play exercise rest timer sound: ${error}`)
              })
            }
          }
        }
      }

      const endTime = Date.now() + restTimer * 1000
      countdownWorkerRef.current.postMessage({
        command: "start",
        value: endTime
      })
    }

    return () => {
      if (countdownWorkerRef.current) {
        countdownWorkerRef.current.postMessage({ command: "stop" })
        countdownWorkerRef.current.terminate()
        countdownWorkerRef.current = null
      }
    }
  }, [restTimer, restTimerActive]) // Remove audio dependencies to prevent worker recreation

  const startRestTimer = (seconds: number) => {
    addLog(`Starting rest timer for ${seconds} seconds`)
    setRestTimer(seconds)
    setRestTimerActive(true)
    soundPlayedRef.current = false
  }

  const stopRestTimer = () => {
    addLog("Stopping rest timer")
    setRestTimerActive(false)
    setRestTimer(0)
    if (countdownWorkerRef.current) {
      countdownWorkerRef.current.postMessage({ command: "stop" })
      countdownWorkerRef.current.terminate()
      countdownWorkerRef.current = null
    }
  }

  const testDirectSound = async () => {
    addLog("Testing direct sound call...")
    try {
      const result = await playTimerSound("bell")
      addLog(`Direct sound result: ${result ? "SUCCESS" : "FAILED"}`)
    } catch (error) {
      addLog(`Direct sound error: ${error}`)
    }
  }

  const clearLog = () => {
    setTestLog([])
  }

  return (
    <div className="p-4 border rounded-lg bg-white shadow">
      <h3 className="font-bold mb-4">Exercise Timer Debug</h3>
      
      <div className="space-y-2 mb-4">
        <p>Sound Enabled: {soundEnabled ? "✅ YES" : "❌ NO"}</p>
        <p>Rest Timer Active: {restTimerActive ? "✅ YES" : "❌ NO"}</p>
        <p>Rest Timer: {restTimer}s</p>
        <p>Worker Ref: {countdownWorkerRef.current ? "✅ EXISTS" : "❌ NULL"}</p>
        <p>Sound Played: {soundPlayedRef.current ? "✅ YES" : "❌ NO"}</p>
      </div>

      <div className="flex flex-wrap gap-2 mb-4">
        <Button onClick={toggleSound} variant="outline" size="sm">
          {soundEnabled ? "Disable" : "Enable"} Sound
        </Button>
        <Button onClick={testDirectSound} variant="outline" size="sm">
          Test Direct Sound
        </Button>
        <Button 
          onClick={() => startRestTimer(5)} 
          variant="outline" 
          size="sm"
          disabled={restTimerActive}
        >
          Start 5s Timer
        </Button>
        <Button 
          onClick={() => startRestTimer(10)} 
          variant="outline" 
          size="sm"
          disabled={restTimerActive}
        >
          Start 10s Timer
        </Button>
        <Button 
          onClick={stopRestTimer} 
          variant="outline" 
          size="sm"
          disabled={!restTimerActive}
        >
          Stop Timer
        </Button>
        <Button onClick={clearLog} variant="ghost" size="sm">
          Clear Log
        </Button>
      </div>

      <div className="bg-gray-50 p-3 rounded max-h-48 overflow-y-auto">
        <h4 className="font-medium mb-2">Debug Log:</h4>
        {testLog.length === 0 ? (
          <p className="text-gray-500 text-sm">No activity yet</p>
        ) : (
          <div className="space-y-1">
            {testLog.map((log, index) => (
              <p key={index} className="text-xs font-mono">
                {log}
              </p>
            ))}
          </div>
        )}
      </div>

      <div className="mt-4 p-3 bg-green-50 rounded text-sm">
        <h4 className="font-medium mb-2">Instructions:</h4>
        <ol className="space-y-1">
          <li>1. Activez le son</li>
          <li>2. Testez le son direct</li>
          <li>3. Démarrez un timer de 5s ou 10s</li>
          <li>4. Attendez que le timer atteigne 0</li>
          <li>5. Vérifiez si le son se joue</li>
          <li>6. Consultez les logs pour identifier le problème</li>
        </ol>
      </div>
    </div>
  )
}
