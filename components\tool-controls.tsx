"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Maximize, Minimize, Volume2, VolumeX } from "lucide-react"
import { useLanguage } from "@/components/language-provider"
import { useSound } from "@/components/sound-provider"
import { getTranslations } from "@/lib/i18n/translations"

interface ToolControlsProps {
  onFullscreenToggle?: (isFullscreen: boolean) => void
  className?: string
  variant?: "default" | "compact"
  showSound?: boolean
}

export function ToolControls({
  onFullscreenToggle,
  className = "",
  variant = "default",
  showSound = true
}: ToolControlsProps) {
  const [isToolFullscreen, setIsToolFullscreen] = useState(false)
  const { language } = useLanguage()
  const { isSoundEnabled, toggleSound } = useSound()
  const t = getTranslations(language)

  const toggleToolFullscreen = () => {
    const newFullscreenState = !isToolFullscreen
    setIsToolFullscreen(newFullscreenState)
    onFullscreenToggle?.(newFullscreenState)
  }

  // Gérer la touche Échap pour sortir du plein écran
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isToolFullscreen) {
        setIsToolFullscreen(false)
        onFullscreenToggle?.(false)
      }
    }

    if (isToolFullscreen) {
      document.addEventListener('keydown', handleKeyDown)
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isToolFullscreen, onFullscreenToggle])

  const buttonSize = variant === "compact" ? "sm" : "default"
  const iconSize = variant === "compact" ? "h-4 w-4" : "h-[1.2rem] w-[1.2rem]"

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* Bouton Son */}
      {showSound && (
        <Button
          variant="outline"
          size={buttonSize}
          onClick={toggleSound}
          title={isSoundEnabled ? t.soundOff : t.soundOn}
          className="shrink-0"
        >
          {isSoundEnabled ?
            <Volume2 className={iconSize} /> :
            <VolumeX className={iconSize} />
          }
          <span className="sr-only">{isSoundEnabled ? t.soundOff : t.soundOn}</span>
        </Button>
      )}

      {/* Bouton Plein écran */}
      <Button
        variant="outline"
        size={buttonSize}
        onClick={toggleToolFullscreen}
        title={isToolFullscreen ? t.exitFullscreen : t.enterFullscreen}
        className="shrink-0"
      >
        {isToolFullscreen ?
          <Minimize className={iconSize} /> :
          <Maximize className={iconSize} />
        }
        <span className="sr-only">{isToolFullscreen ? t.exitFullscreen : t.enterFullscreen}</span>
      </Button>
    </div>
  )
}
