"use client"

import { useState, useRef, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { useTimerAudio } from "@/hooks/use-robust-audio"

export function MeetingTimerDebug() {
  const [testLog, setTestLog] = useState<string[]>([])
  const [isWorkerRunning, setIsWorkerRunning] = useState(false)
  const workerRef = useRef<Worker | null>(null)
  
  const { playTimerSound, soundEnabled, toggleSound } = useTimerAudio()

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setTestLog(prev => [...prev, `${timestamp}: ${message}`])
    console.log(`[MeetingTimerDebug] ${message}`)
  }

  // Initialiser le worker comme dans le meeting timer
  useEffect(() => {
    addLog("Initializing worker...")
    
    try {
      workerRef.current = new Worker('/workers/stopwatch-worker.js')
      addLog("Worker created successfully")

      workerRef.current.onmessage = (event: MessageEvent) => {
        const { type, ...data } = event.data
        addLog(`Worker message: ${type}`)

        if (data.playSound && soundEnabled) {
          addLog(`Worker requested sound: ${data.playSound}`)
          
          // Reproduire exactement le code du meeting timer
          playTimerSound(data.playSound as "bell").then((success) => {
            if (success) {
              addLog("Meeting timer sound played successfully")
            } else {
              addLog("Failed to play meeting timer sound")
            }
          }).catch((error) => {
            addLog(`Could not play meeting timer sound: ${error}`)
          })
        }
      }

      workerRef.current.onerror = (error) => {
        addLog(`Worker error: ${error}`)
      }

    } catch (error) {
      addLog(`Failed to create worker: ${error}`)
    }

    return () => {
      if (workerRef.current) {
        addLog("Terminating worker...")
        workerRef.current.terminate()
        workerRef.current = null
      }
    }
  }, [playTimerSound, soundEnabled])

  const startWorker = () => {
    if (workerRef.current) {
      addLog("Starting worker...")
      setIsWorkerRunning(true)
      
      // Envoyer des données d'agenda comme dans le meeting timer
      const testAgenda = [
        { id: "1", title: "Test Item 1", duration: 1, completed: false }, // 1 minute
        { id: "2", title: "Test Item 2", duration: 1, completed: false }
      ]
      
      workerRef.current.postMessage({
        command: 'updateData',
        payload: {
          agendaItems: testAgenda,
          plannedDuration: 2
        }
      })
      
      workerRef.current.postMessage({ command: 'reset' })
      
      setTimeout(() => {
        if (workerRef.current) {
          workerRef.current.postMessage({
            command: 'start',
            payload: {
              resetAgenda: true,
              agendaItems: testAgenda,
              plannedDuration: 2
            }
          })
        }
      }, 100)
    }
  }

  const stopWorker = () => {
    if (workerRef.current) {
      addLog("Stopping worker...")
      workerRef.current.postMessage({ command: 'pause' })
      setIsWorkerRunning(false)
    }
  }

  const testDirectSound = async () => {
    addLog("Testing direct sound call...")
    try {
      const result = await playTimerSound("bell")
      addLog(`Direct sound result: ${result ? "SUCCESS" : "FAILED"}`)
    } catch (error) {
      addLog(`Direct sound error: ${error}`)
    }
  }

  const testWorkerSound = () => {
    if (workerRef.current) {
      addLog("Requesting worker to trigger sound...")
      // Simuler un message qui devrait déclencher un son
      workerRef.current.postMessage({ command: 'forceNextItem' })
    }
  }

  const clearLog = () => {
    setTestLog([])
  }

  return (
    <div className="p-4 border rounded-lg bg-white shadow">
      <h3 className="font-bold mb-4">Meeting Timer Debug</h3>
      
      <div className="space-y-2 mb-4">
        <p>Sound Enabled: {soundEnabled ? "✅ YES" : "❌ NO"}</p>
        <p>Worker Running: {isWorkerRunning ? "✅ YES" : "❌ NO"}</p>
        <p>Worker Ref: {workerRef.current ? "✅ EXISTS" : "❌ NULL"}</p>
      </div>

      <div className="flex flex-wrap gap-2 mb-4">
        <Button onClick={toggleSound} variant="outline" size="sm">
          {soundEnabled ? "Disable" : "Enable"} Sound
        </Button>
        <Button onClick={testDirectSound} variant="outline" size="sm">
          Test Direct Sound
        </Button>
        <Button 
          onClick={startWorker} 
          variant="outline" 
          size="sm"
          disabled={isWorkerRunning}
        >
          Start Worker
        </Button>
        <Button 
          onClick={stopWorker} 
          variant="outline" 
          size="sm"
          disabled={!isWorkerRunning}
        >
          Stop Worker
        </Button>
        <Button 
          onClick={testWorkerSound} 
          variant="outline" 
          size="sm"
          disabled={!isWorkerRunning}
        >
          Test Worker Sound
        </Button>
        <Button onClick={clearLog} variant="ghost" size="sm">
          Clear Log
        </Button>
      </div>

      <div className="bg-gray-50 p-3 rounded max-h-48 overflow-y-auto">
        <h4 className="font-medium mb-2">Debug Log:</h4>
        {testLog.length === 0 ? (
          <p className="text-gray-500 text-sm">No activity yet</p>
        ) : (
          <div className="space-y-1">
            {testLog.map((log, index) => (
              <p key={index} className="text-xs font-mono">
                {log}
              </p>
            ))}
          </div>
        )}
      </div>

      <div className="mt-4 p-3 bg-blue-50 rounded text-sm">
        <h4 className="font-medium mb-2">Instructions:</h4>
        <ol className="space-y-1">
          <li>1. Activez le son</li>
          <li>2. Testez le son direct</li>
          <li>3. Démarrez le worker</li>
          <li>4. Testez le son via worker</li>
          <li>5. Vérifiez les logs pour identifier le problème</li>
        </ol>
      </div>
    </div>
  )
}
