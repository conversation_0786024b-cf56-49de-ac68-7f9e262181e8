"use client"

import { useEffect } from "react"
import { useTimerAudio } from "@/hooks/use-robust-audio"

export function DebugAudioHook() {
  const {
    playTimerSound,
    soundEnabled,
    toggleSound,
    isAudioReady,
    isPlaying,
    hasWakeLock,
    audioState,
    lastError
  } = useTimerAudio()

  useEffect(() => {
    console.log("[DebugAudioHook] Hook initialized with state:", {
      soundEnabled,
      isAudioReady,
      isPlaying,
      hasWakeLock,
      audioState,
      lastError
    })
  }, [soundEnabled, isAudioReady, isPlaying, hasWakeLock, audioState, lastError])

  useEffect(() => {
    console.log("[DebugAudioHook] playTimerSound function:", typeof playTimerSound)
    console.log("[DebugAudioHook] soundEnabled:", soundEnabled)
  }, [playTimerSound, soundEnabled])

  const testSound = async () => {
    console.log("[DebugAudioHook] Testing sound...")
    try {
      const result = await playTimerSound("bell")
      console.log("[DebugAudioHook] Sound test result:", result)
    } catch (error) {
      console.error("[DebugAudioHook] Sound test error:", error)
    }
  }

  return (
    <div className="p-4 border rounded-lg bg-gray-50">
      <h3 className="font-bold mb-2">Debug Audio Hook</h3>
      <div className="space-y-2 text-sm">
        <p>Sound Enabled: {soundEnabled ? "✅" : "❌"}</p>
        <p>Audio Ready: {isAudioReady ? "✅" : "❌"}</p>
        <p>Is Playing: {isPlaying ? "✅" : "❌"}</p>
        <p>Has Wake Lock: {hasWakeLock ? "✅" : "❌"}</p>
        <p>Audio State: {audioState}</p>
        <p>Last Error: {lastError || "None"}</p>
        <p>playTimerSound type: {typeof playTimerSound}</p>
        
        <div className="flex gap-2 mt-4">
          <button 
            onClick={toggleSound}
            className="px-3 py-1 bg-blue-500 text-white rounded"
          >
            Toggle Sound
          </button>
          <button 
            onClick={testSound}
            className="px-3 py-1 bg-green-500 text-white rounded"
            disabled={!soundEnabled}
          >
            Test Sound
          </button>
        </div>
      </div>
    </div>
  )
}
