@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  /* Utilitaires RTL personnalisés */
  .text-start {
    text-align: start !important;
  }
  
  .text-end {
    text-align: end !important;
  }
  
  [dir="rtl"] .rtl\:me-1 {
    margin-left: 0.25rem !important;
    margin-right: 0 !important;
  }
  
  [dir="rtl"] .rtl\:me-2 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
  }
  
  [dir="rtl"] .rtl\:me-3 {
    margin-left: 0.75rem !important;
    margin-right: 0 !important;
  }
  
  [dir="rtl"] .rtl\:me-4 {
    margin-left: 1rem !important;
    margin-right: 0 !important;
  }
  
  [dir="rtl"] .rtl\:ms-1 {
    margin-right: 0.25rem !important;
    margin-left: 0 !important;
  }
  
  [dir="rtl"] .rtl\:ms-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
  }
  
  [dir="rtl"] .rtl\:ms-3 {
    margin-right: 0.75rem !important;
    margin-left: 0 !important;
  }
  
  [dir="rtl"] .rtl\:ms-4 {
    margin-right: 1rem !important;
    margin-left: 0 !important;
  }
  
  [dir="rtl"] .rtl\:ps-1 {
    padding-right: 0.25rem !important;
    padding-left: 0 !important;
  }
  
  [dir="rtl"] .rtl\:ps-2 {
    padding-right: 0.5rem !important;
    padding-left: 0 !important;
  }
  
  [dir="rtl"] .rtl\:ps-3 {
    padding-right: 0.75rem !important;
    padding-left: 0 !important;
  }
  
  [dir="rtl"] .rtl\:ps-4 {
    padding-right: 1rem !important;
    padding-left: 0 !important;
  }
  
  [dir="rtl"] .rtl\:pe-1 {
    padding-left: 0.25rem !important;
    padding-right: 0 !important;
  }
  
  [dir="rtl"] .rtl\:pe-2 {
    padding-left: 0.5rem !important;
    padding-right: 0 !important;
  }
  
  [dir="rtl"] .rtl\:pe-3 {
    padding-left: 0.75rem !important;
    padding-right: 0 !important;
  }
  
  [dir="rtl"] .rtl\:pe-4 {
    padding-left: 1rem !important;
    padding-right: 0 !important;
  }
  
  /* Inversion des bordures en RTL */
  [dir="rtl"] .rtl\:border-s {
    border-right-width: 1px !important;
    border-left-width: 0 !important;
  }
  
  [dir="rtl"] .rtl\:border-e {
    border-left-width: 1px !important;
    border-right-width: 0 !important;
  }
  
  /* Coins arrondis RTL */
  [dir="rtl"] .rtl\:rounded-s {
    border-top-right-radius: var(--radius) !important;
    border-bottom-right-radius: var(--radius) !important;
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }
  
  [dir="rtl"] .rtl\:rounded-e {
    border-top-left-radius: var(--radius) !important;
    border-bottom-left-radius: var(--radius) !important;
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }
  
  /* Inversion des flexbox en RTL */
  [dir="rtl"] .rtl\:flex-row-reverse {
    flex-direction: row-reverse !important;
  }
  
  /* Position des éléments en RTL */
  [dir="rtl"] .rtl\:start-0 {
    left: auto !important;
    right: 0 !important;
  }
  
  [dir="rtl"] .rtl\:end-0 {
    right: auto !important;
    left: 0 !important;
  }
  
  /* Transformations RTL */
  [dir="rtl"] .rtl\:rotate-180 {
    transform: rotate(180deg) !important;
  }
  
  [dir="rtl"] .rtl\:scale-x-n1 {
    transform: scaleX(-1) !important;
  }
  
  /* Transitions pour les changements RTL/LTR */
  .rtl-transition {
    transition: margin 0.3s ease, padding 0.3s ease, transform 0.3s ease, text-align 0.3s ease;
  }
  
  /* Styles pour les éléments inversés en RTL */
  .rtl-inverted {
    transform: scaleX(-1);
  }
  
  /* Styles pour les tableaux en RTL */
  [dir="rtl"] table th,
  [dir="rtl"] table td {
    text-align: right;
  }
  
  [dir="ltr"] table th,
  [dir="ltr"] table td {
    text-align: left;
  }
  
  /* Styles pour les listes en RTL */
  [dir="rtl"] ul,
  [dir="rtl"] ol {
    padding-right: 1.5rem;
    padding-left: 0;
  }
  
  /* Autres alignements directionnels */
  [dir="rtl"] .rtl\:float-start {
    float: right !important;
  }
  
  [dir="rtl"] .rtl\:float-end {
    float: left !important;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Classes de base pour RTL/LTR */
  .rtl {
    direction: rtl !important;
    text-align: right !important;
  }
  
  .ltr {
    direction: ltr !important;
    text-align: left !important;
  }
  
  /* Styles pour les attributs dir */
  [dir="rtl"] {
    direction: rtl !important;
  }
  
  [dir="ltr"] {
    direction: ltr !important;
  }
  
  /* Marges et espacements spécifiques au RTL */
  [dir="rtl"] .mr-1, [dir="rtl"] .mr-2, [dir="rtl"] .mr-3, [dir="rtl"] .mr-4 {
    margin-right: 0 !important;
  }
  
  [dir="rtl"] .ml-1, [dir="rtl"] .ml-2, [dir="rtl"] .ml-3, [dir="rtl"] .ml-4 {
    margin-left: 0 !important;
  }
  
  /* Forcer les directions pour les conteneurs RTL/LTR */
  html[dir="rtl"] *,
  body.rtl * {
    direction: inherit;
  }
  
  html[dir="ltr"] *,
  body.ltr * {
    direction: inherit;
  }
}

@layer base {
  /* Règles globales pour RTL */
  [dir="rtl"] {
    /* Inverser les images qui ont besoin de l'être en RTL */
    img.rtl-mirror,
    svg.rtl-mirror {
      transform: scaleX(-1);
    }
    
    /* Inverser les icônes de direction */
    .icon-arrow-left,
    .icon-chevron-left,
    .icon-back {
      transform: scaleX(-1);
    }
    
    .icon-arrow-right,
    .icon-chevron-right,
    .icon-forward {
      transform: scaleX(-1);
    }
  }
}

@layer components {
  /* Composants de mise en page pour RTL */
  .rtl-aware-layout {
    @apply rtl:flex-row-reverse;
  }
  
  /* Navigation adaptée au RTL */
  .rtl-nav-item {
    @apply ltr:ml-4 rtl:mr-4 ltr:pl-2 rtl:pr-2;
  }
  
  /* Composants avec des ombres directionnelles */
  .shadow-directional {
    @apply ltr:shadow-r-md rtl:shadow-l-md;
  }
}

/* Styles pour les éléments avec positions fixes/absolues */
[dir="rtl"] .fixed-element,
[dir="rtl"] .absolute-element {
  right: auto;
  left: 0;
}

/* Styles pour les conteneurs de débordement */
[dir="rtl"] .overflow-container {
  direction: rtl;
}
[dir="rtl"] .overflow-content {
  direction: ltr;
}
