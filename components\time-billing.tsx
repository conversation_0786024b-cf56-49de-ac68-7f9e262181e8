"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as UICalendar } from "@/components/ui/calendar" // Renamed to avoid conflict
import { getTranslations } from "@/lib/i18n/translations"
import { FileText, Plus, Trash2, Download, Calendar as CalendarIcon } from "lucide-react" // Renamed Calendar icon
import { format } from "date-fns"
import { useRTL } from "@/hooks/useRTL" // Import du hook useRTL
import { ToolControls } from "@/components/tool-controls"
import { ToolFullscreenWrapper } from "@/components/tool-fullscreen-wrapper"
import {
  fr, enUS, es, de, it, pt, nl, pl, uk, tr, ru, ar, he,
  id, ms, th, vi, zhC<PERSON> as zh, ja, ko, el, bg, cs, sk, hu, ro,
  hr, sr, bs, sl, et, lv, lt, da, fi, nb, sv, ca, af
} from "date-fns/locale"

// Clé de stockage local
const BILLING_ENTRIES_KEY = "timetools_billing_entries"

// Fonctions utilitaires pour le stockage local
function saveToLocalStorage<T>(key: string, data: T): void {
  try {
    if (typeof window !== "undefined") {
      const serializedData = JSON.stringify(data)
      localStorage.setItem(key, serializedData)
    }
  } catch (error) {
    console.error("Error saving to localStorage:", error)
  }
}

function getFromLocalStorage<T>(key: string, defaultValue: T): T {
  try {
    if (typeof window !== "undefined") {
      const serializedData = localStorage.getItem(key)
      if (serializedData === null) {
        return defaultValue
      }
      return JSON.parse(serializedData) as T
    }
    return defaultValue
  } catch (error) {
    console.error("Error getting from localStorage:", error)
    return defaultValue
  }
}

interface TimeBillingProps {
  lang: string
}

interface TimeEntry {
  id: string
  date: string
  client: string
  project: string
  description: string
  hours: number
  rate: number
}

export function TimeBilling({ lang }: TimeBillingProps) {
  const t = getTranslations(lang)
  const isRTLLayout = useRTL(lang) // Utilisation du hook useRTL pour détecter les langues RTL
  const [entries, setEntries] = useState<TimeEntry[]>([]) // Initialize empty, load from storage
  const [isToolFullscreen, setIsToolFullscreen] = useState(false)

  // Locale pour date-fns
  const getLocale = () => {
    switch (lang) {
      case "fr": return fr;
      case "es": return es;
      case "de": return de;
      case "it": return it;
      case "pt": return pt;
      case "nl": return nl;
      case "pl": return pl;
      case "uk": return uk;
      case "tr": return tr;
      case "ru": return ru;
      case "ar": return ar;
      case "he": return he;
      case "id": return id;
      case "ms": return ms;
      case "th": return th;
      case "vi": return vi;
      case "zh": return zh;
      case "ja": return ja;
      case "ko": return ko;
      case "el": return el;
      case "bg": return bg;
      case "cs": return cs;
      case "sk": return sk;
      case "hu": return hu;
      case "ro": return ro;
      case "hr": return hr;
      case "sr": return sr;
      case "bs": return bs;
      case "sl": return sl;
      case "et": return et;
      case "lv": return lv;
      case "lt": return lt;
      case "da": return da;
      case "fi": return fi;
      case "nb": return nb;
      case "sv": return sv;
      case "ca": return ca;
      case "af": return af;
      default: return enUS;
    }
  }

  const [newEntry, setNewEntry] = useState<Omit<TimeEntry, "id">>({
    date: new Date().toISOString().split("T")[0], // Keep as string for initial state
    client: "",
    project: "",
    description: "",
    hours: 0,
    rate: 75, // Default rate, adjust as needed
  })

  // State for the calendar date picker
  const [calendarDate, setCalendarDate] = useState<Date | undefined>(new Date())

  // Charger les entrées depuis localStorage
  useEffect(() => {
    const savedEntries = getFromLocalStorage<TimeEntry[]>(BILLING_ENTRIES_KEY, [])
    // Example data if storage is empty (optional)
    if (savedEntries.length === 0) {
       setEntries([
         {
           id: "1",
           date: "2023-05-15",
           client: "Acme Inc.",
           project: "Website",
           description: "Frontend development",
           hours: 4.5,
           rate: 75,
         },
         {
           id: "2",
           date: "2023-05-16",
           client: "Acme Inc.",
           project: "Website",
           description: "API integration",
           hours: 3,
           rate: 75,
         },
         {
           id: "3",
           date: "2023-05-17",
           client: "Globex Corp",
           project: "Mobile app",
           description: "UI design",
           hours: 6,
           rate: 85,
         },
       ]);
    } else {
       setEntries(savedEntries);
    }
    // Set initial calendar date based on newEntry date string
    setCalendarDate(new Date(newEntry.date))
  }, []) // Removed dependencies to only run once on mount

  // Sauvegarder les entrées dans localStorage quand elles changent
  useEffect(() => {
    // Avoid saving the initial example data if it wasn't modified
    // Check if the first entry exists and if its client is not the default example one
    if (entries.length > 0 && entries[0]?.client !== "Acme Inc.") {
       saveToLocalStorage(BILLING_ENTRIES_KEY, entries)
    }
  }, [entries])

  // Update newEntry.date when calendarDate changes
  useEffect(() => {
    if (calendarDate) {
      setNewEntry(prev => ({ ...prev, date: format(calendarDate, "yyyy-MM-dd") }))
    }
  }, [calendarDate])


  const addEntry = () => {
    if (
      newEntry.client.trim() === "" ||
      newEntry.project.trim() === "" ||
      newEntry.description.trim() === "" ||
      newEntry.hours <= 0
    ) {
      return
    }

    const entry: TimeEntry = {
      id: Date.now().toString(),
      ...newEntry,
    }

    setEntries([...entries, entry])
    setNewEntry({
      date: new Date().toISOString().split("T")[0],
      client: "",
      project: "",
      description: "",
      hours: 0,
      rate: 75,
    })
  }

  const removeEntry = (id: string) => {
    setEntries(entries.filter((entry) => entry.id !== id))
  }

  const calculateTotal = () => {
    return entries.reduce((total, entry) => {
      return total + entry.hours * entry.rate
    }, 0)
  }

  const calculateHours = () => {
    return entries.reduce((total, entry) => {
      return total + entry.hours
    }, 0)
  }

  const getUniqueClients = () => {
    return [...new Set(entries.map((entry) => entry.client))]
  }

  const getUniqueProjects = () => {
    return [...new Set(entries.map((entry) => entry.project))]
  }

  const exportCSV = () => {
    const headers = [t.date, t.client, t.project, t.description, t.hours, t.rate, t.amount]
    const rows = entries.map((entry) => [
      entry.date,
      entry.client,
      entry.project,
      entry.description,
      entry.hours.toString(),
      entry.rate.toString(),
      (entry.hours * entry.rate).toString(),
    ])

    const csvContent = [headers.join(","), ...rows.map((row) => row.join(","))].join("\n")

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
    const url = URL.createObjectURL(blob)

    const link = document.createElement("a")
    link.setAttribute("href", url)
    link.setAttribute("download", `time-billing-export-${new Date().toISOString().split("T")[0]}.csv`)
    link.style.visibility = "hidden"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const mainContent = (
    <CardContent>
        <Tabs defaultValue="entries" className="mb-6">
          <TabsList className="grid grid-cols-2 w-full mb-6">
            <TabsTrigger value="entries" className="flex items-center gap-2">
              <CalendarIcon className="h-4 w-4" /> {/* Corrected icon name */}
              {t.entries}
            </TabsTrigger>
            <TabsTrigger value="new" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              {t.newEntry}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="entries">
            <div className="flex justify-between items-center mb-4">
              <div>
                <h3 className="font-medium">{t.timeEntries}</h3>
                <p className="text-sm text-muted-foreground">
                  {entries.length} {t.entries}, {calculateHours().toFixed(1)} {t.hours}
                </p>
              </div>
              <Button variant="outline" onClick={exportCSV} className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                {t.exportCSV}
              </Button>
            </div>

            {entries.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground border rounded-md">{t.noEntriesYet}</div>
            ) : (
              <div className="space-y-3">
                {entries.map((entry) => (
                  <div key={entry.id} className="p-4 border rounded-md">
                    <div className="flex flex-col sm:flex-row justify-between items-start">
                      <div>
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium">{entry.client}</h4>
                          <span className="text-sm text-muted-foreground">|</span>
                          <p className="text-sm">{entry.project}</p>
                        </div>
                        <p className="text-sm text-muted-foreground">{entry.description}</p>
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => removeEntry(entry.id)}
                        className="h-8 w-8 text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">{t.delete}</span>
                      </Button>
                    </div>
                    <div className="flex flex-col sm:flex-row justify-between items-center mt-2 text-sm">
                      <p>{new Date(entry.date).toLocaleDateString(lang)}</p>
                      <div className="flex items-center gap-4">
                        <p>
                          {entry.hours} {t.hours}
                        </p>
                        <p>${entry.rate}/h</p>
                        <p className="font-medium">
                          {new Intl.NumberFormat(lang, { style: "currency", currency: "USD" }).format(
                            entry.hours * entry.rate,
                          )}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}

                <div className="flex justify-between items-center p-4 border-t mt-4">
                  <p className="font-medium">{t.total}</p>
                  <p className="text-xl font-bold">
                    {new Intl.NumberFormat(lang, { style: "currency", currency: "USD" }).format(calculateTotal())}
                  </p>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="new">
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  {/* Removed duplicated Label and Input tag start */}
                  <Label htmlFor="entry-date">{t.date}</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                          variant={"outline"}
                          className={`w-full ${isRTLLayout ? "justify-end text-right" : "justify-start text-left"} font-normal`}
                        >
                          <CalendarIcon className={isRTLLayout ? "ml-2 h-4 w-4" : "mr-2 h-4 w-4"} />
                          {calendarDate ? (
                            format(calendarDate, "PPP", { locale: getLocale() })
                          ) : (
                            <span>{t.pickDate || "Pick a date"}</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" side="bottom" align={isRTLLayout ? "end" : "start"}>
                        <UICalendar
                          mode="single"
                          selected={calendarDate}
                          onSelect={(date) => setCalendarDate(date ?? undefined)} // Corrected onSelect handler type
                          initialFocus
                          lang={lang} // Pass the language prop here
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  <div>
                  <Label htmlFor="entry-client">{t.client}</Label>
                  <Input
                    id="entry-client"
                    placeholder={t.clientName}
                    value={newEntry.client}
                    onChange={(e) => setNewEntry({ ...newEntry, client: e.target.value })}
                    list="clients"
                    className={isRTLLayout ? "text-right" : ""}
                    dir={isRTLLayout ? "rtl" : "ltr"}
                  />
                  <datalist id="clients">
                    {getUniqueClients().map((client, index) => (
                      <option key={index} value={client} />
                    ))}
                  </datalist>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="entry-project">{t.project}</Label>
                  <Input
                    id="entry-project"
                    placeholder={t.projectName}
                    value={newEntry.project}
                    onChange={(e) => setNewEntry({ ...newEntry, project: e.target.value })}
                    list="projects"
                    className={isRTLLayout ? "text-right" : ""}
                    dir={isRTLLayout ? "rtl" : "ltr"}
                  />
                  <datalist id="projects">
                    {getUniqueProjects().map((project, index) => (
                      <option key={index} value={project} />
                    ))}
                  </datalist>
                </div>
                <div>
                  <Label htmlFor="entry-description">{t.description}</Label>
                  <Input
                    id="entry-description"
                    placeholder={t.taskDescription}
                    value={newEntry.description}
                    onChange={(e) => setNewEntry({ ...newEntry, description: e.target.value })}
                    className={isRTLLayout ? "text-right" : ""}
                    dir={isRTLLayout ? "rtl" : "ltr"}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="entry-hours">{t.hours}</Label>
                  <Input
                    id="entry-hours"
                    type="number"
                    min="0.1"
                    step="0.1"
                    value={newEntry.hours || ""}
                    onChange={(e) => setNewEntry({ ...newEntry, hours: Number.parseFloat(e.target.value) || 0 })}
                    className={isRTLLayout ? "text-right" : ""}
                    dir={isRTLLayout ? "rtl" : "ltr"}
                  />
                </div>
                <div>
                  <Label htmlFor="entry-rate">{t.hourlyRateDollar}</Label>
                  <Input
                    id="entry-rate"
                    type="number"
                    min="0"
                    value={newEntry.rate}
                    onChange={(e) => setNewEntry({ ...newEntry, rate: Number.parseInt(e.target.value) || 0 })}
                    className={isRTLLayout ? "text-right" : ""}
                    dir={isRTLLayout ? "rtl" : "ltr"}
                  />
                </div>
              </div>

              <Button
                onClick={addEntry}
                className="w-full"
                disabled={
                  newEntry.client.trim() === "" ||
                  newEntry.project.trim() === "" ||
                  newEntry.description.trim() === "" ||
                  newEntry.hours <= 0
                }
              >
                <Plus className={`h-4 w-4 ${isRTLLayout ? "ml-2" : "mr-2"}`} />
                {t.addEntry || "Add Entry"}
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
  )

  return (
    <>
      <Card className="w-full max-w-6xl mx-auto">
        <CardHeader>
          <div className="flex justify-between items-start">
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {t.timeBilling}
            </CardTitle>
            {/* Contrôles d'outils en haut à droite */}
            <ToolControls
              onFullscreenToggle={setIsToolFullscreen}
              variant="compact"
            />
          </div>
        </CardHeader>
        {mainContent}
      </Card>

      {/* Wrapper plein écran */}
      <ToolFullscreenWrapper
        isFullscreen={isToolFullscreen}
        onClose={() => setIsToolFullscreen(false)}
        toolName={t.timeBilling}
      >
        <Card className="w-full border-0 shadow-none bg-transparent">
          {mainContent}
        </Card>
      </ToolFullscreenWrapper>
    </>
  )
}
