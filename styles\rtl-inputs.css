/* Styles spécifiques pour les champs de texte en mode RTL */

/* Style de base pour tous les champs de texte */
.rtl input[type="text"],
.rtl input[type="email"],
.rtl input[type="password"],
.rtl input[type="search"],
.rtl input[type="tel"],
.rtl input[type="url"],
.rtl input[type="number"],
.rtl input:not([type]),
.rtl textarea,
.rtl .form-input,
.rtl .input,
.rtl [role="textbox"] {
  direction: rtl !important;
  text-align: right !important;
}

/* Ajuster les placeholders */
.rtl input::placeholder,
.rtl textarea::placeholder {
  text-align: right;
  direction: rtl;
}

/* Ajuster les icônes dans les champs de texte */
.rtl .input-with-icon-left .icon,
.rtl .input-with-icon-left svg {
  left: auto;
  right: 0.5rem;
}

.rtl .input-with-icon-right .icon,
.rtl .input-with-icon-right svg {
  right: auto;
  left: 0.5rem;
}

/* Ajuster les labels */
.rtl label,
.rtl .form-label {
  text-align: right;
  direction: rtl;
}

/* Ajuster les descriptions */
.rtl .form-description,
.rtl .input-description {
  text-align: right;
  direction: rtl;
}

/* Ajuster les messages d'erreur */
.rtl .form-message,
.rtl .input-message,
.rtl .error-message {
  text-align: right;
  direction: rtl;
}

/* Ajuster les groupes de formulaires */
.rtl .form-group,
.rtl .input-group,
.rtl .form-control {
  text-align: right;
  direction: rtl;
}

/* Ajuster les champs de texte avec des boutons */
.rtl .input-with-button .button {
  right: auto;
  left: 0;
}

/* Ajuster les champs de texte avec des badges */
.rtl .input-with-badge .badge {
  right: auto;
  left: 0.5rem;
}

/* Ajuster les champs de texte avec des tooltips */
.rtl .input-with-tooltip .tooltip {
  text-align: right;
}

/* Ajuster les champs de texte avec des popups */
.rtl .input-with-popup .popup {
  text-align: right;
}

/* Ajuster les champs de texte avec des menus */
.rtl .input-with-menu .menu {
  text-align: right;
}

/* Ajuster les champs de texte avec des dropdowns */
.rtl .input-with-dropdown .dropdown {
  text-align: right;
}

/* Ajuster les champs de texte avec des modals */
.rtl .input-with-modal .modal {
  text-align: right;
}

/* Ajuster les champs de texte avec des alerts */
.rtl .input-with-alert .alert {
  text-align: right;
}

/* Ajuster les champs de texte avec des notifications */
.rtl .input-with-notification .notification {
  text-align: right;
}

/* Ajuster les champs de texte avec des toasts */
.rtl .input-with-toast .toast {
  text-align: right;
}

/* Ajuster les champs de texte avec des carousels */
.rtl .input-with-carousel .carousel {
  direction: rtl;
}

/* Ajuster les champs de texte avec des sliders */
.rtl .input-with-slider .slider {
  direction: rtl;
}

/* Ajuster les champs de texte avec des ratings */
.rtl .input-with-rating .rating {
  flex-direction: row-reverse;
}

/* Ajuster les champs de texte avec des pagination */
.rtl .input-with-pagination .pagination {
  flex-direction: row-reverse;
}

/* Ajuster les champs de texte avec des breadcrumbs */
.rtl .input-with-breadcrumb .breadcrumb {
  flex-direction: row-reverse;
}

/* Ajuster les champs de texte avec des stepper */
.rtl .input-with-stepper .stepper {
  flex-direction: row-reverse;
}

/* Ajuster les champs de texte avec des timeline */
.rtl .input-with-timeline .timeline {
  text-align: right;
}

/* Ajuster les champs de texte avec des tree view */
.rtl .input-with-tree .tree {
  text-align: right;
}

/* Ajuster les champs de texte avec des data tables */
.rtl .input-with-data-table .data-table {
  text-align: right;
}

/* Ajuster les champs de texte avec des calendars */
.rtl .input-with-calendar .calendar {
  direction: rtl;
}

/* Ajuster les champs de texte avec des date pickers */
.rtl .input-with-date-picker .date-picker {
  direction: rtl;
}

/* Ajuster les champs de texte avec des time pickers */
.rtl .input-with-time-picker .time-picker {
  direction: rtl;
}

/* Ajuster les champs de texte avec des color pickers */
.rtl .input-with-color-picker .color-picker {
  direction: rtl;
}

/* Ajuster les champs de texte avec des file uploads */
.rtl .input-with-file-upload .file-upload {
  text-align: right;
}

/* Ajuster les champs de texte avec des rich text editors */
.rtl .input-with-rich-text-editor .rich-text-editor {
  text-align: right;
  direction: rtl;
}

/* Ajuster les champs de texte avec des code editors */
.rtl .input-with-code-editor .code-editor {
  text-align: right;
  direction: rtl;
}

/* Ajuster les champs de texte avec des markdown */
.rtl .input-with-markdown .markdown {
  text-align: right;
  direction: rtl;
}

/* Ajuster les champs de texte avec des charts */
.rtl .input-with-chart .chart {
  direction: rtl;
}

/* Ajuster les champs de texte avec des maps */
.rtl .input-with-map .map {
  direction: rtl;
}

/* Ajuster les champs de texte avec des videos */
.rtl .input-with-video .video {
  direction: rtl;
}

/* Ajuster les champs de texte avec des audios */
.rtl .input-with-audio .audio {
  direction: rtl;
}

/* Ajuster les champs de texte avec des images */
.rtl .input-with-image .image {
  direction: rtl;
}

/* Ajuster les champs de texte avec des avatars */
.rtl .input-with-avatar .avatar {
  direction: rtl;
}

/* Ajuster les champs de texte avec des badges */
.rtl .input-with-badge .badge {
  direction: rtl;
}

/* Ajuster les champs de texte avec des tooltips */
.rtl .input-with-tooltip .tooltip {
  direction: rtl;
}

/* Ajuster les champs de texte avec des popups */
.rtl .input-with-popup .popup {
  direction: rtl;
}

/* Ajuster les champs de texte avec des menus */
.rtl .input-with-menu .menu {
  direction: rtl;
}

/* Ajuster les champs de texte avec des dropdowns */
.rtl .input-with-dropdown .dropdown {
  direction: rtl;
}

/* Ajuster les champs de texte avec des modals */
.rtl .input-with-modal .modal {
  direction: rtl;
}

/* Ajuster les champs de texte avec des alerts */
.rtl .input-with-alert .alert {
  direction: rtl;
}

/* Ajuster les champs de texte avec des notifications */
.rtl .input-with-notification .notification {
  direction: rtl;
}

/* Ajuster les champs de texte avec des toasts */
.rtl .input-with-toast .toast {
  direction: rtl;
}

/* Ajuster les champs de texte avec des carousels */
.rtl .input-with-carousel .carousel {
  direction: rtl;
}

/* Ajuster les champs de texte avec des sliders */
.rtl .input-with-slider .slider {
  direction: rtl;
}

/* Ajuster les champs de texte avec des ratings */
.rtl .input-with-rating .rating {
  direction: rtl;
}

/* Ajuster les champs de texte avec des pagination */
.rtl .input-with-pagination .pagination {
  direction: rtl;
}

/* Ajuster les champs de texte avec des breadcrumbs */
.rtl .input-with-breadcrumb .breadcrumb {
  direction: rtl;
}

/* Ajuster les champs de texte avec des stepper */
.rtl .input-with-stepper .stepper {
  direction: rtl;
}

/* Ajuster les champs de texte avec des timeline */
.rtl .input-with-timeline .timeline {
  direction: rtl;
}

/* Ajuster les champs de texte avec des tree view */
.rtl .input-with-tree .tree {
  direction: rtl;
}

/* Ajuster les champs de texte avec des data tables */
.rtl .input-with-data-table .data-table {
  direction: rtl;
}

/* Ajuster les champs de texte avec des calendars */
.rtl .input-with-calendar .calendar {
  direction: rtl;
}

/* Ajuster les champs de texte avec des date pickers */
.rtl .input-with-date-picker .date-picker {
  direction: rtl;
}

/* Ajuster les champs de texte avec des time pickers */
.rtl .input-with-time-picker .time-picker {
  direction: rtl;
}

/* Ajuster les champs de texte avec des color pickers */
.rtl .input-with-color-picker .color-picker {
  direction: rtl;
}

/* Ajuster les champs de texte avec des file uploads */
.rtl .input-with-file-upload .file-upload {
  direction: rtl;
}

/* Ajuster les champs de texte avec des rich text editors */
.rtl .input-with-rich-text-editor .rich-text-editor {
  direction: rtl;
}

/* Ajuster les champs de texte avec des code editors */
.rtl .input-with-code-editor .code-editor {
  direction: rtl;
}

/* Ajuster les champs de texte avec des markdown */
.rtl .input-with-markdown .markdown {
  direction: rtl;
}

/* Ajuster les champs de texte avec des charts */
.rtl .input-with-chart .chart {
  direction: rtl;
}

/* Ajuster les champs de texte avec des maps */
.rtl .input-with-map .map {
  direction: rtl;
}

/* Ajuster les champs de texte avec des videos */
.rtl .input-with-video .video {
  direction: rtl;
}

/* Ajuster les champs de texte avec des audios */
.rtl .input-with-audio .audio {
  direction: rtl;
}

/* Ajuster les champs de texte avec des images */
.rtl .input-with-image .image {
  direction: rtl;
}
