import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const COUNTER_FILE_PATH = path.join(process.cwd(), 'data', 'install-counter.json');

interface InstallCounter {
  totalInstalls: number;
  lastUpdated: string | null;
  installHistory: Array<{
    timestamp: string;
    userAgent?: string;
    language?: string;
  }>;
}

// Fonction pour lire le compteur
function readCounter(): InstallCounter {
  try {
    if (!fs.existsSync(COUNTER_FILE_PATH)) {
      // Créer le fichier s'il n'existe pas
      const initialData: InstallCounter = {
        totalInstalls: 0,
        lastUpdated: null,
        installHistory: []
      };
      fs.writeFileSync(COUNTER_FILE_PATH, JSON.stringify(initialData, null, 2));
      return initialData;
    }
    
    const data = fs.readFileSync(COUNTER_FILE_PATH, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Erreur lors de la lecture du compteur:', error);
    return {
      totalInstalls: 0,
      lastUpdated: null,
      installHistory: []
    };
  }
}

// Fonction pour écrire le compteur
function writeCounter(data: InstallCounter): boolean {
  try {
    // Créer le dossier data s'il n'existe pas
    const dataDir = path.dirname(COUNTER_FILE_PATH);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    fs.writeFileSync(COUNTER_FILE_PATH, JSON.stringify(data, null, 2));
    return true;
  } catch (error) {
    console.error('Erreur lors de l\'écriture du compteur:', error);
    return false;
  }
}

// GET - Récupérer le nombre d'installations
export async function GET() {
  try {
    const counter = readCounter();
    
    return NextResponse.json({
      totalInstalls: counter.totalInstalls,
      lastUpdated: counter.lastUpdated
    }, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    });
  } catch (error) {
    console.error('Erreur lors de la récupération du compteur:', error);
    return NextResponse.json(
      { error: 'Erreur lors de la récupération du compteur' },
      { status: 500 }
    );
  }
}

// POST - Incrémenter le compteur d'installations
export async function POST(request: NextRequest) {
  try {
    const body = await request.json().catch(() => ({}));
    const userAgent = request.headers.get('user-agent') || undefined;
    const acceptLanguage = request.headers.get('accept-language') || undefined;
    
    const counter = readCounter();
    
    // Incrémenter le compteur
    counter.totalInstalls += 1;
    counter.lastUpdated = new Date().toISOString();
    
    // Ajouter à l'historique (garder seulement les 100 dernières installations)
    counter.installHistory.push({
      timestamp: new Date().toISOString(),
      userAgent,
      language: acceptLanguage
    });
    
    // Garder seulement les 100 dernières installations
    if (counter.installHistory.length > 100) {
      counter.installHistory = counter.installHistory.slice(-100);
    }
    
    // Sauvegarder
    const success = writeCounter(counter);
    
    if (!success) {
      return NextResponse.json(
        { error: 'Erreur lors de la sauvegarde' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      totalInstalls: counter.totalInstalls,
      message: 'Installation enregistrée avec succès'
    });
    
  } catch (error) {
    console.error('Erreur lors de l\'incrémentation du compteur:', error);
    return NextResponse.json(
      { error: 'Erreur lors de l\'incrémentation du compteur' },
      { status: 500 }
    );
  }
}
