'use client';

import { useEffect } from 'react';
import { useLanguage } from '@/components/language-provider';
import { getTranslations } from '@/lib/i18n/translations';
import { getTranslatedRoute } from '@/lib/i18n/route-translations';
import { languages, isRtlLang } from '@/lib/i18n/languages';
import { usePathname } from 'next/navigation';

/**
 * Component that dynamically updates the page title when the language changes
 */
export function DynamicPageTitle() {
  const { language } = useLanguage();
  const pathname = usePathname();

  useEffect(() => {
    // Get the current route to determine which page we're on
    const segments = pathname.split('/');
    const currentLang = segments.length > 1 ? segments[1] : '';

    // Make sure to decode the URL segment for non-ASCII characters (important for RTL languages)
    const routeSegment = segments.length > 2
      ? decodeURIComponent(segments[2])
      : '';

    // Verify if the current language is valid
    const isValidLang = languages.some(lang => lang.code === currentLang);

    // Use the language from context if URL language is not valid
    const effectiveLanguage = isValidLang ? currentLang : language;

    // If neither is valid, log an error and use English as fallback
    if (!effectiveLanguage) {
      console.error(`[DynamicPageTitle] Invalid language in URL and context: ${currentLang}, ${language}`);
      return;
    }

    // Get translations for the effective language
    const t = getTranslations(effectiveLanguage);

    // Set the appropriate title based on the current route
    let pageTitle = t.homePageTitle || 'Timer Kit';

    // Define all available routes
    const availableRoutes = [
      'timer', 'countdown', 'todo', 'time-tracking', 'world-clock',
      'intervals', 'pomodoro', 'meeting-timer', 'time-billing',
      'workout-intervals', 'exercise-templates'
    ];

    // Find the original route (English version) for the current URL segment
    let originalRoute = '';

    // First check if the route segment is already an original route
    if (availableRoutes.includes(routeSegment)) {
      originalRoute = routeSegment;
    } else {
      // Try to find the original route by checking all translations
      for (const route of availableRoutes) {
        const translatedRoute = getTranslatedRoute(effectiveLanguage, route);
        if (translatedRoute === routeSegment) {
          originalRoute = route;
          break;
        }
      }
    }

    // Set the page title based on the original route
    if (originalRoute) {
      switch (originalRoute) {
        case 'timer':
          pageTitle = t.timerPageTitle || 'Timer - Timer Kit';
          break;
        case 'countdown':
          pageTitle = t.countdownPageTitle || 'Countdown - Timer Kit';
          break;
        case 'todo':
          pageTitle = t.todoPageTitle || 'Todo List - Timer Kit';
          break;
        case 'time-tracking':
          pageTitle = t.timeTrackingPageTitle || 'Time Tracking - Timer Kit';
          break;
        case 'world-clock':
          pageTitle = t.worldClockPageTitle || 'World Clock - Timer Kit';
          break;
        case 'intervals':
          pageTitle = t.intervalsPageTitle || 'Intervals - Timer Kit';
          break;
        case 'pomodoro':
          pageTitle = t.pomodoroPageTitle || 'Pomodoro - Timer Kit';
          break;
        case 'meeting-timer':
          pageTitle = t.meetingTimerPageTitle || 'Meeting Timer - Timer Kit';
          break;
        case 'time-billing':
          pageTitle = t.timeBillingPageTitle || 'Time Billing - Timer Kit';
          break;
        case 'workout-intervals':
          pageTitle = t.workoutIntervalsPageTitle || 'Workout Intervals - Timer Kit';
          break;
        case 'exercise-templates':
          pageTitle = t.exerciseTemplatesPageTitle || 'Exercise Templates - Timer Kit';
          break;
      }
    }

    // Check if we're using an RTL language
    const isRtl = isRtlLang(effectiveLanguage);

    // For RTL languages, we need to ensure the title is properly displayed
    // Some browsers might need special handling for RTL titles
    if (isRtl) {
      // Add a zero-width space character at the beginning to ensure proper RTL rendering
      // This helps some browsers correctly display RTL text in the title
      const rtlPageTitle = '\u200F' + pageTitle;
      document.title = rtlPageTitle;

      // Force a re-render of the title by setting it again after a short delay
      // This helps with some browser quirks in RTL title rendering
      setTimeout(() => {
        document.title = rtlPageTitle;
      }, 50);
    } else {
      // For LTR languages, just set the title normally
      document.title = pageTitle;
    }

    console.log(`[DynamicPageTitle] Updated title to: "${pageTitle}" for language: ${effectiveLanguage} (RTL: ${isRtl}), route: ${originalRoute || routeSegment}`);
  }, [pathname, language]);

  // This component doesn't render anything
  return null;
}


