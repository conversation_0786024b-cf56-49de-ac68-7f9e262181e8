"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { getTranslations, formatTranslation } from "@/lib/i18n/translations"
import { Dumbbell, Plus, Trash2, Copy, Clock, Activity, Edit } from "lucide-react"
import { useSound } from "@/components/sound-provider"
import { Badge } from "@/components/ui/badge"
import { ToolControls } from "@/components/tool-controls"
import { ToolFullscreenWrapper } from "@/components/tool-fullscreen-wrapper"

// --- Interfaces ---
export interface Exercise {
  id: string
  name: string
  sets: number
  reps: number
  rest: number // in seconds
}

export interface WorkoutTemplate {
  id: string
  name: string
  description: string
  exercises: Exercise[]
}

interface ExerciseTemplatesProps {
  lang: string
}

// --- Storage Constants and Utilities ---
const STORAGE_KEYS = {
  EXERCISE_TEMPLATES: "workout-templates-v1",
}

// Generic localStorage functions
function getFromLocalStorage<T>(key: string, defaultValue: T): T {
  if (typeof window === "undefined") return defaultValue

  try {
    const item = window.localStorage.getItem(key)
    if (!item) return defaultValue

    return JSON.parse(item) as T
  } catch (error) {
    console.error(`Error retrieving ${key} from localStorage:`, error)
    return defaultValue
  }
}

function saveToLocalStorage<T>(key: string, value: T): void {
  if (typeof window === "undefined") return

  try {
    window.localStorage.setItem(key, JSON.stringify(value))
  } catch (error) {
    console.error(`Error saving ${key} to localStorage:`, error)
  }
}

function removeFromLocalStorage(key: string): void {
  if (typeof window === "undefined") return

  try {
    window.localStorage.removeItem(key)
  } catch (error) {
    console.error(`Error removing ${key} from localStorage:`, error)
  }
}

// --- Default Templates ---
const defaultTemplates: WorkoutTemplate[] = [
  {
    id: "default-1",
    name: "Full Body Workout",
    description: "A complete full body workout for beginners",
    exercises: [
      { id: "ex-1", name: "Squats", sets: 3, reps: 12, rest: 60 },
      { id: "ex-2", name: "Push-ups", sets: 3, reps: 10, rest: 60 },
      { id: "ex-3", name: "Lunges", sets: 3, reps: 10, rest: 60 },
      { id: "ex-4", name: "Plank", sets: 3, reps: 30, rest: 60 },
    ],
  },
  {
    id: "default-2",
    name: "Upper Body Workout",
    description: "Focused on the upper body",
    exercises: [
      { id: "ex-5", name: "Push-ups", sets: 4, reps: 12, rest: 60 },
      { id: "ex-6", name: "Pull-ups", sets: 3, reps: 8, rest: 90 },
      { id: "ex-7", name: "Dips", sets: 3, reps: 10, rest: 60 },
      { id: "ex-8", name: "Shoulder Press", sets: 3, reps: 12, rest: 60 },
    ],
  },
]

// --- Validation Function ---
function isValidWorkoutTemplate(template: any): template is WorkoutTemplate {
  if (
    !template ||
    typeof template !== "object" ||
    typeof template.id !== "string" ||
    typeof template.name !== "string" ||
    typeof template.description !== "string" ||
    !Array.isArray(template.exercises)
  ) {
    return false
  }

  // Validate each exercise within the template
  return template.exercises.every(
    (ex: any): ex is Exercise =>
      ex &&
      typeof ex === "object" &&
      typeof ex.id === "string" &&
      typeof ex.name === "string" &&
      typeof ex.sets === "number" &&
      ex.sets > 0 &&
      typeof ex.reps === "number" &&
      ex.reps > 0 &&
      typeof ex.rest === "number" &&
      ex.rest >= 0,
  )
}

// --- Main Component ---
export function ExerciseTemplates({ lang }: ExerciseTemplatesProps) {
  const t = getTranslations(lang)
  const { playSound } = useSound()

  // --- State Initialization with Improved Data Loading ---
  const [templates, setTemplates] = useState<WorkoutTemplate[]>(() => {
    // Load templates from localStorage with validation
    const savedData = getFromLocalStorage<WorkoutTemplate[]>(STORAGE_KEYS.EXERCISE_TEMPLATES, defaultTemplates)

    // Validate the loaded data
    if (Array.isArray(savedData)) {
      const validTemplates = savedData.filter(isValidWorkoutTemplate)
      if (validTemplates.length === savedData.length) {
        console.log("Successfully loaded and validated templates from localStorage:", validTemplates)
        return validTemplates
      } else {
        console.warn(
          `Invalid template structures found in localStorage (${savedData.length - validTemplates.length} invalid). Falling back to defaults.`,
        )
        // Clean up bad data
        removeFromLocalStorage(STORAGE_KEYS.EXERCISE_TEMPLATES)
        return defaultTemplates
      }
    } else {
      console.warn("Invalid data format found in localStorage (not an array). Using defaults.")
      removeFromLocalStorage(STORAGE_KEYS.EXERCISE_TEMPLATES)
      return defaultTemplates
    }
  })

  // --- Other States ---
  const [activeTemplate, setActiveTemplate] = useState<WorkoutTemplate | null>(null)
  const [editingTemplate, setEditingTemplate] = useState<WorkoutTemplate | null>(null)
  const [newTemplate, setNewTemplate] = useState<Omit<WorkoutTemplate, "id">>({
    name: "",
    description: "",
    exercises: [],
  })
  const [newExercise, setNewExercise] = useState<Omit<Exercise, "id">>({
    name: "",
    sets: 3,
    reps: 10,
    rest: 60,
  })
  const [activeTab, setActiveTab] = useState("templates")
  const [workoutActive, setWorkoutActive] = useState(false)
  const [currentExerciseIndex, setCurrentExerciseIndex] = useState(0)
  const [currentSetIndex, setCurrentSetIndex] = useState(0)
  const [restTimer, setRestTimer] = useState(0)
  const [restTimerActive, setRestTimerActive] = useState(false)
  const [isToolFullscreen, setIsToolFullscreen] = useState(false)
  const originalTitle = useRef<string>("")

  // Sauvegarder le titre original de la page au chargement du composant
  useEffect(() => {
    originalTitle.current = document.title
    return () => {
      document.title = originalTitle.current
    }
  }, [])

  // Mettre à jour le titre de l'onglet du navigateur avec l'exercice en cours
  useEffect(() => {
    if (workoutActive && activeTemplate) {
      const exercises = activeTemplate.exercises
      const currentExercise = exercises[currentExerciseIndex]

      if (currentExercise) {
        if (restTimerActive) {
          document.title = `${formatTime(restTimer)} - ${t.rest || "Repos"} - ${currentExercise.name}`
        } else {
          const setsRemaining = currentExercise.sets - currentSetIndex
          document.title = `${currentExercise.name} - ${setsRemaining}/${currentExercise.sets} ${t.sets || "séries"}`
        }
      }
    } else {
      document.title = originalTitle.current
    }
  }, [workoutActive, activeTemplate, currentExerciseIndex, currentSetIndex, restTimerActive, restTimer, t])

  // --- Worker Ref ---
  const countdownWorkerRef = useRef<Worker | null>(null)
  // Référence pour suivre si le son a déjà été joué
  const soundPlayedRef = useRef(false)

  // --- Initialize Worker ---
  useEffect(() => {
    // Réinitialiser la référence de son joué
    soundPlayedRef.current = false

    // Create the worker only in browser environment
    if (typeof window !== "undefined") {
      try {
        countdownWorkerRef.current = new Worker("/workers/countdown-worker.js")
        console.log("Countdown Worker initialized for ExerciseTemplates.")

        countdownWorkerRef.current.onmessage = (event: MessageEvent) => {
          const { type, timeLeft } = event.data
          if (type === "tick") {
            const remainingSeconds = Math.max(0, Math.round(timeLeft / 1000))
            setRestTimer(remainingSeconds)
            if (remainingSeconds <= 0 && !soundPlayedRef.current) {
              // Utiliser une référence pour éviter les déclenchements multiples
              countdownWorkerRef.current?.postMessage({ command: "stop" })

              // Désactiver le timer et jouer le son une seule fois
              setRestTimerActive(false)
              // Marquer que le son a été joué
              soundPlayedRef.current = true

              // Jouer le son une seule fois quand le timer atteint zéro
              try {
                playSound("bell")
              } catch (error) {
                console.warn("Could not play sound:", error)
              }
              console.log("Timer finished! Timer deactivated and sound played.")
            }
          } else {
            console.log("Received non-tick message from worker:", event.data)
          }
        }
      } catch (error) {
        console.error("Failed to initialize countdown worker:", error)
      }
    }

    return () => {
      if (countdownWorkerRef.current) {
        console.log("Terminating countdown worker for ExerciseTemplates...")
        countdownWorkerRef.current.postMessage({ command: "stop" })
        countdownWorkerRef.current.terminate()
        countdownWorkerRef.current = null
      }
    }
  }, [playSound])

  // --- Persist Templates to localStorage ---
  useEffect(() => {
    saveToLocalStorage(STORAGE_KEYS.EXERCISE_TEMPLATES, templates)
  }, [templates])

  // --- Template Management Functions ---
  const selectTemplate = (template: WorkoutTemplate) => {
    setActiveTemplate(template)
    if (workoutActive) {
      stopWorkout()
    }
  }

  const startEditingTemplate = (template: WorkoutTemplate) => {
    // Create a deep copy to avoid direct state mutation
    setEditingTemplate(JSON.parse(JSON.stringify(template)))
    setActiveTab("edit")
    setNewExercise({ name: "", sets: 3, reps: 10, rest: 60 })
  }

  const saveEditedTemplate = () => {
    if (!editingTemplate) return

    setTemplates((prevTemplates) => prevTemplates.map((t) => (t.id === editingTemplate.id ? editingTemplate : t)))

    if (activeTemplate?.id === editingTemplate.id) {
      setActiveTemplate(editingTemplate)
    }

    setEditingTemplate(null)
    setActiveTab("templates")
  }

  const addExerciseToNewTemplate = () => {
    if (newExercise.name.trim() === "") return

    const exercise: Exercise = {
      id: `ex-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`,
      ...newExercise,
    }

    setNewTemplate((prev) => ({
      ...prev,
      exercises: [...prev.exercises, exercise],
    }))

    setNewExercise({ name: "", sets: 3, reps: 10, rest: 60 })
  }

  const addExerciseToEditingTemplate = () => {
    if (!editingTemplate || newExercise.name.trim() === "") return

    const exercise: Exercise = {
      id: `ex-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`,
      ...newExercise,
    }

    setEditingTemplate((prev) => {
      if (!prev) return null
      const currentExercises = prev.exercises || []
      return { ...prev, exercises: [...currentExercises, exercise] }
    })

    setNewExercise({ name: "", sets: 3, reps: 10, rest: 60 })
  }

  const removeExerciseFromNewTemplate = (id: string) => {
    setNewTemplate((prev) => ({
      ...prev,
      exercises: prev.exercises.filter((exercise) => exercise.id !== id),
    }))
  }

  const removeExerciseFromEditingTemplate = (id: string) => {
    setEditingTemplate((prev) => {
      if (!prev) return null
      return { ...prev, exercises: prev.exercises.filter((exercise) => exercise.id !== id) }
    })
  }

  const saveNewTemplate = () => {
    if (newTemplate.name.trim() === "" || newTemplate.exercises.length === 0) return

    const template: WorkoutTemplate = {
      id: `tmpl-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`,
      ...newTemplate,
    }

    setTemplates((prevTemplates) => [...prevTemplates, template])
    setNewTemplate({ name: "", description: "", exercises: [] })
    setActiveTab("templates")
  }

  const deleteTemplate = (id: string) => {
    setTemplates((prevTemplates) => prevTemplates.filter((template) => template.id !== id))

    if (activeTemplate?.id === id) setActiveTemplate(null)

    if (editingTemplate?.id === id) {
      setEditingTemplate(null)
      setActiveTab("templates")
    }
  }

  const duplicateTemplate = (template: WorkoutTemplate) => {
    const newDupTemplate: WorkoutTemplate = {
      ...JSON.parse(JSON.stringify(template)),
      id: `tmpl-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`,
      name: `${template.name} (${t.copy || "Copy"})`,
    }

    setTemplates((prevTemplates) => [...prevTemplates, newDupTemplate])
  }

  // --- Utility Functions ---
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, "0")}`
  }

  const calculateTotalTime = (exercises: Exercise[]) => {
    return exercises.reduce((total, exercise) => {
      const isTimeBased = exercise.name.toLowerCase().includes("plank")
      const workTimePerSet = isTimeBased ? exercise.reps : exercise.reps * 3
      const totalWorkTime = exercise.sets * workTimePerSet
      const restTimeBetweenSets = (exercise.sets > 1 ? exercise.sets - 1 : 0) * exercise.rest
      const interExerciseRest = exercise.rest
      return total + totalWorkTime + restTimeBetweenSets + interExerciseRest
    }, 0)
  }

  // --- Timer Functions ---
  const startRestTimer = useCallback((durationSeconds: number) => {
    if (countdownWorkerRef.current && durationSeconds > 0) {
      const endTime = Date.now() + durationSeconds * 1000
      setRestTimer(durationSeconds)
      setRestTimerActive(true)
      // Réinitialiser la référence de son joué
      soundPlayedRef.current = false
      countdownWorkerRef.current.postMessage({ command: "start", value: endTime })
      console.log(`Starting rest timer via worker for ${durationSeconds}s`)
    } else {
      setRestTimer(0)
      setRestTimerActive(false)
    }
  }, [])

  const stopRestTimer = useCallback(() => {
    if (countdownWorkerRef.current) {
      countdownWorkerRef.current.postMessage({ command: "stop" })
      console.log("Stopping rest timer via worker")
    }
    setRestTimerActive(false)
    setRestTimer(0)
  }, [])

  // --- Workout Functions ---
  const startWorkout = () => {
    if (!activeTemplate) return
    stopRestTimer()
    setWorkoutActive(true)
    setCurrentExerciseIndex(0)
    setCurrentSetIndex(0)
    setRestTimer(0)
    setRestTimerActive(false)
    try {
      setTimeout(() => playSound("bell"), 50)
    } catch (error) {
      console.warn("Could not play sound:", error)
    }
  }

  const completeSet = () => {
    if (!activeTemplate || !workoutActive) return
    const currentExercise = activeTemplate.exercises[currentExerciseIndex]

    if (!currentExercise) {
      console.error("Workout logic error: Current exercise not found.")
      stopWorkout()
      return
    }

    const isLastSet = currentSetIndex >= currentExercise.sets - 1
    const isLastExercise = currentExerciseIndex >= activeTemplate.exercises.length - 1

    stopRestTimer()

    if (!isLastSet) {
      setCurrentSetIndex(currentSetIndex + 1)
      startRestTimer(currentExercise.rest)
    } else if (!isLastExercise) {
      setCurrentExerciseIndex(currentExerciseIndex + 1)
      setCurrentSetIndex(0)
      startRestTimer(currentExercise.rest)
    } else {
      setWorkoutActive(false)
      try {
        playSound("bell")
      } catch (error) {
        console.warn("Could not play sound:", error)
      }
      console.log("Workout Finished!")
    }
  }

  const stopWorkout = () => {
    setWorkoutActive(false)
    stopRestTimer()
    console.log("Workout Stopped Manually.")
    document.title = originalTitle.current
  }

  // --- Styles pour les onglets en RTL ---
  const rtlTabsStyles = `
    /* Styles pour les onglets en mode RTL */

    /* Force la direction des onglets en mode mobile */
    @media (max-width: 640px) {
      .rtl [role="tablist"] {
        display: flex !important;
        flex-direction: column !important;
      }

      .rtl [role="tab"] {
        width: 100% !important;
        margin-bottom: 8px !important;
      }
    }

    /* Ajuste la direction en mode desktop - INVERSER L'ORDRE DES ONGLETS */
    @media (min-width: 641px) {
      .rtl [role="tablist"] {
        display: flex !important;
        flex-direction: row-reverse !important;
      }

      .rtl [role="tab"] {
        flex: 1 !important;
      }
    }

    /* IMPORTANT: Empêcher l'inversion des icônes dans les onglets */
    .rtl .rtl-icon-no-flip {
      transform: scaleX(1) !important;
    }

    /* Garantir que l'icône reste à gauche du texte en RTL */
    .rtl [role="tab"] .flex {
      flex-direction: row-reverse !important;
    }

    /* Ajuster les marges pour l'espacement correct */
    .rtl [role="tab"] svg {
      margin-right: 0 !important;
      margin-left: 8px !important;
    }

    /* Styles spécifiques pour les onglets avec data-tab-trigger */
    .rtl [data-tab-trigger="true"] {
      display: flex !important;
      flex-direction: row !important;
    }

    /* Correction pour l'ordre des onglets en desktop */
    @media (min-width: 641px) {
      .rtl .sm\\:flex-row {
        flex-direction: row-reverse !important;
      }
    }

    /* Annuler toutes les transformations sur les icônes dans les onglets */
    .rtl .icon-wrapper svg,
    .rtl .icon-activity,
    .rtl .icon-plus,
    .rtl .icon-edit,
    .rtl [role="tab"] svg.lucide-activity,
    .rtl [role="tab"] svg.lucide-plus,
    .rtl [role="tab"] svg.lucide-edit,
    .rtl svg,
    .rtl [role="tab"] svg,
    .rtl .lucide,
    .rtl .rtl-icon-no-flip {
      transform: scaleX(1) !important;
    }

    /* Styles spécifiques pour le wrapper d'icône */
    .rtl .icon-wrapper {
      display: inline-flex !important;
      transform: none !important;
    }

    /* Styles globaux pour tous les SVG */
    .rtl svg {
      transform: scaleX(1) !important;
    }
  `;

  // --- Render ---
  const mainContent = (
    <>
      {/* Style injecté pour les onglets RTL */}
      <style dangerouslySetInnerHTML={{ __html: rtlTabsStyles }} />

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
          {/* --- MODIFIED TabsList --- */}
          <TabsList className="flex flex-col h-auto space-y-2 sm:space-y-0 sm:flex-row sm:h-10 w-full">
            <TabsTrigger
              value="templates"
              className="w-full justify-center sm:flex-1 flex items-center gap-2"
              data-tab-trigger="true"
            >
              <div className="flex items-center gap-2">
                {/* Utiliser une classe spécifique pour cette icône */}
                <span className="icon-wrapper" style={{ display: 'inline-flex' }}>
                  <img
                    src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWFjdGl2aXR5Ij48cGF0aCBkPSJNMjIgMTJoLTRsLTMgOUw5IDNsLTMgOUgyIi8+PC9zdmc+"
                    alt="Activity icon"
                    className="h-4 w-4"
                    style={{ filter: 'invert(0)' }}
                  />
                </span>
                <span>{t.templates}</span>
              </div>
            </TabsTrigger>
            <TabsTrigger
              value="create"
              className="w-full justify-center sm:flex-1 flex items-center gap-2"
              data-tab-trigger="true"
            >
              <div className="flex items-center gap-2">
                {/* Utiliser une classe spécifique pour cette icône */}
                <span className="icon-wrapper" style={{ display: 'inline-flex' }}>
                  <img
                    src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLXBsdXMiPjxwYXRoIGQ9Ik0xMiA1djE0TTUgMTJoMTQiLz48L3N2Zz4="
                    alt="Plus icon"
                    className="h-4 w-4"
                    style={{ filter: 'invert(0)' }}
                  />
                </span>
                <span>{t.createTemplate}</span>
              </div>
            </TabsTrigger>
            <TabsTrigger
              value="edit"
              disabled={!editingTemplate}
              className="w-full justify-center sm:flex-1 flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:text-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
              data-tab-trigger="true"
            >
              <div className="flex items-center gap-2">
                {/* Utiliser une classe spécifique pour cette icône */}
                <span className="icon-wrapper" style={{ display: 'inline-flex' }}>
                  <img
                    src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWVkaXQiPjxwYXRoIGQ9Ik0xMSA0SDRhMiAyIDAgMCAwLTIgMnYxNGEyIDIgMCAwIDAgMiAyaDE0YTIgMiAwIDAgMCAyLTJ2LTciLz48cGF0aCBkPSJNMTguNSAyLjVhMi4xMjEgMi4xMjEgMCAwIDEgMyAzTDEyIDE1bC00IDEgMS00IDkuNS05LjV6Ii8+PC9zdmc+"
                    alt="Edit icon"
                    className="h-4 w-4"
                    style={{ filter: 'invert(0)' }}
                  />
                </span>
                <span>{editingTemplate ? `${t.editTemplate} (${editingTemplate.name.substring(0, 10)}...)` : t.editTemplate}</span>
              </div>
            </TabsTrigger>
          </TabsList>
          {/* --- END MODIFIED TabsList --- */}

          {/* Templates Tab */}
          <TabsContent value="templates">
            {workoutActive && activeTemplate ? (
              <div className="space-y-6">
                {/* Workout Active View */}
                <div className="p-4 md:p-6 border rounded-lg bg-primary/5">
                  <h3 className="text-xl font-bold mb-2">{activeTemplate.name}</h3>
                  {activeTemplate.exercises[currentExerciseIndex] ? (
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-2">
                      <div className="flex-grow">
                        <p className="text-lg font-medium">{activeTemplate.exercises[currentExerciseIndex].name}</p>
                        <p className="text-sm text-muted-foreground">
                          {t.set} {currentSetIndex + 1}/{activeTemplate.exercises[currentExerciseIndex].sets}
                        </p>
                      </div>
                      <div className="text-left sm:text-right flex-shrink-0">
                        <p className="text-lg font-medium flex items-center gap-1">
                          <span className="text-xl font-bold">{activeTemplate.exercises[currentExerciseIndex].reps}</span> {t.reps}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {t.rest}: {formatTime(activeTemplate.exercises[currentExerciseIndex].rest)}
                        </p>
                      </div>
                    </div>
                  ) : (
                    <p className="text-red-500">{t.error || "Error: Exercise data missing."}</p>
                  )}
                  <div className="flex justify-center items-center gap-6 mt-8 min-h-[120px]">
                    {restTimerActive ? (
                      <div className="text-center">
                        <p className="text-2xl font-medium mb-4">{t.rest}</p>
                        <p className="text-5xl md:text-6xl font-mono font-bold tracking-wider">{formatTime(restTimer)}</p>
                      </div>
                    ) : (
                      <Button onClick={completeSet} size="lg" className="px-12 py-8 text-xl">
                        {activeTemplate.exercises[currentExerciseIndex] &&
                        currentSetIndex >= activeTemplate.exercises[currentExerciseIndex].sets - 1 &&
                        currentExerciseIndex >= activeTemplate.exercises.length - 1
                          ? t.finishWorkout || "Terminer l'entraînement"
                          : activeTemplate.exercises[currentExerciseIndex] ?
                            `${t.completeSet || "Terminer"} (${activeTemplate.exercises[currentExerciseIndex].reps} ${t.reps || "reps"})` :
                            t.completeSet || "Terminer"}
                      </Button>
                    )}
                  </div>
                  <Button onClick={stopWorkout} variant="outline" className="w-full mt-6">
                    {t.stopWorkout || "Arrêter l'entraînement"}
                  </Button>
                </div>
              </div>
            ) : (
              <>
                {/* Templates List View */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  {templates.length === 0 ? (
                    <div className="col-span-1 md:col-span-2 text-center py-8 text-muted-foreground border rounded-md">
                      {t.noTemplatesYet || "No templates yet."}{" "}
                      <Button variant="link" className="p-1 h-auto" onClick={() => setActiveTab("create")}>
                        {t.createOneNow || "Create one now?"}
                      </Button>
                    </div>
                  ) : (
                    templates.map((template) => (
                      <Card
                        key={template.id}
                        className={`cursor-pointer hover:border-primary transition-colors ${
                          activeTemplate?.id === template.id ? "border-primary ring-2 ring-primary/50" : ""
                        }`}
                        onClick={() => selectTemplate(template)}
                      >
                        <CardHeader className="pb-2">
                          <div className="flex justify-between items-start gap-2">
                            <div className="flex-grow min-w-0">
                              <CardTitle className="text-lg truncate" title={template.name}>
                                {template.name}
                              </CardTitle>
                              <CardDescription className="text-sm h-10 overflow-hidden text-ellipsis">
                                {template.description || t.noDescription || "No description"}
                              </CardDescription>
                            </div>
                            <div className="flex gap-1 flex-shrink-0">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  startEditingTemplate(template)
                                }}
                                title={t.editTemplate || "Edit Template"}
                              >
                                <Edit className="h-4 w-4" />
                                <span className="sr-only">{t.editTemplate || "Edit Template"}</span>
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  duplicateTemplate(template)
                                }}
                                title={t.duplicateTemplate || "Duplicate Template"}
                              >
                                <Copy className="h-4 w-4" />
                                <span className="sr-only">{t.duplicateTemplate || "Duplicate Template"}</span>
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-destructive hover:text-destructive hover:bg-destructive/10"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  const confirmMsg = formatTranslation(
                                    t.confirmDeleteTemplate || 'Are you sure you want to delete "{name}"?',
                                    { name: template.name },
                                  )
                                  if (window.confirm(confirmMsg)) {
                                    deleteTemplate(template.id)
                                  }
                                }}
                                title={t.deleteTemplate || "Delete Template"}
                              >
                                <Trash2 className="h-4 w-4" />
                                <span className="sr-only">{t.deleteTemplate || "Delete Template"}</span>
                              </Button>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="flex justify-between text-sm text-muted-foreground">
                            <span>
                              {template.exercises.length}{" "}
                              {template.exercises.length === 1 ? t.exercise || "exercise" : t.exercises || "exercises"}
                            </span>
                            <span className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />~{Math.ceil(calculateTotalTime(template.exercises) / 60)} min
                            </span>
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  )}
                </div>
                {/* Selected Template Details View */}
                {activeTemplate && !workoutActive && (
                  <Card className="mt-6 border-primary">
                    <CardHeader>
                      <CardTitle>{activeTemplate.name}</CardTitle>
                      <CardDescription>
                        {activeTemplate.description || t.noDescription || "No description"}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {activeTemplate.exercises.map((exercise, index) => (
                          <div key={exercise.id} className="p-3 border rounded-md bg-muted/30">
                            <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-2">
                              <div className="flex items-center gap-2 flex-grow">
                                <span className="font-medium text-muted-foreground w-6 text-right">{index + 1}.</span>
                                <h4 className="font-medium">{exercise.name}</h4>
                              </div>
                              <div className="flex items-center gap-2 sm:gap-4 text-sm flex-wrap justify-start sm:justify-end pl-8 sm:pl-0">
                                <Badge variant="secondary">
                                  {exercise.sets} {t.sets || "Sets"}
                                </Badge>
                                <Badge variant="secondary">
                                  {exercise.reps} {t.reps || "Reps"}
                                </Badge>
                                <Badge variant="outline">
                                  {formatTime(exercise.rest)} {t.rest || "Rest"}
                                </Badge>
                              </div>
                            </div>
                          </div>
                        ))}
                        <div className="flex justify-between items-center p-4 border-t mt-4">
                          <p className="font-medium">{t.estimatedTotalTime || "Estimated Total Time"}</p>
                          <p className="text-lg font-bold flex items-center gap-1">
                            <Clock className="h-4 w-4" />~{Math.ceil(calculateTotalTime(activeTemplate.exercises) / 60)}{" "}
                            min
                          </p>
                        </div>
                        <Button className="w-full" onClick={startWorkout} size="lg">
                          {t.startWorkout || "Start Workout"}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </>
            )}
          </TabsContent>

          {/* Create Tab */}
          <TabsContent value="create">
            <div className="space-y-6 p-1">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="template-name">
                    {t.templateName || "Template Name"} <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="template-name"
                    placeholder={t.templateNamePlaceholder || "e.g., Morning Routine"}
                    value={newTemplate.name}
                    onChange={(e) => setNewTemplate({ ...newTemplate, name: e.target.value })}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="template-description">{t.description || "Description"}</Label>
                  <Input
                    id="template-description"
                    placeholder={t.descriptionPlaceholder || "e.g., Quick workout to start the day"}
                    value={newTemplate.description}
                    onChange={(e) => setNewTemplate({ ...newTemplate, description: e.target.value })}
                  />
                </div>
              </div>
              <div className="border-t pt-6">
                <h3 className="font-medium mb-4">
                  {t.exercises || "Exercises"} <span className="text-red-500">*</span>
                </h3>
                <div className="space-y-3 mb-6 max-h-60 overflow-y-auto pr-2 border rounded-md p-2 bg-muted/20">
                  {newTemplate.exercises.length === 0 && (
                    <p className="text-sm text-muted-foreground p-4 text-center">
                      {t.noExercisesAddedYet || "No exercises added yet."}
                    </p>
                  )}
                  {newTemplate.exercises.map((exercise, index) => (
                    <div key={exercise.id} className="p-3 border rounded-md bg-background">
                      <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-2">
                        <div className="flex items-center gap-2 flex-grow">
                          <span className="font-medium text-muted-foreground w-6 text-right">{index + 1}.</span>
                          <h4 className="font-medium">{exercise.name}</h4>
                        </div>
                        <div className="flex items-center gap-2 text-sm flex-wrap justify-start sm:justify-end pl-8 sm:pl-0">
                          <Badge variant="secondary">
                            {exercise.sets} {t.sets || "Sets"}
                          </Badge>
                          <Badge variant="secondary">
                            {exercise.reps} {t.reps || "Reps"}
                          </Badge>
                          <Badge variant="outline">
                            {formatTime(exercise.rest)} {t.rest || "Rest"}
                          </Badge>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => removeExerciseFromNewTemplate(exercise.id)}
                            className="h-7 w-7 text-destructive hover:text-destructive hover:bg-destructive/10"
                            title={t.delete || "Delete"}
                          >
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">{t.delete || "Delete"}</span>
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="border rounded-md p-4">
                  <h4 className="text-sm font-medium mb-3">{t.addNewExercise || "Add New Exercise"}</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-3 mb-4">
                    <div className="sm:col-span-2 md:col-span-1">
                      <Label htmlFor="new-exercise-name">
                        {t.name || "Name"} <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="new-exercise-name"
                        placeholder={t.exerciseNamePlaceholder || "e.g., Jumping Jacks"}
                        value={newExercise.name}
                        onChange={(e) => setNewExercise({ ...newExercise, name: e.target.value })}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="new-exercise-sets">{t.sets || "Sets"}</Label>
                      <Input
                        id="new-exercise-sets"
                        type="number"
                        min="1"
                        max="20"
                        value={newExercise.sets}
                        onChange={(e) =>
                          setNewExercise({ ...newExercise, sets: Math.max(1, Number.parseInt(e.target.value) || 1) })
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="new-exercise-reps">{t.reps || "Reps"}</Label>
                      <Input
                        id="new-exercise-reps"
                        type="number"
                        min="1"
                        max="100"
                        value={newExercise.reps}
                        onChange={(e) =>
                          setNewExercise({ ...newExercise, reps: Math.max(1, Number.parseInt(e.target.value) || 1) })
                        }
                      />
                    </div>
                    <div>
                      <Label htmlFor="new-exercise-rest">
                        {t.restTime || "Rest Time"} ({t.seconds || "Seconds"})
                      </Label>
                      <Input
                        id="new-exercise-rest"
                        type="number"
                        min="0"
                        max="600"
                        step="5"
                        value={newExercise.rest}
                        onChange={(e) =>
                          setNewExercise({ ...newExercise, rest: Math.max(0, Number.parseInt(e.target.value) || 0) })
                        }
                      />
                    </div>
                  </div>
                  <Button
                    onClick={addExerciseToNewTemplate}
                    variant="outline"
                    className="w-full"
                    disabled={newExercise.name.trim() === ""}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    {t.addExerciseToList || "Add Exercise to List"}
                  </Button>
                </div>
                <Button
                  onClick={saveNewTemplate}
                  className="w-full mt-6"
                  size="lg"
                  disabled={newTemplate.name.trim() === "" || newTemplate.exercises.length === 0}
                >
                  {t.saveNewTemplate || "Save New Template"}
                </Button>
              </div>
            </div>
          </TabsContent>

          {/* Edit Tab */}
          <TabsContent value="edit" forceMount className={!editingTemplate ? "hidden" : ""}>
            {editingTemplate ? (
              <div className="space-y-6 p-1">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="edit-template-name">
                      {t.templateName || "Template Name"} <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="edit-template-name"
                      placeholder={t.templateNamePlaceholder || "e.g., Morning Routine"}
                      value={editingTemplate.name}
                      onChange={(e) => setEditingTemplate((prev) => (prev ? { ...prev, name: e.target.value } : null))}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit-template-description">{t.description || "Description"}</Label>
                    <Input
                      id="edit-template-description"
                      placeholder={t.descriptionPlaceholder || "e.g., Quick workout to start the day"}
                      value={editingTemplate.description}
                      onChange={(e) =>
                        setEditingTemplate((prev) => (prev ? { ...prev, description: e.target.value } : null))
                      }
                    />
                  </div>
                </div>
                <div className="border-t pt-6">
                  <h3 className="font-medium mb-4">
                    {t.exercises || "Exercises"} <span className="text-red-500">*</span>
                  </h3>
                  <div className="space-y-3 mb-6 max-h-60 overflow-y-auto pr-2 border rounded-md p-2 bg-muted/20">
                    {editingTemplate.exercises.length === 0 && (
                      <p className="text-sm text-muted-foreground p-4 text-center">
                        {t.noExercisesAddedYet || "No exercises added yet."}
                      </p>
                    )}
                    {editingTemplate.exercises.map((exercise, index) => (
                      <div key={exercise.id} className="p-3 border rounded-md bg-background">
                        <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-2">
                          <div className="flex items-center gap-2 flex-grow">
                            <span className="font-medium text-muted-foreground w-6 text-right">{index + 1}.</span>
                            <h4 className="font-medium">{exercise.name}</h4>
                          </div>
                          <div className="flex items-center gap-2 text-sm flex-wrap justify-start sm:justify-end pl-8 sm:pl-0">
                            <Badge variant="secondary">
                              {exercise.sets} {t.sets || "Sets"}
                            </Badge>
                            <Badge variant="secondary">
                              {exercise.reps} {t.reps || "Reps"}
                            </Badge>
                            <Badge variant="outline">
                              {formatTime(exercise.rest)} {t.rest || "Rest"}
                            </Badge>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => removeExerciseFromEditingTemplate(exercise.id)}
                              className="h-7 w-7 text-destructive hover:text-destructive hover:bg-destructive/10"
                              title={t.delete || "Delete"}
                            >
                              <Trash2 className="h-4 w-4" />
                              <span className="sr-only">{t.delete || "Delete"}</span>
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="border rounded-md p-4">
                    <h4 className="text-sm font-medium mb-3">{t.addNewExercise || "Add New Exercise"}</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-3 mb-4">
                      <div className="sm:col-span-2 md:col-span-1">
                        <Label htmlFor="edit-add-exercise-name">
                          {t.name || "Name"} <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="edit-add-exercise-name"
                          placeholder={t.exerciseNamePlaceholder || "e.g., Crunches"}
                          value={newExercise.name}
                          onChange={(e) => setNewExercise({ ...newExercise, name: e.target.value })}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="edit-add-exercise-sets">{t.sets || "Sets"}</Label>
                        <Input
                          id="edit-add-exercise-sets"
                          type="number"
                          min="1"
                          max="20"
                          value={newExercise.sets}
                          onChange={(e) =>
                            setNewExercise({ ...newExercise, sets: Math.max(1, Number.parseInt(e.target.value) || 1) })
                          }
                        />
                      </div>
                      <div>
                        <Label htmlFor="edit-add-exercise-reps">{t.reps || "Reps"}</Label>
                        <Input
                          id="edit-add-exercise-reps"
                          type="number"
                          min="1"
                          max="100"
                          value={newExercise.reps}
                          onChange={(e) =>
                            setNewExercise({ ...newExercise, reps: Math.max(1, Number.parseInt(e.target.value) || 1) })
                          }
                        />
                      </div>
                      <div>
                        <Label htmlFor="edit-add-exercise-rest">
                          {t.restTime || "Rest Time"} ({t.seconds || "Seconds"})
                        </Label>
                        <Input
                          id="edit-add-exercise-rest"
                          type="number"
                          min="0"
                          max="600"
                          step="5"
                          value={newExercise.rest}
                          onChange={(e) =>
                            setNewExercise({ ...newExercise, rest: Math.max(0, Number.parseInt(e.target.value) || 0) })
                          }
                        />
                      </div>
                    </div>
                    <Button
                      onClick={addExerciseToEditingTemplate}
                      variant="outline"
                      className="w-full"
                      disabled={newExercise.name.trim() === ""}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      {t.addExerciseToList || "Add Exercise to List"}
                    </Button>
                  </div>
                  <Button
                    onClick={saveEditedTemplate}
                    className="w-full mt-6"
                    size="lg"
                    disabled={
                      !editingTemplate || editingTemplate.name.trim() === "" || editingTemplate.exercises.length === 0
                    }
                  >
                    {t.saveChanges || "Save Changes"}
                  </Button>
                </div>
              </div>
            ) : null}
          </TabsContent>
        </Tabs>
      </CardContent>
    </>
  )

  return (
    <>
      <Card className="w-full max-w-6xl mx-auto">
        <CardHeader>
          <div className="flex justify-between items-start">
            <CardTitle className="flex items-center gap-2">
              <Dumbbell className="h-5 w-5" />
              {t.exerciseTemplates}
            </CardTitle>
            {/* Contrôles d'outils en haut à droite */}
            <ToolControls
              onFullscreenToggle={setIsToolFullscreen}
              variant="compact"
            />
          </div>
        </CardHeader>
        {mainContent}
      </Card>

      {/* Wrapper plein écran */}
      <ToolFullscreenWrapper
        isFullscreen={isToolFullscreen}
        onClose={() => setIsToolFullscreen(false)}
        toolName={t.exerciseTemplates}
      >
        <Card className="w-full border-0 shadow-none bg-transparent">
          {mainContent}
        </Card>
      </ToolFullscreenWrapper>
    </>
  )
}
