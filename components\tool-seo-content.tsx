"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { getTranslations } from "@/lib/i18n/translations"
import { RelatedTools } from "@/components/related-tools"
import { ContextualNavigation } from "@/components/contextual-navigation"
import Script from "next/script"

interface ToolSeoContentProps {
  lang?: string
  toolType: 'timer' | 'countdown' | 'worldClock' | 'intervals' | 'pomodoro' | 'todo' | 'timeTracking' | 'meetingTimer' | 'timeBilling' | 'workoutIntervals' | 'exerciseTemplates'
}

export function ToolSeoContent({ lang = "en", toolType }: ToolSeoContentProps) {
  const t = getTranslations(lang)

  // Determine the context for contextual navigation
  const getToolContext = () => {
    switch (toolType) {
      case 'pomodoro':
      case 'todo':
      case 'timeTracking':
        return 'productivity'
      case 'timer':
      case 'countdown':
      case 'worldClock':
      case 'intervals':
        return 'time-management'
      case 'workoutIntervals':
      case 'exerciseTemplates':
        return 'fitness'
      default:
        return 'general'
    }
  }

  // Get the appropriate description based on the tool type
  const getToolDescription = () => {
    switch (toolType) {
      case 'timer':
        return t.timerDescription
      case 'countdown':
        return t.countdownDescription
      case 'worldClock':
        return t.worldClockDescription
      case 'intervals':
        return t.intervalsDescription
      case 'pomodoro':
        return t.pomodoroDescription
      case 'todo':
        return t.todoDescription
      case 'timeTracking':
        return t.timeTrackingDescription
      case 'meetingTimer':
        return t.meetingTimerDescription
      case 'timeBilling':
        return t.timeBillingDescription
      case 'workoutIntervals':
        return t.workoutIntervalsDescription
      case 'exerciseTemplates':
        return t.exerciseTemplatesDescription
      default:
        return ''
    }
  }

  // Get the appropriate title based on the tool type
  const getToolTitle = () => {
    switch (toolType) {
      case 'timer':
        return t.timerToolTitle
      case 'countdown':
        return t.countdownToolTitle
      case 'worldClock':
        return t.worldClockToolTitle
      case 'intervals':
        return t.intervalsToolTitle
      case 'pomodoro':
        return t.pomodoroToolTitle
      case 'todo':
        return t.todoToolTitle
      case 'timeTracking':
        return t.timeTrackingToolTitle
      case 'meetingTimer':
        return t.meetingTimerToolTitle
      case 'timeBilling':
        return t.timeBillingToolTitle
      case 'workoutIntervals':
        return t.workoutIntervalsToolTitle
      case 'exerciseTemplates':
        return t.exerciseTemplatesToolTitle
      default:
        return t.defaultToolTitle
    }
  }

  // Get tool-specific features
  const getToolFeatures = () => {
    switch (toolType) {
      case 'timer':
        return [
          t.timerFeature1,
          t.timerFeature2,
          t.timerFeature3,
          t.timerFeature4,
          t.timerFeature5
        ]
      case 'countdown':
        return [
          t.countdownFeature1,
          t.countdownFeature2,
          t.countdownFeature3,
          t.countdownFeature4,
          t.countdownFeature5
        ]
      case 'worldClock':
        return [
          t.worldClockFeature1,
          t.worldClockFeature2,
          t.worldClockFeature3,
          t.worldClockFeature4,
          t.worldClockFeature5
        ]
      case 'intervals':
        return [
          t.intervalsFeature1,
          t.intervalsFeature2,
          t.intervalsFeature3,
          t.intervalsFeature4,
          t.intervalsFeature5
        ]
      case 'pomodoro':
        return [
          t.pomodoroFeature1,
          t.pomodoroFeature2,
          t.pomodoroFeature3,
          t.pomodoroFeature4,
          t.pomodoroFeature5
        ]
      case 'todo':
        return [
          t.todoFeature1,
          t.todoFeature2,
          t.todoFeature3,
          t.todoFeature4,
          t.todoFeature5
        ]
      case 'timeTracking':
        return [
          t.timeTrackingFeature1,
          t.timeTrackingFeature2,
          t.timeTrackingFeature3,
          t.timeTrackingFeature4,
          t.timeTrackingFeature5
        ]
      case 'meetingTimer':
        return [
          t.meetingTimerFeature1,
          t.meetingTimerFeature2,
          t.meetingTimerFeature3,
          t.meetingTimerFeature4,
          t.meetingTimerFeature5
        ]
      case 'timeBilling':
        return [
          t.timeBillingFeature1,
          t.timeBillingFeature2,
          t.timeBillingFeature3,
          t.timeBillingFeature4,
          t.timeBillingFeature5
        ]
      case 'workoutIntervals':
        return [
          t.workoutIntervalsFeature1,
          t.workoutIntervalsFeature2,
          t.workoutIntervalsFeature3,
          t.workoutIntervalsFeature4,
          t.workoutIntervalsFeature5
        ]
      case 'exerciseTemplates':
        return [
          t.exerciseTemplatesFeature1,
          t.exerciseTemplatesFeature2,
          t.exerciseTemplatesFeature3,
          t.exerciseTemplatesFeature4,
          t.exerciseTemplatesFeature5
        ]
      default:
        return []
    }
  }

  // Get tool-specific content
  const getToolSpecificContent = () => {
    switch (toolType) {
      case 'timer':
        return t.timerWhyUse
      case 'countdown':
        return t.countdownWhyUse
      case 'worldClock':
        return t.worldClockWhyUse
      case 'intervals':
        return t.intervalsWhyUse
      case 'pomodoro':
        return t.pomodoroWhyUse
      case 'todo':
        return t.todoWhyUse
      case 'timeTracking':
        return t.timeTrackingWhyUse
      case 'meetingTimer':
        return t.meetingTimerWhyUse
      case 'timeBilling':
        return t.timeBillingWhyUse
      case 'workoutIntervals':
        return t.workoutIntervalsWhyUse
      case 'exerciseTemplates':
        return t.exerciseTemplatesWhyUse
      default:
        return ''
    }
  }

  // Create structured data for the tool
  const toolStructuredData = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": `${getToolTitle()} | ${t.appName}`,
    "applicationCategory": "UtilityApplication",
    "operatingSystem": "Web",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "description": getToolDescription()
  }

  return (
    <div className="py-12">
      {/* Add structured data */}
      <Script
        id="tool-structured-data"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(toolStructuredData) }}
      />

      <div className="prose dark:prose-invert max-w-none">
        <h2 className="text-2xl font-bold mb-6">{t.keyFeatures}</h2>
        <Card className="mb-8">
          <CardContent className="pt-6">
            <ul className="list-disc pl-6 space-y-2">
              {getToolFeatures().map((feature, index) => (
                <li key={index}>{feature}</li>
              ))}
            </ul>
          </CardContent>
        </Card>

        {/* Add contextual navigation */}
        <ContextualNavigation
          lang={lang}
          currentTool={toolType}
          context={getToolContext() as "productivity" | "time-management" | "fitness" | "general"}
        />

        <h2 className="text-2xl font-bold mb-6">{t.whyUseOurTool.replace('{toolName}', getToolTitle())}</h2>
        <Card>
          <CardContent className="pt-6">
            <p>
              {getToolSpecificContent()}
            </p>
            <p className="mt-4">
              {t.toolFreeInfo.replace('{toolName}', getToolTitle().toLowerCase())}
            </p>
            <p className="mt-4">
              {t.toolProductivityInfo.replace('{toolName}', getToolTitle().toLowerCase())}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Add related tools section */}
      <RelatedTools lang={lang} currentTool={toolType} maxItems={6} />
    </div>
  )
}
