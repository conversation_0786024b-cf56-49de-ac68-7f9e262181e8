"use client"

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { getTranslations } from "@/lib/i18n/translations"
import { getTranslatedRoute } from "@/lib/i18n/route-translations"
import Link from "next/link"
import { Home } from "lucide-react"

interface PageBreadcrumbsProps {
  lang?: string
  currentPage?: string
  customItems?: Array<{
    label: string
    href?: string
  }>
}

export function PageBreadcrumbs({ lang = "en", currentPage, customItems }: PageBreadcrumbsProps) {
  const t = getTranslations(lang)

  // Helper function to get localized links
  const getLocalizedLink = (route: string) => {
    const translatedRoute = getTranslatedRoute(lang, route)
    return `/${lang}/${translatedRoute}`
  }

  // Get the display name for a page
  const getPageDisplayName = (page: string) => {
    switch (page) {
      case "timer":
        return t.timerStopwatch
      case "countdown":
        return t.countdown
      case "world-clock":
        return t.worldClock
      case "intervals":
        return t.intervals
      case "pomodoro":
        return t.pomodoro
      case "todo":
        return t.todoList
      case "time-tracking":
        return t.timeTracking
      case "workout-intervals":
        return t.workoutIntervals
      case "exercise-templates":
        return t.exerciseTemplates
      case "meeting-timer":
        return t.meetingTimer
      case "time-billing":
        return t.timeBilling
      case "sitemap":
        return t.sitemap || "Plan du site"
      default:
        return page
    }
  }

  // Build breadcrumb items
  const buildBreadcrumbItems = () => {
    const items = []

    // Always start with home
    items.push({
      label: t.home || "Accueil",
      href: `/${lang}`,
      isHome: true
    })

    // Add custom items if provided
    if (customItems) {
      items.push(...customItems.map(item => ({
        label: item.label,
        href: item.href,
        isHome: false
      })))
    }

    // Add current page if specified
    if (currentPage) {
      const pageDisplayName = getPageDisplayName(currentPage)
      items.push({
        label: pageDisplayName,
        href: undefined, // Current page, no link
        isHome: false
      })
    }

    return items
  }

  const breadcrumbItems = buildBreadcrumbItems()

  // Don't render if only home page
  if (breadcrumbItems.length <= 1) {
    return null
  }

  return (
    <div className="mb-6">
      <Breadcrumb>
        <BreadcrumbList>
          {breadcrumbItems.map((item, index) => (
            <div key={index} className="flex items-center">
              {index > 0 && <BreadcrumbSeparator />}
              <BreadcrumbItem>
                {item.href ? (
                  <BreadcrumbLink asChild>
                    <Link href={item.href} className="flex items-center gap-1">
                      {item.isHome && <Home className="h-4 w-4" />}
                      {item.label}
                    </Link>
                  </BreadcrumbLink>
                ) : (
                  <BreadcrumbPage>{item.label}</BreadcrumbPage>
                )}
              </BreadcrumbItem>
            </div>
          ))}
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  )
}
