let intervalId = null;
let endTime = 0;
let lastSentSecond = -1; // Keep track of the last second value sent

self.onmessage = function(event) {
  const { command, value } = event.data;

  if (command === 'start') {
    endTime = value; // value should be the target end timestamp
    // Clear any existing interval before starting a new one
    if (intervalId) {
      clearInterval(intervalId);
    }

    intervalId = setInterval(() => {
      const now = Date.now();
      const timeLeftMs = Math.max(0, endTime - now);
      const currentSecond = Math.round(timeLeftMs / 1000); // Calculate current second

      // Only post message if the second has changed or it's the final tick (<= 0)
      if (currentSecond !== lastSentSecond || timeLeftMs <= 0) {
        self.postMessage({ type: 'tick', timeLeft: timeLeftMs }); // Send raw ms back
        lastSentSecond = currentSecond; // Update the last sent second
      }

      if (timeLeftMs <= 0) {
        clearInterval(intervalId);
        intervalId = null;
        lastSentSecond = -1; // Reset for next run
        // Optionally send a 'finished' message, although the main thread can detect this from timeLeft <= 0
        // self.postMessage({ type: 'finished' });
      }
    }, 250); // Check frequently for accuracy
  } else if (command === 'stop') {
    if (intervalId) {
      clearInterval(intervalId);
      intervalId = null;
    }
    endTime = 0; // Reset end time
    lastSentSecond = -1; // Reset last sent second on stop
  }
};
