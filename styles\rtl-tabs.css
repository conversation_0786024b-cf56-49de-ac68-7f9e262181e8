/* Styles spécifiques pour les onglets en mode RTL */

/* Style de base pour tous les onglets */
.rtl [role="tablist"],
.rtl .tabs-list,
.rtl .tabs-nav,
.rtl .nav-tabs {
  flex-direction: row-reverse !important;
  justify-content: flex-start;
}

/* Nouvel ajout - Styles spécifiques pour les triggers d'onglets en mode RTL */
.rtl-tab-trigger {
  flex-direction: row !important; /* Ne pas inverser l'ordre des éléments internes */
}

.rtl .flex.items-center.gap-2 svg,
.rtl .flex.items-center.justify-center.gap-2 svg {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* Règles spécifiques pour les onglets avec data-tab-trigger */
.rtl [data-tab-trigger="true"] {
  display: flex !important;
  flex-direction: row !important;
}

.rtl [data-tab-trigger="true"] svg {
  margin-right: 0 !important;
  margin-left: 0.5rem !important;
}

/* Inverser l'ordre des onglets dans une grille */
.rtl .grid[role="tablist"],
.rtl .grid.tabs-list {
  direction: rtl;
}

.rtl .grid[role="tablist"] > *,
.rtl .grid.tabs-list > * {
  direction: rtl;
  text-align: right;
}

/* Ajuster les marges et paddings dans les onglets */
.rtl [role="tab"] {
  margin-right: 0;
  margin-left: 0.5rem;
  text-align: right;
}

/* Ajuster les bordures des onglets */
.rtl [role="tab"]:first-child {
  border-top-right-radius: var(--radius);
  border-bottom-right-radius: var(--radius);
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.rtl [role="tab"]:last-child {
  border-top-left-radius: var(--radius);
  border-bottom-left-radius: var(--radius);
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

/* Ajuster les indicateurs d'onglets actifs */
.rtl [role="tab"][aria-selected="true"]::after {
  left: auto;
  right: 0;
}

/* Ajuster les contenus des onglets */
.rtl [role="tabpanel"] {
  direction: rtl;
  text-align: right;
}

/* Ajuster les icônes dans les onglets */
.rtl [role="tab"] svg,
.rtl [role="tab"] .icon {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* Ajuster les badges dans les onglets */
.rtl [role="tab"] .badge {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* Ajuster les onglets verticaux */
.rtl .vertical-tabs [role="tablist"] {
  flex-direction: column !important;
}

.rtl .vertical-tabs [role="tab"] {
  text-align: right;
}

/* Ajuster les onglets avec des boutons de défilement */
.rtl .tabs-with-scroll-buttons .scroll-button-left {
  left: auto;
  right: 0;
}

.rtl .tabs-with-scroll-buttons .scroll-button-right {
  right: auto;
  left: 0;
}

/* Ajuster les onglets avec des menus déroulants */
.rtl .tabs-with-dropdown .dropdown-menu {
  text-align: right;
}

/* Ajuster les onglets avec des badges de notification */
.rtl .tab-with-notification .notification-badge {
  right: auto;
  left: 0.5rem;
}

/* Ajuster les onglets avec des icônes de fermeture */
.rtl .tab-with-close-button .close-button {
  right: auto;
  left: 0.5rem;
}

/* Ajuster les onglets avec des indicateurs de progression */
.rtl .tab-with-progress .progress-indicator {
  transform: scaleX(-1);
}

/* Ajuster les onglets avec des tooltips */
.rtl .tab-with-tooltip .tooltip {
  text-align: right;
}

/* Ajuster les onglets avec des sous-menus */
.rtl .tab-with-submenu .submenu {
  right: auto;
  left: 0;
  text-align: right;
}

/* Ajuster les onglets avec des états */
.rtl .tab-with-state .state-indicator {
  right: auto;
  left: 0.5rem;
}

/* Ajuster les onglets avec des compteurs */
.rtl .tab-with-counter .counter {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* Ajuster les onglets avec des avatars */
.rtl .tab-with-avatar .avatar {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* Ajuster les onglets avec des images */
.rtl .tab-with-image .image {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* Ajuster les onglets avec des boutons */
.rtl .tab-with-button .button {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* Ajuster les onglets avec des inputs */
.rtl .tab-with-input .input {
  text-align: right;
  direction: rtl;
}

/* Ajuster les onglets avec des sélecteurs */
.rtl .tab-with-select .select {
  text-align: right;
  direction: rtl;
}

/* Ajuster les onglets avec des checkboxes */
.rtl .tab-with-checkbox .checkbox {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* Ajuster les onglets avec des radios */
.rtl .tab-with-radio .radio {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* Ajuster les onglets avec des switches */
.rtl .tab-with-switch .switch {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* Ajuster les onglets avec des sliders */
.rtl .tab-with-slider .slider {
  direction: rtl;
}

/* Ajuster les onglets avec des ratings */
.rtl .tab-with-rating .rating {
  flex-direction: row-reverse;
}

/* Ajuster les onglets avec des pagination */
.rtl .tab-with-pagination .pagination {
  flex-direction: row-reverse;
}

/* Ajuster les onglets avec des breadcrumbs */
.rtl .tab-with-breadcrumb .breadcrumb {
  flex-direction: row-reverse;
}

/* Ajuster les onglets avec des stepper */
.rtl .tab-with-stepper .stepper {
  flex-direction: row-reverse;
}

/* Ajuster les onglets avec des timeline */
.rtl .tab-with-timeline .timeline {
  text-align: right;
}

/* Ajuster les onglets avec des tree view */
.rtl .tab-with-tree .tree {
  text-align: right;
}

/* Ajuster les onglets avec des data tables */
.rtl .tab-with-data-table .data-table {
  text-align: right;
}

/* Ajuster les onglets avec des calendars */
.rtl .tab-with-calendar .calendar {
  direction: rtl;
}

/* Ajuster les onglets avec des date pickers */
.rtl .tab-with-date-picker .date-picker {
  direction: rtl;
}

/* Ajuster les onglets avec des time pickers */
.rtl .tab-with-time-picker .time-picker {
  direction: rtl;
}

/* Ajuster les onglets avec des color pickers */
.rtl .tab-with-color-picker .color-picker {
  direction: rtl;
}

/* Ajuster les onglets avec des file uploads */
.rtl .tab-with-file-upload .file-upload {
  text-align: right;
}

/* Ajuster les onglets avec des rich text editors */
.rtl .tab-with-rich-text-editor .rich-text-editor {
  text-align: right;
  direction: rtl;
}

/* Ajuster les onglets avec des code editors */
.rtl .tab-with-code-editor .code-editor {
  text-align: right;
  direction: rtl;
}

/* Ajuster les onglets avec des markdown */
.rtl .tab-with-markdown .markdown {
  text-align: right;
  direction: rtl;
}

/* Ajuster les onglets avec des charts */
.rtl .tab-with-chart .chart {
  direction: rtl;
}

/* Ajuster les onglets avec des maps */
.rtl .tab-with-map .map {
  direction: rtl;
}

/* Ajuster les onglets avec des videos */
.rtl .tab-with-video .video {
  direction: rtl;
}

/* Ajuster les onglets avec des audios */
.rtl .tab-with-audio .audio {
  direction: rtl;
}

/* Ajuster les onglets avec des images */
.rtl .tab-with-image .image {
  direction: rtl;
}

/* Ajuster les onglets avec des avatars */
.rtl .tab-with-avatar .avatar {
  direction: rtl;
}

/* Ajuster les onglets avec des badges */
.rtl .tab-with-badge .badge {
  direction: rtl;
}

/* Ajuster les onglets avec des tooltips */
.rtl .tab-with-tooltip .tooltip {
  direction: rtl;
}

/* Ajuster les onglets avec des popups */
.rtl .tab-with-popup .popup {
  direction: rtl;
}

/* Ajuster les onglets avec des menus */
.rtl .tab-with-menu .menu {
  direction: rtl;
}

/* Ajuster les onglets avec des dropdowns */
.rtl .tab-with-dropdown .dropdown {
  direction: rtl;
}

/* Ajuster les onglets avec des modals */
.rtl .tab-with-modal .modal {
  direction: rtl;
}

/* Ajuster les onglets avec des alerts */
.rtl .tab-with-alert .alert {
  direction: rtl;
}

/* Ajuster les onglets avec des notifications */
.rtl .tab-with-notification .notification {
  direction: rtl;
}

/* Ajuster les onglets avec des toasts */
.rtl .tab-with-toast .toast {
  direction: rtl;
}

/* Ajuster les onglets avec des carousels */
.rtl .tab-with-carousel .carousel {
  direction: rtl;
}

/* Ajuster les onglets avec des sliders */
.rtl .tab-with-slider .slider {
  direction: rtl;
}

/* Ajuster les onglets avec des ratings */
.rtl .tab-with-rating .rating {
  direction: rtl;
}

/* Ajuster les onglets avec des pagination */
.rtl .tab-with-pagination .pagination {
  direction: rtl;
}

/* Ajuster les onglets avec des breadcrumbs */
.rtl .tab-with-breadcrumb .breadcrumb {
  direction: rtl;
}

/* Ajuster les onglets avec des stepper */
.rtl .tab-with-stepper .stepper {
  direction: rtl;
}

/* Ajuster les onglets avec des timeline */
.rtl .tab-with-timeline .timeline {
  direction: rtl;
}

/* Ajuster les onglets avec des tree view */
.rtl .tab-with-tree .tree {
  direction: rtl;
}

/* Ajuster les onglets avec des data tables */
.rtl .tab-with-data-table .data-table {
  direction: rtl;
}

/* Ajuster les onglets avec des calendars */
.rtl .tab-with-calendar .calendar {
  direction: rtl;
}

/* Ajuster les onglets avec des date pickers */
.rtl .tab-with-date-picker .date-picker {
  direction: rtl;
}

/* Ajuster les onglets avec des time pickers */
.rtl .tab-with-time-picker .time-picker {
  direction: rtl;
}

/* Ajuster les onglets avec des color pickers */
.rtl .tab-with-color-picker .color-picker {
  direction: rtl;
}

/* Ajuster les onglets avec des file uploads */
.rtl .tab-with-file-upload .file-upload {
  direction: rtl;
}

/* Ajuster les onglets avec des rich text editors */
.rtl .tab-with-rich-text-editor .rich-text-editor {
  direction: rtl;
}

/* Ajuster les onglets avec des code editors */
.rtl .tab-with-code-editor .code-editor {
  direction: rtl;
}

/* Ajuster les onglets avec des markdown */
.rtl .tab-with-markdown .markdown {
  direction: rtl;
}

/* Ajuster les onglets avec des charts */
.rtl .tab-with-chart .chart {
  direction: rtl;
}

/* Ajuster les onglets avec des maps */
.rtl .tab-with-map .map {
  direction: rtl;
}

/* Ajuster les onglets avec des videos */
.rtl .tab-with-video .video {
  direction: rtl;
}

/* Ajuster les onglets avec des audios */
.rtl .tab-with-audio .audio {
  direction: rtl;
}

/* Ajuster les onglets avec des images */
.rtl .tab-with-image .image {
  direction: rtl;
}
