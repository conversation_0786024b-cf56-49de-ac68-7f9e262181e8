'use client';

import { useEffect, useState } from 'react';
import { useLanguage } from '@/components/language-provider';
import { useRTL } from '@/hooks/useRTL';

export function RTLProvider({ children }: { children: React.ReactNode }) {
  const { language } = useLanguage();
  const isRTL = useRTL(language);
  const [mounted, setMounted] = useState(false);

  // Apply RTL classes immediately on first render to prevent flicker
  useEffect(() => {
    // Apply RTL classes to body instead of html element
    if (isRTL) {
      document.body.classList.add('rtl');
      document.body.classList.remove('ltr');
    } else {
      document.body.classList.add('ltr');
      document.body.classList.remove('rtl');
    }

    setMounted(true);
  }, [isRTL]);

  // Apply full RTL modifications after mounting
  useEffect(() => {
    if (!mounted) {
      return;
    }

    // Apply RTL classes to body only (html is handled by HtmlAttributes)
    if (isRTL) {
      document.body.classList.add('rtl');
      document.body.classList.remove('ltr');

      // Ajouter une classe de transition pour les animations fluides
      document.documentElement.classList.add('rtl-transition');

      // Appliquer toutes les modifications RTL
      applyRTLModifications();

      // Forcer un refresh du document pour appliquer les styles RTL
      setTimeout(() => {
        window.dispatchEvent(new Event('resize'));
        document.documentElement.classList.remove('rtl-transition');
      }, 100);
    } else {
      document.body.classList.add('ltr');
      document.body.classList.remove('rtl');

      // Ajouter une classe de transition pour les animations fluides
      document.documentElement.classList.add('rtl-transition');

      // Réinitialiser toutes les modifications RTL
      resetRTLModifications();

      setTimeout(() => {
        window.dispatchEvent(new Event('resize'));
        document.documentElement.classList.remove('rtl-transition');
      }, 100);
    }

    console.log(`RTLProvider: setting direction to ${isRTL ? 'rtl' : 'ltr'} for language ${language}`);

    // Nettoyage lors du démontage du composant
    return () => {
      document.documentElement.classList.remove('rtl-transition');
    };
  }, [isRTL, language, mounted]);

  // Fonction pour appliquer toutes les modifications RTL
  const applyRTLModifications = () => {
    // Inverser les éléments directionnels
    invertDirectionalElements(true);

    // Ajuster les grilles et les flexbox
    adjustLayoutElements(true);

    // Ajuster les éléments de formulaire pour RTL
    adjustFormElements(true);

    // Inverser les éléments de scroll et d'overflow
    adjustOverflowElements(true);

    // Ajuster les positions fixes et absolues
    adjustPositionedElements(true);

    // Inverser les animations et transitions
    adjustAnimations(true);

    // Ajuster les composants d'interface utilisateur spécifiques
    adjustUIComponents(true);
  };

  // Fonction pour réinitialiser toutes les modifications RTL
  const resetRTLModifications = () => {
    // Réinitialiser les éléments inversés
    invertDirectionalElements(false);

    // Réinitialiser les grilles et les flexbox
    adjustLayoutElements(false);

    // Réinitialiser les éléments de formulaire
    adjustFormElements(false);

    // Réinitialiser les éléments de scroll
    adjustOverflowElements(false);

    // Réinitialiser les positions
    adjustPositionedElements(false);

    // Réinitialiser les animations
    adjustAnimations(false);

    // Réinitialiser les composants d'interface utilisateur spécifiques
    adjustUIComponents(false);
  };

  // Fonction pour inverser les éléments directionnels (icônes, boutons, etc.)
  const invertDirectionalElements = (toRTL: boolean) => {
    // Sélectionner tous les éléments avec la classe rtl-mirror
    const elementsToInvert = document.querySelectorAll('.rtl-mirror, [data-rtl-mirror="true"]');
    elementsToInvert.forEach(el => {
      if (toRTL) {
        el.classList.add('rtl-inverted');
      } else {
        el.classList.remove('rtl-inverted');
      }
    });

    // Inverser les icônes de direction
    const directionIcons = document.querySelectorAll('.icon-arrow-left, .icon-arrow-right, .icon-chevron-left, .icon-chevron-right, [data-rtl-flip="true"]');
    directionIcons.forEach(icon => {
      if (toRTL) {
        icon.classList.add('rtl-inverted');
      } else {
        icon.classList.remove('rtl-inverted');
      }
    });
  };

  // Ajuster les éléments de mise en page (grilles, flexbox)
  const adjustLayoutElements = (toRTL: boolean) => {
    // Ajuster les grilles
    const gridElements = document.querySelectorAll('.grid, [data-rtl-grid="true"]');
    gridElements.forEach(el => {
      if (toRTL) {
        el.classList.add('rtl-grid');
      } else {
        el.classList.remove('rtl-grid');
      }
    });

    // Ajuster les éléments flex qui ne sont pas déjà traités par CSS
    const flexElements = document.querySelectorAll('[data-rtl-flex="true"]');
    flexElements.forEach(el => {
      if (el instanceof HTMLElement) {
        if (toRTL) {
          el.style.flexDirection = el.style.flexDirection === 'row' ? 'row-reverse' : el.style.flexDirection;
        } else {
          el.style.flexDirection = el.style.flexDirection === 'row-reverse' ? 'row' : el.style.flexDirection;
        }
      }
    });
  };

  // Ajuster les éléments de formulaire pour RTL
  const adjustFormElements = (toRTL: boolean) => {
    // Ajuster l'alignement du texte des inputs
    const textInputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="password"], input[type="search"], textarea, [data-rtl-input="true"]');
    textInputs.forEach(input => {
      if (input instanceof HTMLElement) {
        if (toRTL) {
          input.classList.add('rtl-text-input');
          input.style.textAlign = 'right';
          input.style.direction = 'rtl';
        } else {
          input.classList.remove('rtl-text-input');
          input.style.textAlign = '';
          input.style.direction = '';
        }
      }
    });

    // Ajuster les sliders et contrôles de formulaire directionnels
    const sliders = document.querySelectorAll('input[type="range"], .slider, [data-rtl-slider="true"]');
    sliders.forEach(slider => {
      if (toRTL) {
        slider.classList.add('rtl-slider');
      } else {
        slider.classList.remove('rtl-slider');
      }
    });
  };

  // Ajuster les éléments avec overflow pour RTL
  const adjustOverflowElements = (toRTL: boolean) => {
    // Inverser le scroll horizontal des éléments avec overflow
    const overflowElements = document.querySelectorAll('.overflow-x-auto, .overflow-x-scroll, [style*="overflow-x"], [data-rtl-overflow="true"]');
    overflowElements.forEach(el => {
      if (toRTL) {
        el.classList.add('rtl-overflow');
        // Inverser le scroll initial pour les éléments avec défilement horizontal
        if (el instanceof HTMLElement && el.scrollWidth > el.clientWidth) {
          el.scrollLeft = el.scrollWidth;
        }
      } else {
        el.classList.remove('rtl-overflow');
        if (el instanceof HTMLElement) {
          el.scrollLeft = 0;
        }
      }
    });
  };

  // Ajuster les éléments positionnés pour RTL
  const adjustPositionedElements = (toRTL: boolean) => {
    // Ajuster les éléments positionnés avec left/right
    const positionedElements = document.querySelectorAll('.fixed, .absolute, [data-rtl-positioned="true"]');
    positionedElements.forEach(el => {
      if (el instanceof HTMLElement) {
        if (toRTL) {
          if (el.style.left && !el.style.right) {
            el.dataset.originalLeft = el.style.left;
            el.style.right = el.style.left;
            el.style.left = '';
          }
        } else {
          if (el.dataset.originalLeft) {
            el.style.left = el.dataset.originalLeft;
            el.style.right = '';
            delete el.dataset.originalLeft;
          }
        }
      }
    });
  };

  // Ajuster les animations pour RTL
  const adjustAnimations = (toRTL: boolean) => {
    // Inverser les animations directionnelles
    const animatedElements = document.querySelectorAll('.animate-slide-left, .animate-slide-right, [data-rtl-animation="true"]');
    animatedElements.forEach(el => {
      if (toRTL) {
        if (el.classList.contains('animate-slide-left')) {
          el.classList.remove('animate-slide-left');
          el.classList.add('animate-slide-right');
        } else if (el.classList.contains('animate-slide-right')) {
          el.classList.remove('animate-slide-right');
          el.classList.add('animate-slide-left');
        }
      }
    });
  };

  // Ajuster les composants d'interface utilisateur spécifiques
  const adjustUIComponents = (toRTL: boolean) => {
    // Ajuster les onglets (tabs)
    const tabs = document.querySelectorAll('[role="tablist"], [data-rtl-tabs="true"], [data-orientation="horizontal"]');
    tabs.forEach(tab => {
      if (toRTL) {
        tab.classList.add('rtl-tabs');

        // Ne pas manipuler directement les styles inline des triggers
        // Ajouter une classe à la place
        const tabTriggers = tab.querySelectorAll('[role="tab"], [data-radix-collection-item]');
        tabTriggers.forEach(trigger => {
          trigger.classList.add('rtl-tab-trigger');
        });
      } else {
        tab.classList.remove('rtl-tabs');

        // Supprimer la classe
        const tabTriggers = tab.querySelectorAll('[role="tab"], [data-radix-collection-item]');
        tabTriggers.forEach(trigger => {
          trigger.classList.remove('rtl-tab-trigger');
        });
      }
    });

    // Ajuster les cards
    const cards = document.querySelectorAll('.card, [data-rtl-card="true"]');
    cards.forEach(card => {
      if (toRTL) {
        card.classList.add('rtl-card');
      } else {
        card.classList.remove('rtl-card');
      }
    });

    // Ajuster les menus déroulants
    const dropdowns = document.querySelectorAll('.dropdown, [data-rtl-dropdown="true"]');
    dropdowns.forEach(dropdown => {
      if (toRTL) {
        dropdown.classList.add('rtl-dropdown');
      } else {
        dropdown.classList.remove('rtl-dropdown');
      }
    });

    // Ajuster les mini-applications
    const miniApps = document.querySelectorAll('[data-mini-app="true"], [data-rtl-app="true"]');
    miniApps.forEach(app => {
      if (toRTL) {
        app.classList.add('rtl-mini-app');
      } else {
        app.classList.remove('rtl-mini-app');
      }
    });
  };

  // Utilisation d'une div avec classe pour appliquer le RTL au niveau du conteneur principal
  return (
    <div
      dir={isRTL ? 'rtl' : 'ltr'}
      className={`rtl-container ${isRTL ? 'rtl' : 'ltr'}`}
      data-language={language}
    >
      {children}
    </div>
  );
}