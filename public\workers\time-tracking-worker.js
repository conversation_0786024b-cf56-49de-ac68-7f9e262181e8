let intervalId = null;
let isRunning = false;

self.onmessage = function(event) {
  const { command } = event.data;

  if (command === 'start') {
    if (!isRunning) {
      isRunning = true;
      
      // Clear any existing interval before starting a new one
      if (intervalId) {
        clearInterval(intervalId);
      }

      intervalId = setInterval(() => {
        if (!isRunning) return;
        
        // Send a tick message every second
        self.postMessage({ 
          type: 'tick', 
          timestamp: Date.now() 
        });
      }, 1000); // Update every second
    }
  } else if (command === 'stop') {
    if (isRunning && intervalId) {
      // Stop the timer
      clearInterval(intervalId);
      intervalId = null;
      isRunning = false;
      
      // Send stop confirmation
      self.postMessage({ 
        type: 'stopped' 
      });
    }
  }
};
