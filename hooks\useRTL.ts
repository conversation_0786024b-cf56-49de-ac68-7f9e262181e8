import { useEffect, useState, useMemo } from 'react';

export const useRTL = (locale: string) => {
  // Utiliser useMemo pour éviter des recalculs inutiles
  const rtlLanguages = useMemo(() => [
    // Langues arabes
    'ar', // Arabe
    'ar-SA', 'ar-A<PERSON>', 'ar-BH', 'ar-DZ', 'ar-EG', 'ar-IQ', 'ar-J<PERSON>', 'ar-KW',
    'ar-LB', 'ar-LY', 'ar-MA', 'ar-OM', 'ar-QA', 'ar-SY', 'ar-TN', 'ar-YE',

    // Langues hébraïques
    'he', // Hébreu
    'he-IL',

    // Langues persanes
    'fa', // <PERSON><PERSON> (Persan)
    'fa-IR', 'fa-AF',

    // <PERSON>ues ourdoues
    'ur', // <PERSON><PERSON>u
    'ur-PK', 'ur-IN',

    // <PERSON><PERSON> azé<PERSON> (dialectes méridionaux utilisant l'alphabet arabe)
    'az-Arab', 'az-IR',

    // Autres langues RTL
    'ps', // Pashto
    'ps-AF',
    'sd', // Sindhi
    'sd-PK', 'sd-IN',
    'syr', // Syriaque
    'dv', // Divehi
    'ks', // Kashmiri dans les régions où il est écrit en alphabet arabe
    'ks-Arab',
    'ku-Arab', // Kurde en alphabet arabe
    'ku-IR', 'ku-IQ',
    'ug', // Ouïghour
    'ug-CN',
    'yi', // Yiddish
    'bqi', // Bakhtiari
    'mzn', // Mazandérani
    'ckb', // Kurde sorani
    'arc', // Araméen
    'khw', // Khowar en alphabet arabe
    'lrc', // Lori du Nord
    'lur' // Luri
  ], []);

  // Fonction optimisée pour détecter si une langue est RTL
  const isRTLLanguage = useMemo(() => {
    if (!locale) return false;

    // Vérifier la langue principale (avant le tiret)
    const mainLocale = locale.split('-')[0].toLowerCase();

    // Vérifier d'abord la langue complète, puis la langue principale
    const rtl = rtlLanguages.includes(locale) || rtlLanguages.includes(mainLocale);

    // Vérification supplémentaire pour les scripts RTL
    const rtlScripts = ['Arab', 'Hebr', 'Thaa', 'Syrc'];
    for (const script of rtlScripts) {
      if (locale.includes(`-${script}`)) {
        return true;
      }
    }

    return rtl;
  }, [locale, rtlLanguages]);

  // Return the RTL state directly from the memo to avoid state updates
  // This ensures the RTL value is available immediately during the first render
  return isRTLLanguage;
};