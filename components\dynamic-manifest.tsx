"use client";

import { useEffect } from "react";
import { useLanguage } from "@/components/language-provider";

export function DynamicManifest() {
  const { language } = useLanguage();

  useEffect(() => {
    // Mettre à jour le lien du manifest selon la langue
    const manifestLink = document.querySelector('link[rel="manifest"]') as HTMLLinkElement;
    if (manifestLink) {
      manifestLink.href = `/api/manifest?lang=${language}`;
    }
  }, [language]);

  return null; // Ce composant ne rend rien visuellement
}
