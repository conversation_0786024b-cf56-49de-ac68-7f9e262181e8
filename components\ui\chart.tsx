"use client"

// Simplified Bar Chart component
export function BarChart({
  data,
  index,
  categories,
  colors = ["primary"],
  valueFormatter = (value: number) => `${value}`,
  className,
}: {
  data: any[]
  index: string
  categories: string[]
  colors?: string[]
  valueFormatter?: (value: number) => string
  className?: string
}) {
  const maxValue = Math.max(...data.map((item) => Math.max(...categories.map((cat) => item[cat] || 0))))

  return (
    <div className={`w-full h-full flex flex-col ${className}`}>
      <div className="flex-1 flex items-end gap-2">
        {data.map((item, i) => (
          <div key={i} className="flex-1 flex flex-col items-center">
            <div className="w-full flex gap-1">
              {categories.map((category, catIndex) => {
                const value = item[category] || 0
                const height = `${(value / maxValue) * 100}%`
                const colorClass = colors[catIndex % colors.length] || colors[0]

                return (
                  <div key={category} className="flex-1 flex flex-col items-center">
                    <div className="w-full relative h-full min-h-[8px] bg-muted rounded-t-sm overflow-hidden">
                      <div
                        className={`absolute bottom-0 left-0 w-full bg-${colorClass} transition-all duration-500`}
                        style={{ height }}
                      />
                    </div>
                    <div className="text-xs mt-1 text-muted-foreground">{valueFormatter(value)}</div>
                  </div>
                )
              })}
            </div>
            <div className="text-xs mt-1 font-medium">{item[index]}</div>
          </div>
        ))}
      </div>

      {categories.length > 1 && (
        <div className="flex justify-center gap-4 mt-4">
          {categories.map((category, i) => (
            <div key={category} className="flex items-center gap-1.5">
              <div className={`h-3 w-3 rounded-sm bg-${colors[i % colors.length] || colors[0]}`} />
              <span className="text-xs">{category}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

// Simplified Line Chart component
export function LineChart({
  data,
  index,
  categories,
  colors = ["primary"],
  valueFormatter = (value: number) => `${value}`,
  className,
}: {
  data: any[]
  index: string
  categories: string[]
  colors?: string[]
  valueFormatter?: (value: number) => string
  className?: string
}) {
  return (
    <div className={`w-full h-full flex flex-col ${className}`}>
      <div className="text-center p-4 text-muted-foreground">
        Line chart visualization (simplified version)
        <div className="mt-4 grid grid-cols-1 gap-2">
          {data.map((item, i) => (
            <div key={i} className="flex justify-between items-center p-2 border-b">
              <span>{item[index]}</span>
              <div className="flex gap-4">
                {categories.map((category, catIndex) => (
                  <span key={category} className="font-mono">
                    {category}: {valueFormatter(item[category] || 0)}
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// Simplified Pie Chart component
export function PieChart({
  data,
  index,
  categories,
  colors = ["primary"],
  valueFormatter = (value: number) => `${value}`,
  className,
}: {
  data: any[]
  index: string
  categories: string[]
  colors?: string[]
  valueFormatter?: (value: number) => string
  className?: string
}) {
  const category = categories[0] // Pie charts typically use one category
  const total = data.reduce((sum, item) => sum + (item[category] || 0), 0)

  return (
    <div className={`w-full h-full flex flex-col ${className}`}>
      <div className="flex-1 grid grid-cols-1 gap-2">
        {data.map((item, i) => {
          const value = item[category] || 0
          const percentage = total > 0 ? (value / total) * 100 : 0
          const colorClass = colors[i % colors.length] || colors[0]

          return (
            <div key={i} className="flex items-center gap-2">
              <div className={`h-4 w-4 rounded-sm bg-${colorClass}`} />
              <div className="flex-1">
                <div className="flex justify-between">
                  <span>{item[index]}</span>
                  <span className="font-mono">
                    {valueFormatter(value)} ({percentage.toFixed(1)}%)
                  </span>
                </div>
                <div className="w-full h-1 bg-muted rounded-full mt-1 overflow-hidden">
                  <div className={`h-full bg-${colorClass}`} style={{ width: `${percentage}%` }} />
                </div>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}

