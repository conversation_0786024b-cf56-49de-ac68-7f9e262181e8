# Intégration du Système Audio Robuste

## 🎯 Objectif
Résoudre les problèmes d'effets sonores sur mobile quand l'écran est verrouillé en créant un système de gestion audio robuste.

## 🔧 Composants Créés

### 1. `components/robust-audio-manager.tsx`
**Gestionnaire audio principal** avec fonctionnalités avancées :
- ✅ Gestion avancée du contexte audio (AudioContext)
- ✅ Wake Lock API pour maintenir l'activité quand l'écran est verrouillé
- ✅ Système de retry automatique (5 tentatives max)
- ✅ Préchargement des fichiers audio
- ✅ Fallback vers sons synthétiques
- ✅ Gestion des changements de visibilité de page
- ✅ Volume optimisé pour mobile (1.5x)
- ✅ Compatibilité iOS/Android

### 2. `hooks/use-robust-audio.tsx`
**Hooks React** pour utiliser le système audio :
- `useRobustAudio()` - Hook de base
- `useTimerAudio()` - Hook spécialisé pour les minuteries
- `useVisibilityAudio()` - Hook pour gérer la visibilité de page

### 3. `components/audio-test-panel.tsx`
**Interface de test** pour vérifier le fonctionnement :
- Tests des différents types de sons
- Monitoring de l'état audio en temps réel
- Tests spécifiques mobile (écran verrouillé, Wake Lock)
- Instructions détaillées

### 4. `components/meeting-timer-audio-test.tsx`
**Test spécialisé pour le meeting timer** avec :
- Simulation de meeting avec sons automatiques
- Tests des scénarios spécifiques aux meetings
- Monitoring en temps réel du timer

### 5. `components/debug-audio-hook.tsx`
**Composant de debug** pour vérifier l'état du hook useTimerAudio

### 6. `components/simple-audio-test.tsx`
**Test simple** pour vérifier les scénarios de base

### 7. `components/meeting-timer-debug.tsx`
**Debug spécialisé** pour reproduire exactement le comportement du meeting timer

### 8. `components/exercise-timer-debug.tsx`
**Debug spécialisé** pour reproduire exactement le comportement d'exercise-templates

### 9. `app/[lang]/test-audio/page.tsx`
**Page de test complète** accessible via `/fr/test-audio` (ou autre langue)

## 🔄 Composants Mis à Jour

### 1. `components/sound-provider.tsx`
- ✅ Remplacé l'ancien système par le nouveau système robuste
- ✅ Ajout des nouvelles propriétés (isAudioReady, audioState, hasWakeLock, etc.)
- ✅ Fonction `playSound` maintenant asynchrone avec Promise<boolean>

### 2. `components/countdown-timer.tsx`
- ✅ Utilise maintenant le système audio robuste
- ✅ Gestion des promesses pour la lecture audio
- ✅ Logs détaillés pour le debugging

### 3. `components/intervals.tsx`
- ✅ Migration vers `useTimerAudio()`
- ✅ Gestion robuste des sons de fin d'intervalle
- ✅ Vérification de l'état `soundEnabled`

### 4. `components/pomodoro.tsx`
- ✅ Migration vers `useTimerAudio()`
- ✅ Sons robustes pour les changements de phase
- ✅ Sons robustes pour la fin des sessions

### 5. `components/workout-intervals.tsx`
- ✅ Migration vers `useTimerAudio()`
- ✅ Sons robustes pour les intervalles d'entraînement
- ✅ Gestion des erreurs améliorée

### 6. `components/exercise-templates.tsx`
- ✅ Migration vers `useTimerAudio()`
- ✅ Sons robustes pour les timers de repos
- ✅ Sons robustes pour la fin des entraînements

### 7. `components/meeting-timer.tsx`
- ✅ Migration vers `useTimerAudio()`
- ✅ Sons robustes pour les événements de meeting
- ✅ Gestion des sons via Web Worker
- ✅ Sons robustes pour les changements d'items d'agenda

### 8. `components/timer-stopwatch.tsx`
- ✅ Migration vers `useTimerAudio()`
- ✅ Sons robustes pour les tours de chronomètre
- ✅ Gestion des erreurs améliorée

## 🚀 Fonctionnalités Clés

### Wake Lock API
```typescript
// Demande automatique quand la page est cachée
if (document.hidden) {
  await this.requestWakeLock()
}
```

### Retry Automatique
```typescript
// Jusqu'à 5 tentatives avec délai croissant
for (let attempt = 0; attempt < 3 && !success; attempt++) {
  if (attempt > 0) {
    await new Promise(resolve => setTimeout(resolve, 100 * attempt))
  }
  success = await robustAudio.playSound(type)
}
```

### Préchargement Audio
```typescript
// Précharge les fichiers audio au démarrage
const audio = new Audio("/sound/bell.mp3")
audio.preload = "auto"
audio.setAttribute("playsinline", "true") // Mobile
```

### Gestion de Visibilité
```typescript
// Détecte les changements de visibilité
document.addEventListener("visibilitychange", () => {
  if (document.hidden) {
    this.requestWakeLock()
  } else {
    this.ensureAudioContext()
  }
})
```

## 📱 Tests Mobile

### Comment tester :

#### Test Général :
1. Aller sur `/test-audio`
2. Activer le son
3. Tester les sons de base
4. **Verrouiller l'écran mobile**
5. Attendre 5-10 secondes
6. Tester un son (devrait fonctionner même écran verrouillé)
7. Déverrouiller et vérifier les résultats

#### Test Meeting Timer :
1. Utiliser le panneau "Test Audio Meeting Timer"
2. Démarrer la simulation de meeting
3. **Verrouiller l'écran mobile** pendant la simulation
4. Vérifier que les sons se jouent toutes les 30 secondes même écran verrouillé
5. Déverrouiller et vérifier les résultats

### Résultats attendus :
- ✅ Sons audibles même écran verrouillé
- ✅ Wake Lock activé automatiquement
- ✅ Retry automatique en cas d'échec
- ✅ Fallback vers sons synthétiques
- ✅ Logs détaillés dans la console

## 🔧 Configuration

### Audio Config
```typescript
const AUDIO_CONFIG = {
  maxRetries: 5,
  retryDelay: 200,
  contextResumeTimeout: 10000,
  wakeLockTimeout: 60000,
  preloadSounds: true,
  volumeBoost: 1.5, // Pour mobile
  soundDuration: {
    bell: 1000,
    alarm: 2000,
    notification: 500
  }
}
```

## 🎵 Types de Sons Supportés

1. **"bell"** - Son de cloche (fichier + synthétique)
2. **"alarm"** - Son d'alarme (synthétique)
3. **"notification"** - Son de notification (synthétique)

## 🔍 Debugging

### Logs disponibles :
- `[RobustAudio]` - Gestionnaire principal
- `[useRobustAudio]` - Hook de base
- `[useTimerAudio]` - Hook minuteries
- `[SoundProvider]` - Provider React

### États monitorés :
- `isAudioReady` - Contexte audio prêt
- `isPlaying` - Son en cours de lecture
- `hasWakeLock` - Wake Lock actif
- `audioState` - État du contexte (running/suspended/closed)
- `lastError` - Dernière erreur

## 🚨 Points d'Attention

1. **Wake Lock** nécessite HTTPS en production
2. **Interaction utilisateur** requise avant premier son
3. **Permissions** peuvent être demandées par le navigateur
4. **Compatibilité** vérifiée sur iOS Safari et Chrome Android

## 📈 Améliorations Futures

- [ ] Support des sons personnalisés
- [ ] Gestion des playlists
- [ ] Effets audio avancés
- [ ] Synchronisation multi-onglets
- [ ] Persistance des préférences audio
