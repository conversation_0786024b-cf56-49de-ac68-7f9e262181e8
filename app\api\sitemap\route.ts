import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET() {
  try {
    // Chemin vers le fichier sitemap.xml
    const sitemapPath = path.join(process.cwd(), 'public', 'sitemap.xml');
    
    // Vérifier si le fichier existe
    if (!fs.existsSync(sitemapPath)) {
      return new NextResponse('Sitemap non trouvé', { status: 404 });
    }
    
    // Lire le contenu du fichier
    const sitemapContent = fs.readFileSync(sitemapPath, 'utf-8');
    
    // Retourner le contenu avec l'en-tête Content-Type approprié
    return new NextResponse(sitemapContent, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=86400, s-maxage=86400', // Cache pendant 24 heures
      },
    });
  } catch (error) {
    console.error('Erreur lors du chargement du sitemap:', error);
    return new NextResponse('Erreur lors du chargement du sitemap', { status: 500 });
  }
} 