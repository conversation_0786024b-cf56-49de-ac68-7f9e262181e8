"use client"

import { useState, useEffect, useRef } from "react"
import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { getTranslations } from "@/lib/i18n/translations"
import { Play, Pause, RefreshCw, Bell } from "lucide-react"
import { useTimerAudio } from "@/hooks/use-robust-audio"
import { useRTL } from "@/hooks/useRTL"
import { useLanguage } from "@/components/language-provider"
import { ToolControls } from "@/components/tool-controls"
import { ToolFullscreenWrapper } from "@/components/tool-fullscreen-wrapper"
import { trackToolUsage, trackTimerEvent } from "@/components/google-analytics"
import { useToast } from "@/hooks/use-toast"

interface TimerStopwatchProps {
  lang: string;
  isStandalone?: boolean; // Indique si le composant est utilisé dans la page timer-stopwatch
}

export function TimerStopwatch({ lang, isStandalone = false }: TimerStopwatchProps) {
  const t = getTranslations(lang)
  const { language } = useLanguage()
  const isRTL = useRTL(language || lang)

  // Inverser l'ordre des boutons pour les langues RTL
  const shouldReverseButtons = isRTL

  // Pour les laps, nous n'inversons pas l'ordre car cela cause des problèmes
  const [isRunning, setIsRunning] = useState(false)
  const [time, setTime] = useState(0) // Elapsed time in milliseconds
  const [laps, setLaps] = useState<number[]>([])
  const [isToolFullscreen, setIsToolFullscreen] = useState(false)
  const workerRef = useRef<Worker | null>(null) // Ref to hold the worker instance
  const originalTitle = useRef<string>("")

  // Utiliser le hook audio robuste au lieu de gérer le son directement
  const { playTimerSound, soundEnabled } = useTimerAudio()
  const { toast } = useToast() // Initialize toast

  // Sauvegarder le titre original de la page au chargement du composant
  useEffect(() => {
    originalTitle.current = document.title
    return () => {
      document.title = originalTitle.current
    }
  }, [])

  // Mettre à jour le titre de l'onglet du navigateur avec le temps actuel
  useEffect(() => {
    if (isRunning || time > 0) {
      document.title = `${formatTime(time)} - ${t.stopwatch || "Chronomètre"}`
    } else {
      document.title = originalTitle.current
    }
  }, [time, isRunning, t])

  // Initialize and manage the Web Worker
  useEffect(() => {
    // Create worker instance
    workerRef.current = new Worker('/workers/simple-stopwatch-worker.js')

    // Message handler
    workerRef.current.onmessage = (event) => {
      const { type, timeElapsed } = event.data

      if (type === 'tick') {
        // Update component state with time from worker
        setTime(timeElapsed)
      } else if (type === 'paused') {
        // Update final time when paused
        setTime(timeElapsed)
      } else if (type === 'reset') {
        // Reset time when worker resets
        setTime(0)
      }
    }

    // Error handler
    workerRef.current.onerror = (error) => {
      console.error("Stopwatch worker error:", error)
      toast({
        title: "Timer Error",
        description: "An issue occurred with the background timer.",
        variant: "destructive",
      })
      setIsRunning(false) // Stop timer on worker error
    }

    // Cleanup: Terminate worker when component unmounts
    return () => {
      if (workerRef.current) {
        workerRef.current.postMessage({ command: 'reset' }) // Ensure timer is stopped
        workerRef.current.terminate()
        workerRef.current = null
        console.log("Stopwatch worker terminated.")
      }
    }
  }, [toast]) // Add toast dependency for error handling

  const toggleTimer = () => {
    if (!workerRef.current) {
      console.error("Worker not initialized!")
      toast({
        title: "Error",
        description: "Timer worker failed to load.",
        variant: "destructive"
      })
      return
    }

    const newRunningState = !isRunning
    setIsRunning(newRunningState)

    if (newRunningState) {
      // Start the timer
      workerRef.current.postMessage({ command: 'start' })
      trackTimerEvent('stopwatch', time, 'start')
      trackToolUsage('stopwatch', 'start')
    } else {
      // Pause the timer
      workerRef.current.postMessage({ command: 'pause' })
      trackTimerEvent('stopwatch', time, 'pause')
      trackToolUsage('stopwatch', 'pause')
    }
  }

  const resetTimer = () => {
    // Track reset event before resetting
    trackTimerEvent('stopwatch', time, 'reset')
    trackToolUsage('stopwatch', 'reset')

    if (!workerRef.current) {
      console.error("Worker not initialized!")
      return
    }

    // Reset worker state
    workerRef.current.postMessage({ command: 'reset' })

    // Reset component state
    setIsRunning(false)
    setTime(0)
    setLaps([])

    // Restaurer le titre original
    document.title = originalTitle.current
  }

  // Modifier la fonction addLap pour ajouter une gestion d'erreur
  const addLap = () => {
    setLaps([...laps, time])

    // Track lap event
    trackToolUsage('stopwatch', 'lap')
    trackTimerEvent('stopwatch', time, 'lap')

    // Ajouter un petit délai pour s'assurer que le son se déclenche avec audio robuste
    if (soundEnabled) {
      setTimeout(() => {
        playTimerSound("bell").then((success) => {
          if (success) {
            console.log("Stopwatch lap sound played successfully");
          } else {
            console.warn("Failed to play stopwatch lap sound");
          }
        }).catch((error) => {
          console.warn("Could not play stopwatch lap sound:", error);
        });
      }, 10)
    }
  }

  const formatTime = (ms: number) => {
    const minutes = Math.floor(ms / 60000)
    const seconds = Math.floor((ms % 60000) / 1000)
    const milliseconds = Math.floor((ms % 1000) / 10)

    return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}.${milliseconds.toString().padStart(2, "0")}`
  }

  const timerContent = (
    <div className="flex flex-col items-center justify-center py-12">
      <motion.div
        className="text-7xl md:text-8xl font-bold font-mono mb-12"
        // key={time} // Removed key to prevent flickering on every time update
        initial={{ scale: 0.95 }}
        animate={{ scale: 1 }}
        transition={{ duration: 0.1 }}
      >
        {formatTime(time)}
      </motion.div>

      <div className="flex gap-6 mb-12" data-rtl-flex="true">
        <Button
          onClick={toggleTimer}
          size="lg"
          className="w-32 h-32 rounded-full"
          variant={isRunning ? "destructive" : "default"}
        >
          {isRunning ? <Pause className="h-10 w-10" /> : <Play className="h-10 w-10" />}
        </Button>

        <Button
          onClick={resetTimer}
          size="lg"
          variant="outline"
          className="w-20 h-20 rounded-full"
          disabled={time === 0}
        >
          <RefreshCw className="h-8 w-8" />
        </Button>

        <Button
          onClick={addLap}
          size="lg"
          variant="outline"
          className="w-20 h-20 rounded-full"
          disabled={!isRunning && time === 0}
        >
          <Bell className="h-8 w-8" />
        </Button>
      </div>

      {laps.length > 0 && (
        <div className="w-full max-h-60 overflow-auto border rounded-lg p-4" dir={isRTL ? 'rtl' : 'ltr'} style={{ textAlign: isRTL ? 'right' : 'left' }}>
          <h3 className="font-medium mb-2">{t.laps}</h3>
          <ul className="space-y-2">
            {laps.map((lap, index) => (
              <li key={index} className="py-1 border-b last:border-0" style={{
                display: 'flex',
                justifyContent: 'space-between',
                direction: isRTL ? 'rtl' : 'ltr'
              }}>
                <div style={{ textAlign: isRTL ? 'right' : 'left' }}>
                  {t.lap} {index + 1}
                </div>
                <div className="font-mono" style={{ textAlign: isRTL ? 'left' : 'right' }}>
                  {formatTime(lap)}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  )

  return (
    <>
      <Card className="w-full max-w-4xl mx-auto overflow-hidden">
        <CardContent>
          {/* Contrôles d'outils - adapté RTL */}
          <div className={`flex pt-4 pb-2 ${
            isRTL ? 'justify-start' : 'justify-end'
          }`}>
            <ToolControls
              onFullscreenToggle={setIsToolFullscreen}
              variant="compact"
            />
          </div>

          {timerContent}
        </CardContent>
      </Card>

      {/* Wrapper plein écran */}
      <ToolFullscreenWrapper
        isFullscreen={isToolFullscreen}
        onClose={() => setIsToolFullscreen(false)}
        toolName={t.stopwatch}
      >
        <Card className="w-full border-0 shadow-none bg-transparent">
          <CardContent>
            {timerContent}
          </CardContent>
        </Card>
      </ToolFullscreenWrapper>
    </>
  )
}
