"use client"

import type React from "react"

import { createContext, useContext, useState, useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"
import { languages } from "@/lib/i18n/languages"
import { getTranslatedRoute, getOriginalRoute } from "@/lib/i18n/route-translations"

type LanguageContextType = {
  language: string
  setLanguage: (lang: string) => void
  isChangingLanguage: boolean
}

const LanguageContext = createContext<LanguageContextType>({
  language: "en",
  setLanguage: () => {},
  isChangingLanguage: false,
})

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  // Get initial language from URL during SSR/hydration to prevent flicker
  const pathname = usePathname()
  const initialUrlLang = pathname.split("/")[1]
  const isValidInitialLang = languages.some((lang) => lang.code === initialUrlLang)
  const initialLang = isValidInitialLang ? initialUrlLang : "en"

  const [language, setLanguageState] = useState(initialLang)
  const [isChangingLanguage, setIsChangingLanguage] = useState(false)
  const [isInitialized, setIsInitialized] = useState(false)
  const router = useRouter()

  // Set language on mount and when pathname changes
  useEffect(() => {
    // Extract language from URL or use browser language
    const urlLang = pathname.split("/")[1]
    const isValidLang = languages.some((lang) => lang.code === urlLang)

    if (isValidLang) {
      setLanguageState(urlLang)
    } else if (!isInitialized) {
      // Only try to use browser language on first load
      const browserLang = navigator.language.split("-")[0]
      const validBrowserLang = languages.some((lang) => lang.code === browserLang)
      setLanguageState(validBrowserLang ? browserLang : "en")
    }

    setIsInitialized(true)
  }, [pathname, isInitialized])

  const setLanguage = (lang: string) => {
    if (lang === language) return

    setIsChangingLanguage(true)
    setLanguageState(lang)

    // Update URL to reflect language change
    const segments = pathname.split("/")
    const currentLang = segments[1]
    const isValidLang = languages.some((l) => l.code === currentLang)

    if (isValidLang) {
      // Si nous avons une route après la langue
      if (segments.length > 2 && segments[2]) {
        try {
          // Décoder la route pour gérer les caractères spéciaux
          const decodedRoutePart = decodeURIComponent(segments[2])

          // Liste des routes originales disponibles
          const availableRoutes = [
            'timer', 'countdown', 'todo', 'time-tracking', 'world-clock',
            'intervals', 'pomodoro', 'meeting-timer', 'time-billing',
            'workout-intervals', 'exercise-templates'
          ];

          // Vérifier si la route actuelle est une route originale
          if (availableRoutes.includes(decodedRoutePart)) {
            // Si c'est une route originale, la traduire directement
            const newTranslatedRoute = getTranslatedRoute(lang, decodedRoutePart)
            segments[1] = lang
            segments[2] = newTranslatedRoute
          } else {
            // Sinon, essayer de trouver la route originale correspondante
            let originalRoute = decodedRoutePart // Par défaut

            for (const route of availableRoutes) {
              const translatedRoute = getTranslatedRoute(currentLang, route)
              if (translatedRoute === decodedRoutePart) {
                originalRoute = route
                break
              }
            }

            // Traduire la route originale dans la nouvelle langue
            const newTranslatedRoute = getTranslatedRoute(lang, originalRoute)
            segments[1] = lang
            segments[2] = newTranslatedRoute
          }
        } catch (error) {
          console.error("Error in language change:", error)
          // En cas d'erreur, simplement changer la langue
          segments[1] = lang
        }
      } else {
        segments[1] = lang
      }
    } else {
      segments.splice(1, 0, lang)
    }

    const newPath = segments.join("/")
    router.push(newPath)

    // Reset the changing state after navigation
    setTimeout(() => {
      setIsChangingLanguage(false)
    }, 500)
  }

  return (
    <LanguageContext.Provider value={{ language, setLanguage, isChangingLanguage }}>
      {children}
    </LanguageContext.Provider>
  )
}

export const useLanguage = () => useContext(LanguageContext)

