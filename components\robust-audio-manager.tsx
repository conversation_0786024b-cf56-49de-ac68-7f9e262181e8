"use client"

import { useCallback, useEffect, useRef, useState } from "react"

// Configuration pour la gestion audio robuste
const AUDIO_CONFIG = {
  maxRetries: 5,
  retryDelay: 200,
  contextResumeTimeout: 10000,
  wakeLockTimeout: 60000, // 1 minute
  preloadSounds: true,
  volumeBoost: 1.5, // Augmenter le volume pour mobile
  soundDuration: {
    bell: 1000,
    alarm: 2000,
    notification: 500
  }
}

export type SoundType = "bell" | "alarm" | "notification"

interface AudioState {
  isReady: boolean
  isPlaying: boolean
  hasWakeLock: boolean
  contextState: AudioContextState | null
  lastError: string | null
}

export class RobustAudioManager {
  private audioContext: AudioContext | null = null
  private wakeLock: WakeLockSentinel | null = null
  private preloadedAudio: Map<SoundType, HTMLAudioElement> = new Map()
  private retryTimeouts: Map<string, NodeJS.Timeout> = new Map()
  private wakeLockTimeout: NodeJS.Timeout | null = null
  private visibilityChangeHandler: (() => void) | null = null
  private state: AudioState = {
    isReady: false,
    isPlaying: false,
    hasWakeLock: false,
    contextState: null,
    lastError: null
  }
  private stateChangeCallbacks: Set<(state: AudioState) => void> = new Set()

  constructor() {
    this.initializeAudio()
    this.setupVisibilityHandling()
    this.preloadAudioFiles()
  }

  // Initialiser le contexte audio
  private async initializeAudio(): Promise<void> {
    try {
      if (typeof window === "undefined") return

      const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext
      if (!AudioContextClass) {
        throw new Error("AudioContext not supported")
      }

      this.audioContext = new AudioContextClass()
      this.updateState({ contextState: this.audioContext.state })

      // Débloquer immédiatement si possible
      await this.unlockAudioContext()

      console.log("[RobustAudio] AudioContext initialized successfully")
    } catch (error) {
      console.error("[RobustAudio] Failed to initialize AudioContext:", error)
      this.updateState({ lastError: `Init failed: ${error}` })
    }
  }

  // Débloquer le contexte audio
  private async unlockAudioContext(): Promise<boolean> {
    if (!this.audioContext) return false

    try {
      if (this.audioContext.state === "suspended") {
        await this.audioContext.resume()
        console.log("[RobustAudio] AudioContext resumed")
      }

      // Jouer un son silencieux pour débloquer complètement
      await this.playSilentSound()
      
      this.updateState({ 
        isReady: true, 
        contextState: this.audioContext.state,
        lastError: null 
      })
      return true
    } catch (error) {
      console.error("[RobustAudio] Failed to unlock AudioContext:", error)
      this.updateState({ lastError: `Unlock failed: ${error}` })
      return false
    }
  }

  // Jouer un son silencieux pour débloquer l'audio
  private async playSilentSound(): Promise<void> {
    if (!this.audioContext) return

    try {
      const oscillator = this.audioContext.createOscillator()
      const gainNode = this.audioContext.createGain()

      gainNode.gain.value = 0.001
      oscillator.connect(gainNode)
      gainNode.connect(this.audioContext.destination)

      oscillator.start(0)
      oscillator.stop(0.001)

      // Attendre un peu pour s'assurer que le son est joué
      await new Promise(resolve => setTimeout(resolve, 10))
    } catch (error) {
      console.warn("[RobustAudio] Failed to play silent sound:", error)
    }
  }

  // Précharger les fichiers audio
  private preloadAudioFiles(): void {
    if (!AUDIO_CONFIG.preloadSounds) return

    const soundFiles = {
      bell: "/sound/bell.mp3"
    }

    Object.entries(soundFiles).forEach(([type, url]) => {
      try {
        const audio = new Audio(url)
        audio.preload = "auto"
        audio.volume = Math.min(AUDIO_CONFIG.volumeBoost, 1.0)
        
        // Configurer pour mobile
        audio.setAttribute("playsinline", "true")
        audio.setAttribute("webkit-playsinline", "true")
        
        this.preloadedAudio.set(type as SoundType, audio)
        console.log(`[RobustAudio] Preloaded ${type} sound`)
      } catch (error) {
        console.warn(`[RobustAudio] Failed to preload ${type} sound:`, error)
      }
    })
  }

  // Gérer les changements de visibilité
  private setupVisibilityHandling(): void {
    if (typeof document === "undefined") return

    this.visibilityChangeHandler = () => {
      if (document.hidden) {
        console.log("[RobustAudio] Page hidden - maintaining audio context")
        this.requestWakeLock()
      } else {
        console.log("[RobustAudio] Page visible - ensuring audio context")
        this.ensureAudioContext()
        this.releaseWakeLock()
      }
    }

    document.addEventListener("visibilitychange", this.visibilityChangeHandler)
  }

  // Demander un Wake Lock
  private async requestWakeLock(): Promise<void> {
    if (!("wakeLock" in navigator) || this.wakeLock) return

    try {
      this.wakeLock = await navigator.wakeLock.request("screen")
      this.updateState({ hasWakeLock: true })
      console.log("[RobustAudio] Wake lock acquired")

      // Libérer automatiquement après un délai
      this.wakeLockTimeout = setTimeout(() => {
        this.releaseWakeLock()
      }, AUDIO_CONFIG.wakeLockTimeout)

      this.wakeLock.addEventListener("release", () => {
        console.log("[RobustAudio] Wake lock released")
        this.updateState({ hasWakeLock: false })
      })
    } catch (error) {
      console.warn("[RobustAudio] Failed to acquire wake lock:", error)
    }
  }

  // Libérer le Wake Lock
  private releaseWakeLock(): void {
    if (this.wakeLock) {
      this.wakeLock.release()
      this.wakeLock = null
      this.updateState({ hasWakeLock: false })
    }

    if (this.wakeLockTimeout) {
      clearTimeout(this.wakeLockTimeout)
      this.wakeLockTimeout = null
    }
  }

  // S'assurer que le contexte audio est prêt
  private async ensureAudioContext(): Promise<boolean> {
    if (!this.audioContext) {
      await this.initializeAudio()
    }

    if (this.audioContext?.state === "suspended") {
      return await this.unlockAudioContext()
    }

    return this.audioContext?.state === "running"
  }

  // Mettre à jour l'état et notifier les callbacks
  private updateState(updates: Partial<AudioState>): void {
    this.state = { ...this.state, ...updates }
    this.stateChangeCallbacks.forEach(callback => callback(this.state))
  }

  // Ajouter un callback pour les changements d'état
  public onStateChange(callback: (state: AudioState) => void): () => void {
    this.stateChangeCallbacks.add(callback)
    return () => this.stateChangeCallbacks.delete(callback)
  }

  // Jouer un son avec retry automatique
  public async playSound(type: SoundType, retryCount = 0): Promise<boolean> {
    console.log(`[RobustAudio] Playing ${type} sound (attempt ${retryCount + 1})`)
    
    this.updateState({ isPlaying: true })

    try {
      // S'assurer que le contexte audio est prêt
      const isReady = await this.ensureAudioContext()
      if (!isReady) {
        throw new Error("AudioContext not ready")
      }

      // Demander un Wake Lock si la page est cachée
      if (document.hidden) {
        await this.requestWakeLock()
      }

      // Essayer d'abord le fichier audio préchargé
      const success = await this.tryPreloadedAudio(type)
      if (success) {
        this.updateState({ isPlaying: false, lastError: null })
        return true
      }

      // Fallback vers le son synthétique
      await this.playSyntheticSound(type)
      this.updateState({ isPlaying: false, lastError: null })
      return true

    } catch (error) {
      console.error(`[RobustAudio] Error playing ${type} sound:`, error)
      this.updateState({ lastError: `Play failed: ${error}` })

      // Retry automatique
      if (retryCount < AUDIO_CONFIG.maxRetries) {
        const retryKey = `${type}-${Date.now()}`
        const timeout = setTimeout(() => {
          this.retryTimeouts.delete(retryKey)
          this.playSound(type, retryCount + 1)
        }, AUDIO_CONFIG.retryDelay * (retryCount + 1))
        
        this.retryTimeouts.set(retryKey, timeout)
        return false
      }

      this.updateState({ isPlaying: false })
      return false
    }
  }

  // Essayer de jouer le fichier audio préchargé
  private async tryPreloadedAudio(type: SoundType): Promise<boolean> {
    const audio = this.preloadedAudio.get(type)
    if (!audio) return false

    try {
      // Réinitialiser la position
      audio.currentTime = 0
      
      // Jouer le son
      await audio.play()
      console.log(`[RobustAudio] Played preloaded ${type} sound`)
      return true
    } catch (error) {
      console.warn(`[RobustAudio] Failed to play preloaded ${type} sound:`, error)
      return false
    }
  }

  // Jouer un son synthétique
  private async playSyntheticSound(type: SoundType): Promise<void> {
    if (!this.audioContext) throw new Error("No AudioContext")

    const ctx = this.audioContext
    const now = ctx.currentTime
    const oscillator = ctx.createOscillator()
    const gainNode = ctx.createGain()

    // Configuration selon le type de son
    switch (type) {
      case "bell":
        oscillator.type = "sine"
        oscillator.frequency.value = 600
        gainNode.gain.setValueAtTime(0, now)
        gainNode.gain.linearRampToValueAtTime(0.4 * AUDIO_CONFIG.volumeBoost, now + 0.02)
        gainNode.gain.linearRampToValueAtTime(0.2 * AUDIO_CONFIG.volumeBoost, now + 0.1)
        gainNode.gain.linearRampToValueAtTime(0, now + 0.5)
        break

      case "alarm":
        oscillator.type = "sawtooth"
        oscillator.frequency.value = 800
        gainNode.gain.setValueAtTime(0, now)
        gainNode.gain.linearRampToValueAtTime(0.5 * AUDIO_CONFIG.volumeBoost, now + 0.05)
        gainNode.gain.linearRampToValueAtTime(0.1 * AUDIO_CONFIG.volumeBoost, now + 0.25)
        gainNode.gain.linearRampToValueAtTime(0.5 * AUDIO_CONFIG.volumeBoost, now + 0.5)
        gainNode.gain.linearRampToValueAtTime(0, now + 1.0)
        break

      case "notification":
        oscillator.type = "sine"
        oscillator.frequency.setValueAtTime(1200, now)
        oscillator.frequency.exponentialRampToValueAtTime(500, now + 0.3)
        gainNode.gain.setValueAtTime(0, now)
        gainNode.gain.linearRampToValueAtTime(0.3 * AUDIO_CONFIG.volumeBoost, now + 0.02)
        gainNode.gain.linearRampToValueAtTime(0, now + 0.3)
        break
    }

    oscillator.connect(gainNode)
    gainNode.connect(ctx.destination)

    const duration = AUDIO_CONFIG.soundDuration[type] / 1000
    oscillator.start(now)
    oscillator.stop(now + duration)

    // Attendre la fin du son
    await new Promise(resolve => setTimeout(resolve, duration * 1000))
    
    console.log(`[RobustAudio] Played synthetic ${type} sound`)
  }

  // Nettoyer les ressources
  public cleanup(): void {
    // Arrêter tous les timeouts de retry
    this.retryTimeouts.forEach(timeout => clearTimeout(timeout))
    this.retryTimeouts.clear()

    // Libérer le Wake Lock
    this.releaseWakeLock()

    // Nettoyer les event listeners
    if (this.visibilityChangeHandler) {
      document.removeEventListener("visibilitychange", this.visibilityChangeHandler)
    }

    // Fermer le contexte audio
    if (this.audioContext && this.audioContext.state !== "closed") {
      this.audioContext.close()
    }

    // Nettoyer les callbacks
    this.stateChangeCallbacks.clear()

    console.log("[RobustAudio] Cleanup completed")
  }

  // Obtenir l'état actuel
  public getState(): AudioState {
    return { ...this.state }
  }
}
