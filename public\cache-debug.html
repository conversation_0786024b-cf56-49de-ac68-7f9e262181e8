<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cache Debug - Timer Kit</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2rem;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .section h2 {
            margin-bottom: 15px;
            color: #fff;
            font-size: 1.3rem;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        
        button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .status {
            padding: 15px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            margin-bottom: 15px;
            font-family: monospace;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .status.success {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid rgba(0, 255, 0, 0.3);
        }
        
        .status.error {
            background: rgba(255, 0, 0, 0.1);
            border: 1px solid rgba(255, 0, 0, 0.3);
        }
        
        .log {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.8rem;
            line-height: 1.3;
            white-space: pre-wrap;
        }
        
        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        
        .test-link {
            display: block;
            padding: 10px 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        
        .test-link:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .info-item {
            padding: 10px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 6px;
            font-size: 0.9rem;
        }
        
        .info-label {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            .button-group {
                flex-direction: column;
            }
            
            button {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Cache Debug Tool</h1>
        
        <div class="section">
            <h2>📊 PWA Status</h2>
            <div id="pwa-status" class="status">Checking PWA status...</div>
            <div class="button-group">
                <button onclick="checkPWAStatus()">Refresh Status</button>
                <button onclick="forcePWAInstall()">Force Mark as Installed</button>
            </div>
        </div>
        
        <div class="section">
            <h2>🗂️ Cache Management</h2>
            <div class="button-group">
                <button onclick="diagnoseCacheContents()">Diagnose Cache</button>
                <button onclick="forceCacheLanguage('fr')">Force Cache French</button>
                <button onclick="forceCacheLanguage('en')">Force Cache English</button>
                <button onclick="forceCacheAll()">Force Cache All</button>
                <button onclick="clearAllCaches()" style="background: rgba(255, 0, 0, 0.3);">Clear All Cache</button>
            </div>
            <div id="cache-status" class="status">Ready to test cache operations...</div>
        </div>
        
        <div class="section">
            <h2>🌐 Test Offline Navigation</h2>
            <p style="margin-bottom: 15px;">Test these links while offline to see if they load from cache:</p>
            <div class="test-links">
                <a href="/fr" class="test-link">🇫🇷 French Home</a>
                <a href="/en" class="test-link">🇬🇧 English Home</a>
                <a href="/fr/chronometre" class="test-link">🇫🇷 Stopwatch (FR)</a>
                <a href="/en/stopwatch" class="test-link">🇬🇧 Stopwatch (EN)</a>
                <a href="/fr/minuteur" class="test-link">🇫🇷 Timer (FR)</a>
                <a href="/en/timer" class="test-link">🇬🇧 Timer (EN)</a>
                <a href="/fr/horloge-mondiale" class="test-link">🇫🇷 World Clock (FR)</a>
                <a href="/en/world-clock" class="test-link">🇬🇧 World Clock (EN)</a>
            </div>
        </div>
        
        <div class="section">
            <h2>📝 Service Worker Log</h2>
            <div id="sw-log" class="log">Service Worker logs will appear here...</div>
            <div class="button-group">
                <button onclick="clearLog()">Clear Log</button>
                <button onclick="testServiceWorker()">Test SW Communication</button>
            </div>
        </div>
        
        <div class="section">
            <h2>ℹ️ Browser Information</h2>
            <div id="browser-info" class="info-grid">
                <div class="info-item">
                    <div class="info-label">Loading...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let logContainer = document.getElementById('sw-log');
        let originalConsoleLog = console.log;
        let originalConsoleError = console.error;
        let originalConsoleWarn = console.warn;

        // Capture console logs
        function captureLog(level, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            logContainer.textContent += `[${timestamp}] ${level.toUpperCase()}: ${message}\n`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        console.log = (...args) => {
            originalConsoleLog.apply(console, args);
            captureLog('log', ...args);
        };

        console.error = (...args) => {
            originalConsoleError.apply(console, args);
            captureLog('error', ...args);
        };

        console.warn = (...args) => {
            originalConsoleWarn.apply(console, args);
            captureLog('warn', ...args);
        };

        // PWA Status Check
        function checkPWAStatus() {
            const statusDiv = document.getElementById('pwa-status');
            
            const isStandalone = window.matchMedia("(display-mode: standalone)").matches;
            const isInWebAppiOS = window.navigator.standalone === true;
            const isPWAInstalled = isStandalone || isInWebAppiOS;
            
            const swRegistered = 'serviceWorker' in navigator;
            
            statusDiv.innerHTML = `
                <strong>PWA Installation Status:</strong><br>
                • Standalone Mode: ${isStandalone ? '✅' : '❌'}<br>
                • iOS Web App: ${isInWebAppiOS ? '✅' : '❌'}<br>
                • PWA Installed: ${isPWAInstalled ? '✅' : '❌'}<br>
                • Service Worker Support: ${swRegistered ? '✅' : '❌'}<br>
                • User Agent: ${navigator.userAgent.substring(0, 100)}...
            `;
            
            statusDiv.className = `status ${isPWAInstalled ? 'success' : 'error'}`;
        }

        // Force PWA Install
        function forcePWAInstall() {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.ready.then((registration) => {
                    registration.active?.postMessage({
                        type: 'PWA_INSTALLED'
                    });
                    console.log('Sent PWA_INSTALLED message to service worker');
                });
            }
        }

        // Cache Operations
        function diagnoseCacheContents() {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.ready.then((registration) => {
                    registration.active?.postMessage({
                        type: 'DIAGNOSE_CACHE'
                    });
                    console.log('Sent DIAGNOSE_CACHE message to service worker');
                });
            }
        }

        function forceCacheLanguage(lang) {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.ready.then((registration) => {
                    registration.active?.postMessage({
                        type: 'CACHE_LANGUAGE',
                        lang: lang
                    });
                    console.log(`Sent CACHE_LANGUAGE message for ${lang} to service worker`);
                });
            }
        }

        function forceCacheAll() {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.ready.then((registration) => {
                    registration.active?.postMessage({
                        type: 'FORCE_CACHE_ALL'
                    });
                    console.log('Sent FORCE_CACHE_ALL message to service worker');
                });
            }
        }

        async function clearAllCaches() {
            try {
                const cacheNames = await caches.keys();
                for (const cacheName of cacheNames) {
                    await caches.delete(cacheName);
                    console.log(`Deleted cache: ${cacheName}`);
                }
                
                // Clear localStorage
                localStorage.clear();
                console.log('Cleared localStorage');
                
                document.getElementById('cache-status').innerHTML = '✅ All caches cleared successfully';
                document.getElementById('cache-status').className = 'status success';
                
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } catch (error) {
                console.error('Error clearing caches:', error);
                document.getElementById('cache-status').innerHTML = `❌ Error clearing caches: ${error.message}`;
                document.getElementById('cache-status').className = 'status error';
            }
        }

        function testServiceWorker() {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.ready.then((registration) => {
                    registration.active?.postMessage({
                        type: 'TEST'
                    });
                    console.log('Sent TEST message to service worker');
                });
            }
        }

        function clearLog() {
            logContainer.textContent = '';
        }

        // Browser Information
        function updateBrowserInfo() {
            const infoDiv = document.getElementById('browser-info');
            
            infoDiv.innerHTML = `
                <div class="info-item">
                    <div class="info-label">Online Status</div>
                    <div>${navigator.onLine ? '🟢 Online' : '🔴 Offline'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Connection</div>
                    <div>${navigator.connection ? navigator.connection.effectiveType : 'Unknown'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Language</div>
                    <div>${navigator.language}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Platform</div>
                    <div>${navigator.platform}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Cookies Enabled</div>
                    <div>${navigator.cookieEnabled ? '✅' : '❌'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Local Storage</div>
                    <div>${typeof(Storage) !== "undefined" ? '✅' : '❌'}</div>
                </div>
            `;
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            checkPWAStatus();
            updateBrowserInfo();
            
            // Update online status
            window.addEventListener('online', updateBrowserInfo);
            window.addEventListener('offline', updateBrowserInfo);
            
            console.log('Cache Debug Tool initialized');
        });
    </script>
</body>
</html>
