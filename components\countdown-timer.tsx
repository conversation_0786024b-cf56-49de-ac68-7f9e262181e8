"use client"

import { useState, useEffect, useRef } from "react"
import { motion } from "framer-motion"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { getTranslations } from "@/lib/i18n/translations"
import { Play, Pause, RefreshCw, Save, Clock, Plus, Trash2 } from "lucide-react"
import { useSound } from "@/components/sound-provider"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { useToast } from "@/components/ui/use-toast" // Import useToast for notifications fallback/info
import { useRTL } from "@/hooks/useRTL"
import { useLanguage } from "@/components/language-provider"
import { ToolControls } from "@/components/tool-controls"
import { ToolFullscreenWrapper } from "@/components/tool-fullscreen-wrapper"
import { trackToolUsage, trackTimerEvent } from "@/components/google-analytics"

interface CountdownTimerProps {
  lang: string
}

interface TimePreset {
  id: string
  name: string
  hours: number
  minutes: number
  seconds: number
}

// Clé de stockage local pour les presets
const PRESETS_STORAGE_KEY = "timetools_countdown_presets"

// Fonction utilitaire pour convertir le temps en millisecondes
const timeToMs = (hours: number, minutes: number, seconds: number) => {
  return (hours * 3600 + minutes * 60 + seconds) * 1000
}

// Fonction utilitaire pour convertir les millisecondes en temps
const msToTime = (ms: number) => {
  // --- FIX: Utiliser Math.ceil pour un affichage plus intuitif du compte à rebours ---
  const totalSeconds = Math.ceil(Math.max(0, ms) / 1000) // Arrondir au supérieur pour afficher la seconde jusqu'à ce qu'elle soit écoulée
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const seconds = totalSeconds % 60

  return { hours, minutes, seconds }
}

// Fonctions utilitaires pour le stockage local
function saveToLocalStorage<T>(key: string, data: T): void {
  try {
    if (typeof window !== "undefined") {
      const serializedData = JSON.stringify(data)
      localStorage.setItem(key, serializedData)
    }
  } catch (error) {
    console.error("Error saving to localStorage:", error)
  }
}

function getFromLocalStorage<T>(key: string, defaultValue: T): T {
  try {
    if (typeof window !== "undefined") {
      const serializedData = localStorage.getItem(key)
      if (serializedData === null) {
        return defaultValue
      }
      return JSON.parse(serializedData) as T
    }
    return defaultValue
  } catch (error) {
    console.error("Error getting from localStorage:", error)
    return defaultValue
  }
}

export function CountdownTimer({ lang }: CountdownTimerProps) {
  const t = getTranslations(lang)
  const { language } = useLanguage()
  const isRTL = useRTL(language || lang)
  const [isRunning, setIsRunning] = useState(false)
  const [time, setTime] = useState(300000) // 5 minutes en ms par défaut
  const [hours, setHours] = useState(0)
  const [minutes, setMinutes] = useState(5)
  const [seconds, setSeconds] = useState(0)
  const [activeTab, setActiveTab] = useState<string>("timer")
  const [isToolFullscreen, setIsToolFullscreen] = useState(false)
  const [presets, setPresets] = useState<TimePreset[]>([
    { id: "1", name: t.quickBreak || "Pause rapide", hours: 0, minutes: 5, seconds: 0 },
    { id: "2", name: t.meeting || "Réunion", hours: 0, minutes: 30, seconds: 0 },
    { id: "3", name: t.lunch || "Déjeuner", hours: 1, minutes: 0, seconds: 0 },
  ])
  const [newPresetName, setNewPresetName] = useState("")
  const [autoRestart, setAutoRestart] = useState(false)
  const [restartCount, setRestartCount] = useState(0)
  const [maxRestarts, setMaxRestarts] = useState(0)
  // Removed shouldRestart state as it's no longer needed
  const [initialTime, setInitialTime] = useState(300000) // Initial duration in ms
  const [remainingTimeOnPause, setRemainingTimeOnPause] = useState(initialTime) // Time left when paused
  const [notificationPermission, setNotificationPermission] = useState<NotificationPermission>("default")
  const originalTitle = useRef<string>("")

  // const intervalRef = useRef<NodeJS.Timeout | null>(null) // No longer needed
  // const endTimeRef = useRef<number>(0) // Worker handles end time internally
  const workerRef = useRef<Worker | null>(null); // Ref to hold the worker instance
  const { playSound, isSoundEnabled } = useSound()
  const { toast } = useToast() // Initialize toast

  // Sauvegarder le titre original de la page au chargement du composant
  useEffect(() => {
    originalTitle.current = document.title
    return () => {
      document.title = originalTitle.current
    }
  }, [])

  // Mettre à jour le titre de l'onglet du navigateur avec le temps restant
  useEffect(() => {
    if (isRunning || (time > 0 && time < initialTime)) {
      document.title = `${formatTime(time)} - ${t.countdown || "Compte à rebours"}`
      console.log(`Updating tab title: ${formatTime(time)} - ${t.countdown || "Compte à rebours"}`)
    } else {
      document.title = originalTitle.current
      console.log(`Resetting tab title to: ${originalTitle.current}`)
    }
  }, [time, isRunning, initialTime, t])

  // Charger les presets depuis localStorage
  useEffect(() => {
    const savedPresets = getFromLocalStorage<TimePreset[]>(PRESETS_STORAGE_KEY, [])
    if (savedPresets.length > 0) {
      setPresets(savedPresets)
    }
  }, []) // Only run on mount

  // Initialize and manage the Web Worker
  useEffect(() => {
    // Create worker instance
    workerRef.current = new Worker('/workers/countdown-worker.js'); // Path relative to public folder

    // Message handler
    workerRef.current.onmessage = (event) => {
      const { type, timeLeft: workerTimeLeft } = event.data;

      if (type === 'tick') {
        // Update component state with time from worker
        // Use functional update to be safe
        setTime(prevTime => {
           const roundedNewTime = Math.round(workerTimeLeft);
           const roundedPrevTime = Math.round(prevTime);
           // Only update if the rounded value actually changed
           return roundedNewTime !== roundedPrevTime ? roundedNewTime : prevTime;
        });

        // Check for completion inside the message handler
        if (workerTimeLeft <= 0) {
          handleTimerCompletion();
        }
      }
    };

    // Error handler
    workerRef.current.onerror = (error) => {
      console.error("Worker error:", error);
      // Maybe show a toast or fallback?
      toast({
        title: "Timer Error",
        description: "An issue occurred with the background timer.",
        variant: "destructive",
      })
      setIsRunning(false); // Stop timer on worker error
    };


    // Cleanup: Terminate worker when component unmounts
    return () => {
      if (workerRef.current) {
        workerRef.current.postMessage({ command: 'stop' }); // Ensure interval is cleared
        workerRef.current.terminate();
        workerRef.current = null;
        console.log("Countdown worker terminated.");
      }
    };
  }, [toast]); // Add toast dependency for error handling

  // Request notification permission on mount
  useEffect(() => {
    if (typeof window !== "undefined" && "Notification" in window) {
      if (Notification.permission !== "granted" && Notification.permission !== "denied") {
        Notification.requestPermission().then((permission) => {
          setNotificationPermission(permission)
          if (permission === "denied") {
            toast({
              title: t.notificationsBlockedTitle || "Notifications Bloquées",
              description: t.notificationsBlockedDesc || "Les notifications sont bloquées. Le son pourrait ne pas jouer en arrière-plan.",
              variant: "destructive",
            })
          } else if (permission === "granted") {
             setNotificationPermission(permission) // Update state
          }
        })
      } else {
        setNotificationPermission(Notification.permission)
      }
    }
  }, [t, toast]) // Request permission logic depends on t and toast

  // Sauvegarder les presets dans localStorage quand ils changent
  useEffect(() => {
    saveToLocalStorage(PRESETS_STORAGE_KEY, presets)
  }, [presets])

  // Initialiser les heures, minutes et secondes au chargement et quand initialTime change
  useEffect(() => {
    const { hours: h, minutes: m, seconds: s } = msToTime(initialTime)
    setHours(h)
    setMinutes(m)
    setSeconds(s)
    if (!isRunning) {
      setTime(initialTime) // Ensure time reflects initialTime when not running
      setRemainingTimeOnPause(initialTime) // Reset pause time as well
    }
  }, [initialTime]) // Dépendance ajoutée: initialTime

  // Mettre à jour initialTime lorsque les entrées changent (uniquement si le timer n'est pas en cours)
  useEffect(() => {
    if (!isRunning) {
      const newTime = timeToMs(hours, minutes, seconds)
      setInitialTime(newTime)
      // setTime(newTime) // This is handled by the effect above now
      // setRemainingTimeOnPause(newTime) // This is handled by the effect above now
    }
  }, [hours, minutes, seconds, isRunning])

  // Function to handle timer completion logic
  const handleTimerCompletion = () => {
    console.log("Timer completed - handling completion logic.");

    // Track timer completion
    trackTimerEvent('countdown', initialTime, 'completed');
    trackToolUsage('countdown', 'completed');

    // Ensure worker is stopped if it hasn't already stopped itself
    workerRef.current?.postMessage({ command: 'stop' });

    setIsRunning(false);
    setTime(0); // Explicitly set display time to 0
    setRemainingTimeOnPause(initialTime); // Reset remaining time for potential next start

    // Play sound
    if (isSoundEnabled) {
      try {
        playSound("bell");
        console.log("Timer finished, playing sound.");
      } catch (error) {
        console.warn("Could not play alarm sound:", error);
      }
    }

    // Show notification if tab is hidden and permission granted
    if (document.hidden && notificationPermission === "granted") {
      try {
        new Notification(t.countdownFinished || "Compte à rebours terminé!", {
          body: t.timerEndedBody || `Le minuteur de ${formatTime(initialTime)} est terminé.`,
          icon: "/placeholder-logo.svg",
          silent: true, // Sound handled above
        });
        console.log("Showing background notification.");
      } catch (err) {
        console.error("Error showing notification:", err);
      }
    }

    // Restaurer le titre original
    document.title = originalTitle.current;

    // Log pour le débogage
    console.log(`Timer completed. autoRestart=${autoRestart}, maxRestarts=${maxRestarts}, restartCount=${restartCount}`);

    // Note: L'Auto Restart est maintenant géré par un effet dédié qui surveille time et isRunning
  };


  // REMOVED: Old interval logic useEffect is replaced by worker communication

  // Effet dédié pour gérer l'Auto Restart quand le timer atteint zéro
  useEffect(() => {
    // Vérifier si le timer vient de se terminer (time === 0 et isRunning est false)
    // et si l'Auto Restart est activé
    if (time === 0 && !isRunning && autoRestart && (maxRestarts === 0 || restartCount < maxRestarts)) {
      console.log("Auto-restart effect triggered - timer just completed");

      // Attendre un court délai avant de redémarrer
      const restartTimeout = setTimeout(() => {
        if (workerRef.current) {
          console.log(`Auto-restarting timer with initialTime=${initialTime}ms`);

          // Réinitialiser les états du timer
          setRemainingTimeOnPause(initialTime);
          setTime(initialTime);

          // Démarrer le worker avec un nouveau temps de fin
          const newEndTime = Date.now() + initialTime;
          workerRef.current.postMessage({ command: 'start', value: newEndTime });

          // Mettre à jour l'état de fonctionnement
          setIsRunning(true);

          // Incrémenter le compteur de redémarrages
          setRestartCount(prev => {
            const newCount = prev + 1;
            console.log(`Incrementing restart count from ${prev} to ${newCount}`);
            return newCount;
          });

          console.log("Auto-restart complete - timer should now be running");
        } else {
          console.error("Cannot auto-restart: worker not available");
        }
      }, 1000); // Délai légèrement plus long pour s'assurer que tout est bien réinitialisé

      return () => clearTimeout(restartTimeout);
    }
  }, [time, isRunning, autoRestart, maxRestarts, restartCount, initialTime]);

  const toggleTimer = () => {
    if (!workerRef.current) {
       console.error("Worker not initialized!");
       toast({ title: "Error", description: "Timer worker failed to load.", variant: "destructive" });
       return;
    }

    if (isRunning) {
      // Pause the timer
      console.log("Pausing timer via worker.");
      workerRef.current.postMessage({ command: 'stop' });
      setIsRunning(false);
      // Store remaining time when pausing (based on current display time)
      // Note: worker stops, so this 'time' state is the last value received
      setRemainingTimeOnPause(time);

      // Track pause event
      trackTimerEvent('countdown', time, 'pause');
      trackToolUsage('countdown', 'pause');

    } else {
      // Start or resume the timer
      console.log(`Starting/Resuming timer via worker. autoRestart=${autoRestart}, maxRestarts=${maxRestarts}`);
      // Use remainingTimeOnPause if resuming, otherwise use initialTime
      const timeToStartMs = (time > 0 && remainingTimeOnPause > 0 && remainingTimeOnPause < initialTime)
                           ? remainingTimeOnPause
                           : initialTime;

      // If timer was finished (time <= 0), reset display and remaining time before starting
      if (time <= 0) {
         setTime(initialTime);
         setRemainingTimeOnPause(initialTime);
         // Use initialTime for calculation below
         const targetEndTime = Date.now() + initialTime;
         workerRef.current.postMessage({ command: 'start', value: targetEndTime });
      } else {
         // Calculate target end time based on timeToStartMs
         const targetEndTime = Date.now() + timeToStartMs;
         workerRef.current.postMessage({ command: 'start', value: targetEndTime });
      }

      setIsRunning(true);
      // Reset restart count only when manually starting/resuming
      setRestartCount(0);

      // Track start event
      trackTimerEvent('countdown', timeToStartMs, 'start');
      trackToolUsage('countdown', 'start');
    }
  }

  const resetTimer = () => {
    console.log("Resetting timer via worker.");

    // Track reset event before resetting
    trackTimerEvent('countdown', time, 'reset');
    trackToolUsage('countdown', 'reset');

    workerRef.current?.postMessage({ command: 'stop' }); // Tell worker to stop
    setIsRunning(false);

    // Réinitialiser les compteurs de redémarrage
    setRestartCount(0);
    // Removed setShouldRestart as it's no longer needed

    // Réinitialiser les temps
    setTime(initialTime); // Reset display time
    setRemainingTimeOnPause(initialTime); // Reset remaining time state

    // Restaurer le titre original
    document.title = originalTitle.current;

    console.log("Timer fully reset, auto-restart counters cleared");
  }

  const applyPreset = (preset: TimePreset) => {
    if (isRunning) {
      // Stop timer if running before applying preset
      workerRef.current?.postMessage({ command: 'stop' });
      setIsRunning(false);
    }
    // No need to clear intervalRef anymore

    // Track preset usage
    trackToolUsage('countdown', 'preset_applied');
    trackTimerEvent('countdown', timeToMs(preset.hours, preset.minutes, preset.seconds), 'preset_applied');

    // Setting hours/minutes/seconds will trigger the useEffect to update initialTime
    setHours(preset.hours)
    setMinutes(preset.minutes)
    setSeconds(preset.seconds)
  }

  const savePreset = () => {
    if (newPresetName.trim() === "") return

    const newPreset: TimePreset = {
      id: Date.now().toString(),
      name: newPresetName,
      hours,
      minutes,
      seconds,
    }

    const updatedPresets = [...presets, newPreset]
    setPresets(updatedPresets)
    setNewPresetName("")

    // Sauvegarder dans localStorage
    saveToLocalStorage(PRESETS_STORAGE_KEY, updatedPresets)
  }

  const deletePreset = (id: string) => {
    const updatedPresets = presets.filter((preset) => preset.id !== id)
    setPresets(updatedPresets)

    // Sauvegarder dans localStorage
    saveToLocalStorage(PRESETS_STORAGE_KEY, updatedPresets)
  }

  const handleHoursChange = (value: string) => {
    const h = Number.parseInt(value) || 0
    setHours(Math.max(0, Math.min(99, h)))
  }

  const handleMinutesChange = (value: string) => {
    const m = Number.parseInt(value) || 0
    setMinutes(Math.max(0, Math.min(59, m)))
  }

  const handleSecondsChange = (value: string) => {
    const s = Number.parseInt(value) || 0
    setSeconds(Math.max(0, Math.min(59, s)))
  }

  const formatTime = (ms: number) => {
    const { hours, minutes, seconds } = msToTime(ms)
    return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`
  }

  const progress = initialTime > 0 ? (time / initialTime) * 100 : 0

  const countdownContent = (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid grid-cols-2 w-full mb-8">
        <TabsTrigger value="timer" className="flex items-center gap-2">
          <Clock className="h-4 w-4" />
          {t.timer}
        </TabsTrigger>
        <TabsTrigger value="presets" className="flex items-center gap-2">
          <Save className="h-4 w-4" />
          {t.presets || "Préréglages"}
        </TabsTrigger>
      </TabsList>

      <TabsContent value="timer" className="space-y-8">
        <div className="flex flex-col items-center justify-center py-12">
          <div className="w-full h-3 bg-muted rounded-full mb-12 overflow-hidden">
            <motion.div
              className="h-full bg-primary"
              style={{ width: `${progress}%` }}
              initial={{ width: "100%" }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>

          <motion.div
            className="text-7xl md:text-8xl font-bold font-mono mb-12"
            key={time}
            initial={{ scale: 0.95 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.1 }}
          >
            {formatTime(time)}
          </motion.div>

          {!isRunning && (
            <div className="w-full max-w-md mb-8">
              <div className="grid grid-cols-3 gap-4 mb-6">
                {isRTL ? (
                  <>
                    <div>
                      <Label htmlFor="seconds" className="mb-2 block text-center">
                        {t.seconds || "Secondes"}
                      </Label>
                      <Input
                        id="seconds"
                        type="number"
                        min="0"
                        max="59"
                        value={seconds}
                        onChange={(e) => handleSecondsChange(e.target.value)}
                        className="text-center text-lg"
                        dir="ltr"
                      />
                    </div>
                    <div>
                      <Label htmlFor="minutes" className="mb-2 block text-center">
                        {t.minutes}
                      </Label>
                      <Input
                        id="minutes"
                        type="number"
                        min="0"
                        max="59"
                        value={minutes}
                        onChange={(e) => handleMinutesChange(e.target.value)}
                        className="text-center text-lg"
                        dir="ltr"
                      />
                    </div>
                    <div>
                      <Label htmlFor="hours" className="mb-2 block text-center">
                        {t.hours || "Heures"}
                      </Label>
                      <Input
                        id="hours"
                        type="number"
                        min="0"
                        max="99"
                        value={hours}
                        onChange={(e) => handleHoursChange(e.target.value)}
                        className="text-center text-lg"
                        dir="ltr"
                      />
                    </div>
                  </>
                ) : (
                  <>
                    <div>
                      <Label htmlFor="hours" className="mb-2 block text-center">
                        {t.hours || "Heures"}
                      </Label>
                      <Input
                        id="hours"
                        type="number"
                        min="0"
                        max="99"
                        value={hours}
                        onChange={(e) => handleHoursChange(e.target.value)}
                        className="text-center text-lg"
                        dir="ltr"
                      />
                    </div>
                    <div>
                      <Label htmlFor="minutes" className="mb-2 block text-center">
                        {t.minutes}
                      </Label>
                      <Input
                        id="minutes"
                        type="number"
                        min="0"
                        max="59"
                        value={minutes}
                        onChange={(e) => handleMinutesChange(e.target.value)}
                        className="text-center text-lg"
                        dir="ltr"
                      />
                    </div>
                    <div>
                      <Label htmlFor="seconds" className="mb-2 block text-center">
                        {t.seconds || "Secondes"}
                      </Label>
                      <Input
                        id="seconds"
                        type="number"
                        min="0"
                        max="59"
                        value={seconds}
                        onChange={(e) => handleSecondsChange(e.target.value)}
                        className="text-center text-lg"
                        dir="ltr"
                      />
                    </div>
                  </>
                )}
              </div>

              <div className="grid grid-cols-4 gap-2 mb-6" dir={isRTL ? "rtl" : "ltr"}>
                {[1, 5, 10, 15, 30, 45, 60, 90].map((value) => (
                  <Button
                    key={value}
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if (value < 60) {
                        setHours(0)
                        setMinutes(value)
                        setSeconds(0)
                      } else {
                        setHours(Math.floor(value / 60))
                        setMinutes(value % 60)
                        setSeconds(0)
                      }
                    }}
                  >
                    {value < 60
                      ? `${value}m`
                      : `${Math.floor(value / 60)}h${value % 60 > 0 ? ` ${value % 60}m` : ""}`}
                  </Button>
                ))}
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between" dir={isRTL ? "rtl" : "ltr"}>
                  <div className="flex items-center space-x-2" style={{ columnGap: "0.5rem" }}>
                    <Switch id="auto-restart" checked={autoRestart} onCheckedChange={setAutoRestart} />
                    <Label htmlFor="auto-restart">{t.autoRestart || "Redémarrage automatique"}</Label>
                  </div>
                  {autoRestart && (
                    <div className="flex items-center" style={{ columnGap: "0.5rem" }}>
                      <Label htmlFor="max-restarts" className="whitespace-nowrap">
                        {t.maxRestarts || "Nombre max"}:
                      </Label>
                      <Input
                        id="max-restarts"
                        type="number"
                        min="0"
                        value={maxRestarts}
                        onChange={(e) => setMaxRestarts(Number.parseInt(e.target.value) || 0)}
                        className="w-20"
                        dir="ltr" // Keep numbers left-to-right even in RTL mode
                      />
                      <span className="text-sm text-muted-foreground">(0 = {t.infinite || "infini"})</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          <div className="flex gap-6" dir={isRTL ? "rtl" : "ltr"}>
            <Button
              onClick={toggleTimer}
              size="lg"
              className="w-32 h-32 rounded-full"
              variant={isRunning ? "destructive" : "default"}
            >
              {isRunning ? <Pause className="h-10 w-10" /> : <Play className="h-10 w-10" />}
            </Button>

            <Button
              onClick={resetTimer}
              size="lg"
              variant="outline"
              className="w-20 h-20 rounded-full"
              disabled={time === initialTime && !isRunning}
            >
              <RefreshCw className="h-8 w-8" />
            </Button>
          </div>

          {autoRestart && isRunning && restartCount > 0 && (
            <div className="mt-4">
              <Badge variant="outline" className="text-sm">
                {t.cycle || "Cycle"}: {restartCount}/{maxRestarts > 0 ? maxRestarts : "∞"}
              </Badge>
            </div>
          )}
        </div>
      </TabsContent>

      <TabsContent value="presets" className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {presets.map((preset) => (
            <div key={preset.id} className="flex justify-between items-center p-3 border rounded-md" dir={isRTL ? "rtl" : "ltr"}>
              <div>
                <p className="font-medium">{preset.name}</p>
                <p className="text-sm text-muted-foreground">
                  {formatTime(timeToMs(preset.hours, preset.minutes, preset.seconds))}
                </p>
              </div>
              <div className="flex gap-2" dir={isRTL ? "rtl" : "ltr"}>
                <Button variant="outline" size="sm" onClick={() => applyPreset(preset)}>
                  {t.apply || "Appliquer"}
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => deletePreset(preset.id)}
                  className="h-8 w-8 text-destructive"
                >
                  <Trash2 className="h-4 w-4" />
                  <span className="sr-only">{t.delete}</span>
                </Button>
              </div>
            </div>
          ))}
        </div>

        <div className="space-y-4">
          <h3 className="font-medium">{t.saveCurrentAsPreset || "Enregistrer le réglage actuel"}</h3>
          <div className="flex gap-2" dir={isRTL ? "rtl" : "ltr"}>
            <Input
              placeholder={t.presetName || "Nom du préréglage"}
              value={newPresetName}
              onChange={(e) => setNewPresetName(e.target.value)}
              dir={isRTL ? 'rtl' : 'ltr'}
            />
            <Button onClick={savePreset} disabled={newPresetName.trim() === ""}>
              <Plus className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              {t.save}
            </Button>
          </div>
        </div>
      </TabsContent>
    </Tabs>
  )

  return (
    <>
      <Card className="w-full max-w-4xl mx-auto overflow-hidden">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              {t.countdown}
            </CardTitle>
            <ToolControls
              onFullscreenToggle={setIsToolFullscreen}
              variant="compact"
            />
          </div>
        </CardHeader>
        <CardContent>
          {countdownContent}
        </CardContent>
      </Card>

      {/* Wrapper plein écran */}
      <ToolFullscreenWrapper
        isFullscreen={isToolFullscreen}
        onClose={() => setIsToolFullscreen(false)}
        toolName={t.countdown}
      >
        <Card className="w-full border-0 shadow-none bg-transparent">
          <CardContent>
            {countdownContent}
          </CardContent>
        </Card>
      </ToolFullscreenWrapper>
    </>
  )
}
