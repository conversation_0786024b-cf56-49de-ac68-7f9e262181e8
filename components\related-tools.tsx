"use client"

import { Card, CardContent } from "@/components/ui/card"
import { getTranslations } from "@/lib/i18n/translations"
import { getTranslatedRoute } from "@/lib/i18n/route-translations"
import Link from "next/link"
import { Clock, Timer, Globe, ListTodo, Activity, Users, FileText } from "lucide-react"

interface RelatedToolsProps {
  lang?: string
  currentTool?: string
  maxItems?: number
}

export function RelatedTools({ lang = "en", currentTool, maxItems = 6 }: RelatedToolsProps) {
  const t = getTranslations(lang)

  // Helper function to get localized links
  const getLocalizedLink = (route: string) => {
    const translatedRoute = getTranslatedRoute(lang, route)
    return `/${lang}/${translatedRoute}`
  }

  // Define all available tools with their metadata
  const allTools = [
    {
      key: "timer",
      name: t.timerStopwatch,
      description: t.timerDescription,
      icon: <Timer className="h-5 w-5" />,
      category: "time"
    },
    {
      key: "countdown",
      name: t.countdown,
      description: t.countdownDescription,
      icon: <Clock className="h-5 w-5" />,
      category: "time"
    },
    {
      key: "world-clock",
      name: t.worldClock,
      description: t.worldClockDescription,
      icon: <Globe className="h-5 w-5" />,
      category: "time"
    },
    {
      key: "intervals",
      name: t.intervals,
      description: t.intervalsDescription,
      icon: <Activity className="h-5 w-5" />,
      category: "time"
    },
    {
      key: "pomodoro",
      name: t.pomodoro,
      description: t.pomodoroDescription,
      icon: <Timer className="h-5 w-5" />,
      category: "productivity"
    },
    {
      key: "todo",
      name: t.todoList,
      description: t.todoDescription,
      icon: <ListTodo className="h-5 w-5" />,
      category: "productivity"
    },
    {
      key: "time-tracking",
      name: t.timeTracking,
      description: t.timeTrackingDescription,
      icon: <Clock className="h-5 w-5" />,
      category: "productivity"
    },
    {
      key: "workout-intervals",
      name: t.workoutIntervals,
      description: t.workoutIntervalsDescription,
      icon: <Activity className="h-5 w-5" />,
      category: "fitness"
    },
    {
      key: "exercise-templates",
      name: t.exerciseTemplates,
      description: t.exerciseTemplatesDescription,
      icon: <ListTodo className="h-5 w-5" />,
      category: "fitness"
    }
  ]

  // Filter out the current tool and get related tools
  const getRelatedTools = () => {
    const filtered = allTools.filter(tool => tool.key !== currentTool)

    if (!currentTool) {
      // If no current tool specified, return a mix of popular tools
      return filtered.slice(0, maxItems)
    }

    const currentToolData = allTools.find(tool => tool.key === currentTool)
    const currentCategory = currentToolData?.category

    // Prioritize tools from the same category
    const sameCategory = filtered.filter(tool => tool.category === currentCategory)
    const otherCategory = filtered.filter(tool => tool.category !== currentCategory)

    // Combine same category first, then others
    const related = [...sameCategory, ...otherCategory].slice(0, maxItems)
    return related
  }

  const relatedTools = getRelatedTools()

  if (relatedTools.length === 0) {
    return null
  }

  return (
    <div className="mt-8">
      <h2 className="text-2xl font-bold mb-6 flex items-center gap-2">
        <Activity className="h-6 w-6" />
        {t.relatedTools || "Outils connexes"}
      </h2>
      <Card>
        <CardContent className="pt-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {relatedTools.map((tool) => (
            <Link
              key={tool.key}
              href={getLocalizedLink(tool.key)}
              className="group block p-4 border rounded-lg hover:border-primary hover:shadow-md transition-all duration-200"
            >
              <div className="flex items-start gap-3">
                <div className="p-2 rounded-md bg-primary/10 text-primary group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                  {tool.icon}
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-sm group-hover:text-primary transition-colors">
                    {tool.name}
                  </h3>
                  <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                    {tool.description}
                  </p>
                </div>
              </div>
            </Link>
          ))}
        </div>
        </CardContent>
      </Card>
    </div>
  )
}
