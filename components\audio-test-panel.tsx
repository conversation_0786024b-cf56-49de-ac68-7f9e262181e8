"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useTimerAudio, useVisibilityAudio } from "@/hooks/use-robust-audio"
import { Volume2, VolumeX, Play, Pause, RotateCcw, Eye, EyeOff, Smartphone, Monitor } from "lucide-react"

export function AudioTestPanel() {
  const [testInProgress, setTestInProgress] = useState(false)
  const [testResults, setTestResults] = useState<string[]>([])
  
  const {
    playTimerSound,
    soundEnabled,
    toggleSound,
    isAudioReady,
    isPlaying,
    hasWakeLock,
    audioState,
    lastError,
    forceUnlock
  } = useTimerAudio()

  const { isVisible, wasHidden, isHidden } = useVisibilityAudio()

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`])
  }

  const runAudioTest = async (soundType: "bell" | "alarm" | "notification") => {
    setTestInProgress(true)
    addTestResult(`Testing ${soundType} sound...`)
    
    try {
      const success = await playTimerSound(soundType)
      if (success) {
        addTestResult(`✅ ${soundType} sound played successfully`)
      } else {
        addTestResult(`❌ ${soundType} sound failed to play`)
      }
    } catch (error) {
      addTestResult(`❌ ${soundType} sound error: ${error}`)
    }
    
    setTestInProgress(false)
  }

  const testWakeLock = async () => {
    addTestResult("Testing Wake Lock...")
    try {
      if ("wakeLock" in navigator) {
        const wakeLock = await navigator.wakeLock.request("screen")
        addTestResult("✅ Wake Lock acquired successfully")
        setTimeout(() => {
          wakeLock.release()
          addTestResult("Wake Lock released")
        }, 3000)
      } else {
        addTestResult("❌ Wake Lock not supported")
      }
    } catch (error) {
      addTestResult(`❌ Wake Lock error: ${error}`)
    }
  }

  const clearResults = () => {
    setTestResults([])
  }

  const getAudioStateColor = (state: string) => {
    switch (state) {
      case "running": return "bg-green-500"
      case "suspended": return "bg-yellow-500"
      case "closed": return "bg-red-500"
      default: return "bg-gray-500"
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Volume2 className="h-5 w-5" />
          Test Audio Robuste
        </CardTitle>
        <CardDescription>
          Testez le système de gestion audio robuste pour mobile
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* État du système audio */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <Badge variant={isAudioReady ? "default" : "destructive"}>
              {isAudioReady ? "Prêt" : "Non prêt"}
            </Badge>
            <p className="text-xs text-muted-foreground mt-1">Audio Ready</p>
          </div>
          
          <div className="text-center">
            <Badge variant={isPlaying ? "default" : "secondary"}>
              {isPlaying ? "En cours" : "Arrêté"}
            </Badge>
            <p className="text-xs text-muted-foreground mt-1">Playing</p>
          </div>
          
          <div className="text-center">
            <Badge variant={hasWakeLock ? "default" : "secondary"}>
              {hasWakeLock ? "Actif" : "Inactif"}
            </Badge>
            <p className="text-xs text-muted-foreground mt-1">Wake Lock</p>
          </div>
          
          <div className="text-center">
            <Badge className={getAudioStateColor(audioState)}>
              {audioState}
            </Badge>
            <p className="text-xs text-muted-foreground mt-1">Context State</p>
          </div>
        </div>

        {/* État de visibilité */}
        <div className="flex items-center justify-center gap-4 p-4 bg-muted rounded-lg">
          <div className="flex items-center gap-2">
            {isVisible ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
            <span className="text-sm">
              Page {isVisible ? "visible" : "cachée"}
            </span>
          </div>
          {wasHidden && (
            <Badge variant="outline">
              Était cachée
            </Badge>
          )}
        </div>

        {/* Contrôles audio */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="font-medium">Contrôles Audio</h3>
            <Button
              variant="outline"
              size="sm"
              onClick={toggleSound}
              className="flex items-center gap-2"
            >
              {soundEnabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
              {soundEnabled ? "Désactiver" : "Activer"}
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
            <Button
              onClick={() => runAudioTest("bell")}
              disabled={testInProgress || !soundEnabled}
              variant="outline"
            >
              Test Bell
            </Button>
            <Button
              onClick={() => runAudioTest("alarm")}
              disabled={testInProgress || !soundEnabled}
              variant="outline"
            >
              Test Alarm
            </Button>
            <Button
              onClick={() => runAudioTest("notification")}
              disabled={testInProgress || !soundEnabled}
              variant="outline"
            >
              Test Notification
            </Button>
          </div>

          <div className="flex gap-2">
            <Button
              onClick={forceUnlock}
              disabled={testInProgress}
              variant="outline"
              size="sm"
            >
              Force Unlock
            </Button>
            <Button
              onClick={testWakeLock}
              disabled={testInProgress}
              variant="outline"
              size="sm"
            >
              Test Wake Lock
            </Button>
          </div>
        </div>

        {/* Instructions de test */}
        <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
          <h4 className="font-medium text-blue-900 mb-2">Instructions de test mobile :</h4>
          <ol className="text-sm text-blue-800 space-y-1">
            <li>1. Activez le son et testez les différents types</li>
            <li>2. Verrouillez l'écran de votre mobile</li>
            <li>3. Attendez quelques secondes</li>
            <li>4. Déverrouillez et vérifiez les résultats</li>
            <li>5. Testez avec l'onglet en arrière-plan</li>
          </ol>
        </div>

        {/* Erreurs */}
        {lastError && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-800">
              <strong>Erreur :</strong> {lastError}
            </p>
          </div>
        )}

        {/* Résultats des tests */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h3 className="font-medium">Résultats des tests</h3>
            <Button
              onClick={clearResults}
              variant="ghost"
              size="sm"
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Effacer
            </Button>
          </div>
          
          <div className="max-h-40 overflow-y-auto p-3 bg-muted rounded-lg">
            {testResults.length === 0 ? (
              <p className="text-sm text-muted-foreground">Aucun test effectué</p>
            ) : (
              <div className="space-y-1">
                {testResults.map((result, index) => (
                  <p key={index} className="text-xs font-mono">
                    {result}
                  </p>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Informations sur l'appareil */}
        <div className="text-xs text-muted-foreground space-y-1">
          <p className="flex items-center gap-2">
            {/Mobi|Android/i.test(navigator.userAgent) ? (
              <Smartphone className="h-3 w-3" />
            ) : (
              <Monitor className="h-3 w-3" />
            )}
            User Agent: {navigator.userAgent.slice(0, 50)}...
          </p>
          <p>Wake Lock Support: {"wakeLock" in navigator ? "✅" : "❌"}</p>
          <p>Audio Context Support: {window.AudioContext || (window as any).webkitAudioContext ? "✅" : "❌"}</p>
        </div>
      </CardContent>
    </Card>
  )
}
