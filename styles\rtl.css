/* Styles RTL globaux */

/* Inverser les icônes dans les menus */
.rtl .rtl-menu-icon {
  transform: scaleX(-1);
}

/* Inverser les icônes directionnelles */
.rtl .rtl-inverted {
  transform: scaleX(-1);
}

/* Styles pour les cartes en mode RTL */
.rtl .rtl-card {
  direction: rtl;
  text-align: right;
}

.rtl .rtl-card .flex {
  flex-direction: row-reverse;
}

/* Styles pour les onglets en mode RTL */
.rtl .rtl-tablist {
  flex-direction: row-reverse;
}

.rtl .rtl-tab-grid {
  direction: rtl;
}

/* Styles pour les champs de texte en mode RTL */
.rtl .rtl-text-input,
.rtl input[type="text"],
.rtl input[type="email"],
.rtl input[type="password"],
.rtl input[type="search"],
.rtl input[type="tel"],
.rtl input[type="url"],
.rtl input[type="number"],
.rtl input:not([type]),
.rtl textarea,
.rtl .form-input,
.rtl .input,
.rtl [role="textbox"] {
  direction: rtl;
  text-align: right;
}

/* Styles pour les conteneurs de formulaires en mode RTL */
.rtl .rtl-form-group,
.rtl .input-group,
.rtl .form-control,
.rtl .form-field,
.rtl .form-item {
  direction: rtl;
  text-align: right;
}

/* Styles pour les labels en mode RTL */
.rtl .rtl-form-label,
.rtl label,
.rtl legend,
.rtl .form-label {
  direction: rtl;
  text-align: right;
}

/* Styles pour les éléments positionnés en mode RTL */
.rtl .left-0:not(.right-0) {
  left: auto;
  right: 0;
}

.rtl .right-0:not(.left-0) {
  right: auto;
  left: 0;
}

/* Styles pour les listes en mode RTL */
.rtl ul,
.rtl ol,
.rtl dl,
.rtl .list {
  direction: rtl;
  text-align: right;
}

/* Styles pour les tableaux en mode RTL */
.rtl table,
.rtl .table {
  direction: rtl;
  text-align: right;
}

.rtl th,
.rtl td {
  text-align: right;
}

/* Styles pour les boutons en mode RTL */
.rtl .btn-group,
.rtl .button-group {
  flex-direction: row-reverse;
}

/* Styles pour les menus déroulants en mode RTL */
.rtl .dropdown-menu,
.rtl .menu,
.rtl [role="menu"] {
  direction: rtl;
  text-align: right;
}

/* Styles pour les grilles en mode RTL */
.rtl .grid {
  direction: rtl;
}

/* Styles pour les flexbox en mode RTL */
.rtl .flex-row {
  flex-direction: row-reverse;
}

.rtl .justify-start {
  justify-content: flex-end;
}

.rtl .justify-end {
  justify-content: flex-start;
}

.rtl .items-start {
  align-items: flex-end;
}

.rtl .items-end {
  align-items: flex-start;
}

/* Styles pour les marges et paddings en mode RTL */
.rtl .ml-1, .rtl .ml-2, .rtl .ml-3, .rtl .ml-4, .rtl .ml-5 {
  margin-left: 0;
  margin-right: var(--spacing);
}

.rtl .mr-1, .rtl .mr-2, .rtl .mr-3, .rtl .mr-4, .rtl .mr-5 {
  margin-right: 0;
  margin-left: var(--spacing);
}

.rtl .pl-1, .rtl .pl-2, .rtl .pl-3, .rtl .pl-4, .rtl .pl-5 {
  padding-left: 0;
  padding-right: var(--spacing);
}

.rtl .pr-1, .rtl .pr-2, .rtl .pr-3, .rtl .pr-4, .rtl .pr-5 {
  padding-right: 0;
  padding-left: var(--spacing);
}
