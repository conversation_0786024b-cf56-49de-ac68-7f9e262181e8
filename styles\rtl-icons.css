/* Styles spécifiques pour les icônes en mode RTL */

/* Style de base pour toutes les icônes dans les menus */
.rtl nav svg,
.rtl .menu svg,
.rtl .dropdown svg,
.rtl .sidebar svg,
.rtl nav .lucide,
.rtl .menu .lucide,
.rtl .dropdown .lucide,
.rtl .sidebar .lucide,
.rtl li svg,
.rtl li .lucide,
.rtl .menu-item svg,
.rtl .menu-item .lucide,
.rtl .nav-item svg,
.rtl .nav-item .lucide {
  transform: scaleX(-1);
}

/* Inverser les icônes directionnelles */
.rtl .icon-arrow-left,
.rtl .icon-arrow-right,
.rtl .icon-chevron-left,
.rtl .icon-chevron-right,
.rtl [data-icon="arrow-left"],
.rtl [data-icon="arrow-right"],
.rtl [data-icon="chevron-left"],
.rtl [data-icon="chevron-right"],
.rtl svg[data-lucide="ArrowLeft"],
.rtl svg[data-lucide="ArrowRight"],
.rtl svg[data-lucide="ChevronLeft"],
.rtl svg[data-lucide="ChevronRight"],
.rtl .lucide-arrow-left,
.rtl .lucide-arrow-right,
.rtl .lucide-chevron-left,
.rtl .lucide-chevron-right,
.rtl svg.lucide-arrow-left,
.rtl svg.lucide-arrow-right,
.rtl svg.lucide-chevron-left,
.rtl svg.lucide-chevron-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de navigation */
.rtl .icon-navigation-left,
.rtl .icon-navigation-right,
.rtl [data-icon="navigation-left"],
.rtl [data-icon="navigation-right"],
.rtl svg[data-lucide="NavigationLeft"],
.rtl svg[data-lucide="NavigationRight"],
.rtl .lucide-navigation-left,
.rtl .lucide-navigation-right,
.rtl svg.lucide-navigation-left,
.rtl svg.lucide-navigation-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de flèche */
.rtl .icon-arrow-up-left,
.rtl .icon-arrow-up-right,
.rtl .icon-arrow-down-left,
.rtl .icon-arrow-down-right,
.rtl [data-icon="arrow-up-left"],
.rtl [data-icon="arrow-up-right"],
.rtl [data-icon="arrow-down-left"],
.rtl [data-icon="arrow-down-right"],
.rtl svg[data-lucide="ArrowUpLeft"],
.rtl svg[data-lucide="ArrowUpRight"],
.rtl svg[data-lucide="ArrowDownLeft"],
.rtl svg[data-lucide="ArrowDownRight"],
.rtl .lucide-arrow-up-left,
.rtl .lucide-arrow-up-right,
.rtl .lucide-arrow-down-left,
.rtl .lucide-arrow-down-right,
.rtl svg.lucide-arrow-up-left,
.rtl svg.lucide-arrow-up-right,
.rtl svg.lucide-arrow-down-left,
.rtl svg.lucide-arrow-down-right {
  transform: scale(-1, 1);
}

/* Inverser les icônes de chevron */
.rtl .icon-chevrons-left,
.rtl .icon-chevrons-right,
.rtl [data-icon="chevrons-left"],
.rtl [data-icon="chevrons-right"],
.rtl svg[data-lucide="ChevronsLeft"],
.rtl svg[data-lucide="ChevronsRight"],
.rtl .lucide-chevrons-left,
.rtl .lucide-chevrons-right,
.rtl svg.lucide-chevrons-left,
.rtl svg.lucide-chevrons-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de déplacement */
.rtl .icon-move-left,
.rtl .icon-move-right,
.rtl [data-icon="move-left"],
.rtl [data-icon="move-right"],
.rtl svg[data-lucide="MoveLeft"],
.rtl svg[data-lucide="MoveRight"],
.rtl .lucide-move-left,
.rtl .lucide-move-right,
.rtl svg.lucide-move-left,
.rtl svg.lucide-move-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de retour */
.rtl .icon-undo,
.rtl .icon-redo,
.rtl [data-icon="undo"],
.rtl [data-icon="redo"],
.rtl svg[data-lucide="Undo"],
.rtl svg[data-lucide="Redo"],
.rtl .lucide-undo,
.rtl .lucide-redo,
.rtl svg.lucide-undo,
.rtl svg.lucide-redo {
  transform: scaleX(-1);
}

/* Inverser les icônes de réponse */
.rtl .icon-reply,
.rtl .icon-reply-all,
.rtl [data-icon="reply"],
.rtl [data-icon="reply-all"],
.rtl svg[data-lucide="Reply"],
.rtl svg[data-lucide="ReplyAll"],
.rtl .lucide-reply,
.rtl .lucide-reply-all,
.rtl svg.lucide-reply,
.rtl svg.lucide-reply-all {
  transform: scaleX(-1);
}

/* Inverser les icônes de transfert */
.rtl .icon-forward,
.rtl [data-icon="forward"],
.rtl svg[data-lucide="Forward"],
.rtl .lucide-forward,
.rtl svg.lucide-forward {
  transform: scaleX(-1);
}

/* Inverser les icônes de partage */
.rtl .icon-share,
.rtl [data-icon="share"],
.rtl svg[data-lucide="Share"],
.rtl .lucide-share,
.rtl svg.lucide-share {
  transform: scaleX(-1);
}

/* Inverser les icônes de téléchargement */
.rtl .icon-download,
.rtl .icon-upload,
.rtl [data-icon="download"],
.rtl [data-icon="upload"],
.rtl svg[data-lucide="Download"],
.rtl svg[data-lucide="Upload"],
.rtl .lucide-download,
.rtl .lucide-upload,
.rtl svg.lucide-download,
.rtl svg.lucide-upload {
  transform: scaleX(-1);
}

/* Inverser les icônes de tri */
.rtl .icon-sort-asc,
.rtl .icon-sort-desc,
.rtl [data-icon="sort-asc"],
.rtl [data-icon="sort-desc"],
.rtl svg[data-lucide="SortAsc"],
.rtl svg[data-lucide="SortDesc"],
.rtl .lucide-sort-asc,
.rtl .lucide-sort-desc,
.rtl svg.lucide-sort-asc,
.rtl svg.lucide-sort-desc {
  transform: scaleX(-1);
}

/* Inverser les icônes de pagination */
.rtl .icon-first,
.rtl .icon-last,
.rtl .icon-previous,
.rtl .icon-next,
.rtl [data-icon="first"],
.rtl [data-icon="last"],
.rtl [data-icon="previous"],
.rtl [data-icon="next"],
.rtl svg[data-lucide="First"],
.rtl svg[data-lucide="Last"],
.rtl svg[data-lucide="Previous"],
.rtl svg[data-lucide="Next"],
.rtl .lucide-first,
.rtl .lucide-last,
.rtl .lucide-previous,
.rtl .lucide-next,
.rtl svg.lucide-first,
.rtl svg.lucide-last,
.rtl svg.lucide-previous,
.rtl svg.lucide-next {
  transform: scaleX(-1);
}

/* Inverser les icônes de lecture */
.rtl .icon-play,
.rtl .icon-pause,
.rtl .icon-stop,
.rtl .icon-rewind,
.rtl .icon-fast-forward,
.rtl [data-icon="play"],
.rtl [data-icon="pause"],
.rtl [data-icon="stop"],
.rtl [data-icon="rewind"],
.rtl [data-icon="fast-forward"],
.rtl svg[data-lucide="Play"],
.rtl svg[data-lucide="Pause"],
.rtl svg[data-lucide="Stop"],
.rtl svg[data-lucide="Rewind"],
.rtl svg[data-lucide="FastForward"],
.rtl .lucide-play,
.rtl .lucide-pause,
.rtl .lucide-stop,
.rtl .lucide-rewind,
.rtl .lucide-fast-forward,
.rtl svg.lucide-play,
.rtl svg.lucide-pause,
.rtl svg.lucide-stop,
.rtl svg.lucide-rewind,
.rtl svg.lucide-fast-forward {
  transform: scaleX(-1);
}

/* Inverser les icônes de navigation */
.rtl .icon-home,
.rtl .icon-back,
.rtl .icon-forward,
.rtl [data-icon="home"],
.rtl [data-icon="back"],
.rtl [data-icon="forward"],
.rtl svg[data-lucide="Home"],
.rtl svg[data-lucide="Back"],
.rtl svg[data-lucide="Forward"],
.rtl .lucide-home,
.rtl .lucide-back,
.rtl .lucide-forward,
.rtl svg.lucide-home,
.rtl svg.lucide-back,
.rtl svg.lucide-forward {
  transform: scaleX(-1);
}

/* Inverser les icônes de menu */
.rtl .icon-menu-left,
.rtl .icon-menu-right,
.rtl [data-icon="menu-left"],
.rtl [data-icon="menu-right"],
.rtl svg[data-lucide="MenuLeft"],
.rtl svg[data-lucide="MenuRight"],
.rtl .lucide-menu-left,
.rtl .lucide-menu-right,
.rtl svg.lucide-menu-left,
.rtl svg.lucide-menu-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de sidebar */
.rtl .icon-sidebar-left,
.rtl .icon-sidebar-right,
.rtl [data-icon="sidebar-left"],
.rtl [data-icon="sidebar-right"],
.rtl svg[data-lucide="SidebarLeft"],
.rtl svg[data-lucide="SidebarRight"],
.rtl .lucide-sidebar-left,
.rtl .lucide-sidebar-right,
.rtl svg.lucide-sidebar-left,
.rtl svg.lucide-sidebar-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de panel */
.rtl .icon-panel-left,
.rtl .icon-panel-right,
.rtl [data-icon="panel-left"],
.rtl [data-icon="panel-right"],
.rtl svg[data-lucide="PanelLeft"],
.rtl svg[data-lucide="PanelRight"],
.rtl .lucide-panel-left,
.rtl .lucide-panel-right,
.rtl svg.lucide-panel-left,
.rtl svg.lucide-panel-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de layout */
.rtl .icon-layout-left,
.rtl .icon-layout-right,
.rtl [data-icon="layout-left"],
.rtl [data-icon="layout-right"],
.rtl svg[data-lucide="LayoutLeft"],
.rtl svg[data-lucide="LayoutRight"],
.rtl .lucide-layout-left,
.rtl .lucide-layout-right,
.rtl svg.lucide-layout-left,
.rtl svg.lucide-layout-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de dock */
.rtl .icon-dock-left,
.rtl .icon-dock-right,
.rtl [data-icon="dock-left"],
.rtl [data-icon="dock-right"],
.rtl svg[data-lucide="DockLeft"],
.rtl svg[data-lucide="DockRight"],
.rtl .lucide-dock-left,
.rtl .lucide-dock-right,
.rtl svg.lucide-dock-left,
.rtl svg.lucide-dock-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de drawer */
.rtl .icon-drawer-left,
.rtl .icon-drawer-right,
.rtl [data-icon="drawer-left"],
.rtl [data-icon="drawer-right"],
.rtl svg[data-lucide="DrawerLeft"],
.rtl svg[data-lucide="DrawerRight"],
.rtl .lucide-drawer-left,
.rtl .lucide-drawer-right,
.rtl svg.lucide-drawer-left,
.rtl svg.lucide-drawer-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de sheet */
.rtl .icon-sheet-left,
.rtl .icon-sheet-right,
.rtl [data-icon="sheet-left"],
.rtl [data-icon="sheet-right"],
.rtl svg[data-lucide="SheetLeft"],
.rtl svg[data-lucide="SheetRight"],
.rtl .lucide-sheet-left,
.rtl .lucide-sheet-right,
.rtl svg.lucide-sheet-left,
.rtl svg.lucide-sheet-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de modal */
.rtl .icon-modal-left,
.rtl .icon-modal-right,
.rtl [data-icon="modal-left"],
.rtl [data-icon="modal-right"],
.rtl svg[data-lucide="ModalLeft"],
.rtl svg[data-lucide="ModalRight"],
.rtl .lucide-modal-left,
.rtl .lucide-modal-right,
.rtl svg.lucide-modal-left,
.rtl svg.lucide-modal-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de dialog */
.rtl .icon-dialog-left,
.rtl .icon-dialog-right,
.rtl [data-icon="dialog-left"],
.rtl [data-icon="dialog-right"],
.rtl svg[data-lucide="DialogLeft"],
.rtl svg[data-lucide="DialogRight"],
.rtl .lucide-dialog-left,
.rtl .lucide-dialog-right,
.rtl svg.lucide-dialog-left,
.rtl svg.lucide-dialog-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de popover */
.rtl .icon-popover-left,
.rtl .icon-popover-right,
.rtl [data-icon="popover-left"],
.rtl [data-icon="popover-right"],
.rtl svg[data-lucide="PopoverLeft"],
.rtl svg[data-lucide="PopoverRight"],
.rtl .lucide-popover-left,
.rtl .lucide-popover-right,
.rtl svg.lucide-popover-left,
.rtl svg.lucide-popover-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de tooltip */
.rtl .icon-tooltip-left,
.rtl .icon-tooltip-right,
.rtl [data-icon="tooltip-left"],
.rtl [data-icon="tooltip-right"],
.rtl svg[data-lucide="TooltipLeft"],
.rtl svg[data-lucide="TooltipRight"],
.rtl .lucide-tooltip-left,
.rtl .lucide-tooltip-right,
.rtl svg.lucide-tooltip-left,
.rtl svg.lucide-tooltip-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de dropdown */
.rtl .icon-dropdown-left,
.rtl .icon-dropdown-right,
.rtl [data-icon="dropdown-left"],
.rtl [data-icon="dropdown-right"],
.rtl svg[data-lucide="DropdownLeft"],
.rtl svg[data-lucide="DropdownRight"],
.rtl .lucide-dropdown-left,
.rtl .lucide-dropdown-right,
.rtl svg.lucide-dropdown-left,
.rtl svg.lucide-dropdown-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de combobox */
.rtl .icon-combobox-left,
.rtl .icon-combobox-right,
.rtl [data-icon="combobox-left"],
.rtl [data-icon="combobox-right"],
.rtl svg[data-lucide="ComboboxLeft"],
.rtl svg[data-lucide="ComboboxRight"],
.rtl .lucide-combobox-left,
.rtl .lucide-combobox-right,
.rtl svg.lucide-combobox-left,
.rtl svg.lucide-combobox-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de select */
.rtl .icon-select-left,
.rtl .icon-select-right,
.rtl [data-icon="select-left"],
.rtl [data-icon="select-right"],
.rtl svg[data-lucide="SelectLeft"],
.rtl svg[data-lucide="SelectRight"],
.rtl .lucide-select-left,
.rtl .lucide-select-right,
.rtl svg.lucide-select-left,
.rtl svg.lucide-select-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de listbox */
.rtl .icon-listbox-left,
.rtl .icon-listbox-right,
.rtl [data-icon="listbox-left"],
.rtl [data-icon="listbox-right"],
.rtl svg[data-lucide="ListboxLeft"],
.rtl svg[data-lucide="ListboxRight"],
.rtl .lucide-listbox-left,
.rtl .lucide-listbox-right,
.rtl svg.lucide-listbox-left,
.rtl svg.lucide-listbox-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de menu */
.rtl .icon-menu-left,
.rtl .icon-menu-right,
.rtl [data-icon="menu-left"],
.rtl [data-icon="menu-right"],
.rtl svg[data-lucide="MenuLeft"],
.rtl svg[data-lucide="MenuRight"],
.rtl .lucide-menu-left,
.rtl .lucide-menu-right,
.rtl svg.lucide-menu-left,
.rtl svg.lucide-menu-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de navigation */
.rtl .icon-navigation-left,
.rtl .icon-navigation-right,
.rtl [data-icon="navigation-left"],
.rtl [data-icon="navigation-right"],
.rtl svg[data-lucide="NavigationLeft"],
.rtl svg[data-lucide="NavigationRight"],
.rtl .lucide-navigation-left,
.rtl .lucide-navigation-right,
.rtl svg.lucide-navigation-left,
.rtl svg.lucide-navigation-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de tabs */
.rtl .icon-tabs-left,
.rtl .icon-tabs-right,
.rtl [data-icon="tabs-left"],
.rtl [data-icon="tabs-right"],
.rtl svg[data-lucide="TabsLeft"],
.rtl svg[data-lucide="TabsRight"],
.rtl .lucide-tabs-left,
.rtl .lucide-tabs-right,
.rtl svg.lucide-tabs-left,
.rtl svg.lucide-tabs-right {
  transform: scaleX(-1);
}

/* Exception pour les icônes dans les onglets */
.rtl [data-tab-trigger="true"] svg {
  transform: none !important; /* Ne pas inverser ces icônes */
}

/* Inverser les icônes de tablist */
.rtl .icon-tablist-left,
.rtl .icon-tablist-right,
.rtl [data-icon="tablist-left"],
.rtl [data-icon="tablist-right"],
.rtl svg[data-lucide="TablistLeft"],
.rtl svg[data-lucide="TablistRight"],
.rtl .lucide-tablist-left,
.rtl .lucide-tablist-right,
.rtl svg.lucide-tablist-left,
.rtl svg.lucide-tablist-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de tabpanel */
.rtl .icon-tabpanel-left,
.rtl .icon-tabpanel-right,
.rtl [data-icon="tabpanel-left"],
.rtl [data-icon="tabpanel-right"],
.rtl svg[data-lucide="TabpanelLeft"],
.rtl svg[data-lucide="TabpanelRight"],
.rtl .lucide-tabpanel-left,
.rtl .lucide-tabpanel-right,
.rtl svg.lucide-tabpanel-left,
.rtl svg.lucide-tabpanel-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de card */
.rtl .icon-card-left,
.rtl .icon-card-right,
.rtl [data-icon="card-left"],
.rtl [data-icon="card-right"],
.rtl svg[data-lucide="CardLeft"],
.rtl svg[data-lucide="CardRight"],
.rtl .lucide-card-left,
.rtl .lucide-card-right,
.rtl svg.lucide-card-left,
.rtl svg.lucide-card-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de box */
.rtl .icon-box-left,
.rtl .icon-box-right,
.rtl [data-icon="box-left"],
.rtl [data-icon="box-right"],
.rtl svg[data-lucide="BoxLeft"],
.rtl svg[data-lucide="BoxRight"],
.rtl .lucide-box-left,
.rtl .lucide-box-right,
.rtl svg.lucide-box-left,
.rtl svg.lucide-box-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de panel */
.rtl .icon-panel-left,
.rtl .icon-panel-right,
.rtl [data-icon="panel-left"],
.rtl [data-icon="panel-right"],
.rtl svg[data-lucide="PanelLeft"],
.rtl svg[data-lucide="PanelRight"],
.rtl .lucide-panel-left,
.rtl .lucide-panel-right,
.rtl svg.lucide-panel-left,
.rtl svg.lucide-panel-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de tile */
.rtl .icon-tile-left,
.rtl .icon-tile-right,
.rtl [data-icon="tile-left"],
.rtl [data-icon="tile-right"],
.rtl svg[data-lucide="TileLeft"],
.rtl svg[data-lucide="TileRight"],
.rtl .lucide-tile-left,
.rtl .lucide-tile-right,
.rtl svg.lucide-tile-left,
.rtl svg.lucide-tile-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de form */
.rtl .icon-form-left,
.rtl .icon-form-right,
.rtl [data-icon="form-left"],
.rtl [data-icon="form-right"],
.rtl svg[data-lucide="FormLeft"],
.rtl svg[data-lucide="FormRight"],
.rtl .lucide-form-left,
.rtl .lucide-form-right,
.rtl svg.lucide-form-left,
.rtl svg.lucide-form-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de input */
.rtl .icon-input-left,
.rtl .icon-input-right,
.rtl [data-icon="input-left"],
.rtl [data-icon="input-right"],
.rtl svg[data-lucide="InputLeft"],
.rtl svg[data-lucide="InputRight"],
.rtl .lucide-input-left,
.rtl .lucide-input-right,
.rtl svg.lucide-input-left,
.rtl svg.lucide-input-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de textarea */
.rtl .icon-textarea-left,
.rtl .icon-textarea-right,
.rtl [data-icon="textarea-left"],
.rtl [data-icon="textarea-right"],
.rtl svg[data-lucide="TextareaLeft"],
.rtl svg[data-lucide="TextareaRight"],
.rtl .lucide-textarea-left,
.rtl .lucide-textarea-right,
.rtl svg.lucide-textarea-left,
.rtl svg.lucide-textarea-right {
  transform: scaleX(-1);
}

/* Inverser les icônes de textbox */
.rtl .icon-textbox-left,
.rtl .icon-textbox-right,
.rtl [data-icon="textbox-left"],
.rtl [data-icon="textbox-right"],
.rtl svg[data-lucide="TextboxLeft"],
.rtl svg[data-lucide="TextboxRight"],
.rtl .lucide-textbox-left,
.rtl .lucide-textbox-right,
.rtl svg.lucide-textbox-left,
.rtl svg.lucide-textbox-right {
  transform: scaleX(-1);
}

/* Styles pour inverser les icônes directionnelles en mode RTL */

/* Inverser les icônes de base */
[dir="rtl"] svg[data-lucide="ArrowLeft"],
[dir="rtl"] svg[data-lucide="ArrowRight"],
[dir="rtl"] svg[data-lucide="ChevronLeft"],
[dir="rtl"] svg[data-lucide="ChevronRight"],
[dir="rtl"] svg[data-lucide="ChevronsLeft"],
[dir="rtl"] svg[data-lucide="ChevronsRight"],
[dir="rtl"] svg[data-lucide="ArrowLeftCircle"],
[dir="rtl"] svg[data-lucide="ArrowRightCircle"],
[dir="rtl"] svg[data-lucide="ArrowLeftSquare"],
[dir="rtl"] svg[data-lucide="ArrowRightSquare"],
[dir="rtl"] svg[data-lucide="ChevronFirst"],
[dir="rtl"] svg[data-lucide="ChevronLast"],
[dir="rtl"] svg[data-lucide="MoveLeft"],
[dir="rtl"] svg[data-lucide="MoveRight"],
[dir="rtl"] svg[data-lucide="Undo"],
[dir="rtl"] svg[data-lucide="Redo"],
[dir="rtl"] svg[data-lucide="Reply"],
[dir="rtl"] svg[data-lucide="Forward"],
[dir="rtl"] svg[data-lucide="Undo2"],
[dir="rtl"] svg[data-lucide="Redo2"],
[dir="rtl"] svg[data-lucide="CornerLeftUp"],
[dir="rtl"] svg[data-lucide="CornerRightUp"],
[dir="rtl"] svg[data-lucide="CornerLeftDown"],
[dir="rtl"] svg[data-lucide="CornerRightDown"],
[dir="rtl"] svg[data-rtl-mirror="true"] {
  transform: scaleX(-1);
}

/* Spécifique pour le placement des icônes dans les éléments d'interface */
[dir="rtl"] .ml-auto svg {
  margin-left: 0;
  margin-right: auto;
}

[dir="rtl"] .mr-auto svg {
  margin-right: 0;
  margin-left: auto;
}

/* Ajustements pour les icônes dans les menus */
[dir="rtl"] [role="menuitem"] svg,
[dir="rtl"] [role="option"] svg {
  transform-origin: center;
}

/* Pour les icônes dans les onglets */
[dir="rtl"] [role="tab"] svg {
  transform-origin: center;
}

/* Pour les icônes dans les boutons directionnels */
[dir="rtl"] button[aria-label*="previous"],
[dir="rtl"] button[aria-label*="next"],
[dir="rtl"] button[aria-label*="précédent"],
[dir="rtl"] button[aria-label*="suivant"] {
  transform: scaleX(-1);
}

/* Pour les icônes spécifiques à certains composants */
[dir="rtl"] .pagination-nav svg,
[dir="rtl"] .carousel-nav svg,
[dir="rtl"] .slider-nav svg {
  transform: scaleX(-1);
}
