"use client";

import { useEffect, useRef } from "react";
import { useLanguage } from "./language-provider";

export function LanguageCacheDetector() {
  const { language } = useLanguage();
  const previousLanguageRef = useRef<string | null>(null);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Enhanced PWA detection (similar to OfflineCacheManager)
    const isStandalone = window.matchMedia("(display-mode: standalone)").matches;
    const isInWebAppiOS = (window.navigator as any).standalone === true;
    let pwaConsideredInstalled = isStandalone || isInWebAppiOS;

    const isDevelopment = window.location.hostname === 'localhost';
    if (isDevelopment && !pwaConsideredInstalled) {
        // In development, we often want to test PWA caching logic
        // without fully installing the PWA.
        pwaConsideredInstalled = true;
        console.log('[Language Cache Detector] Development mode: PWA status forced to installed for testing caching.');
    }

    if (!pwaConsideredInstalled) {
      console.log('[Language Cache Detector] PWA not considered installed, skipping language cache logic.');
      previousLanguageRef.current = language; // Update ref to avoid false "change" on next render if PWA becomes installed
      return;
    }

    // If PWA is considered installed, ensure the SW knows it.
    // This can help if the initial PWA_INSTALLED message from another source was missed
    // or if the SW restarted.
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.ready.then((registration) => {
            if (registration.active) {
                // console.log('[Language Cache Detector] PWA considered installed. Notifying SW.');
                registration.active.postMessage({ type: 'PWA_INSTALLED' });
            }
        });
    }

    const previousLanguage = previousLanguageRef.current;

    if (language) { // Ensure we have a language
        if (previousLanguage && previousLanguage !== language) {
            console.log(`[Language Cache Detector] Language changed from ${previousLanguage} to ${language}. Requesting cache update.`);
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.ready.then((registration) => {
                    registration.active?.postMessage({
                        type: 'LANGUAGE_CHANGED',
                        newLang: language,
                        oldLang: previousLanguage
                    });
                });
            }
        } else {
            // This covers:
            // 1. Initial load (previousLanguage is null)
            // 2. Language is the same as before, but we want to ensure it's cached (e.g., after PWA install and reload)
            console.log(`[Language Cache Detector] Ensuring current language '${language}' is cached.`);
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.ready.then((registration) => {
                    registration.active?.postMessage({
                        type: 'CACHE_LANGUAGE',
                        lang: language
                    });
                });
            }
        }
    }

    previousLanguageRef.current = language;
  }, [language]);

  return null; // This component doesn't render anything
}
