const zhTranslations = {
  // 导航和界面
  appName: "Timer Kit", // Timer Kit
  timeTools: "时间工具",
  productivitySuite: "效率套件",
  professionalTools: "专业工具",
  fitnessTools: "健身工具",
  coreFeatures: "核心功能",
  home: "首页",

  // 页面标题和元描述
  homePageTitle: "Timer Kit - 专业在线时间管理工具",
  homePageDescription: "免费在线时间管理工具：秒表、倒计时、世界时钟、番茄钟等，提高您的工作效率。",

  timerPageTitle: "在线计时器和秒表 - 免费时间测量工具 | Timer Kit",
  timerPageDescription: "专业的在线计时器和秒表，具有计次、声音提醒和全屏模式。免费且易于使用，用于精确的时间测量。",

  countdownPageTitle: "在线倒计时器 - 免费时间管理工具 | Timer Kit",
  countdownPageDescription: "灵活的在线倒计时器，具有声音提醒和自定义预设，提高效率。非常适合烹饪、锻炼和会议。",

  worldClockPageTitle: "在线世界时钟 - 免费时区转换器 | Timer Kit",
  worldClockPageDescription: "使用我们易于使用的在线世界时钟工具跟踪多个时区。非常适合国际团队和全球协调。",

  intervalsPageTitle: "在线间隔计时器 - 免费锻炼和效率工具 | Timer Kit",
  intervalsPageDescription: "为效率、运动、HIIT训练等创建自定义在线间隔计时器。免费且可定制，满足您的所有需求。",

  pomodoroPageTitle: "在线番茄钟计时器 - 免费效率工具 | Timer Kit",
  pomodoroPageDescription: "使用我们的免费在线番茄工作法计时器提高效率，具有可自定义的工作/休息间隔。非常适合专注的工作会话。",

  todoPageTitle: "在线待办事项列表 - 免费任务管理工具 | Timer Kit",
  todoPageDescription: "简单有效的在线待办事项列表，用于管理您的任务并提高效率。免费提供类别、优先级和截止日期功能。",

  timeTrackingPageTitle: "在线时间追踪 - 免费效率工具 | Timer Kit",
  timeTrackingPageDescription: "通过我们的免费在线时间追踪工具跟踪活动、项目和任务所花费的时间，提供详细报告和分析。",

  meetingTimerPageTitle: "在线会议计时器 - 免费专业工具 | Timer Kit",
  meetingTimerPageDescription: "使用我们的免费专业在线会议计时器和成本计算器，让会议按时进行。非常适合高效的团队会议。",

  timeBillingPageTitle: "在线计时计费 - 免费专业工具 | Timer Kit",
  timeBillingPageDescription: "使用我们的免费在线计时计费工具跟踪可计费小时数并为客户和项目生成报告，专为专业人士设计。",

  workoutIntervalsPageTitle: "在线锻炼间隔 - 免费健身计时器 | Timer Kit",
  workoutIntervalsPageDescription: "使用我们的免费在线锻炼间隔计时器为 HIIT、循环训练等创建并保存间隔锻炼。",

  exerciseTemplatesPageTitle: "在线锻炼模板 - 免费训练计划工具 | Timer Kit",
  exerciseTemplatesPageDescription: "使用我们的免费在线锻炼模板工具存储和管理您的锻炼计划。非常适合跟踪您的健身进度。",

  // 时间工具
  timerStopwatch: "计时器 / 秒表",
  countdown: "倒计时",
  worldClock: "世界时钟",
  intervals: "间隔",
  pomodoro: "番茄钟",

  // 效率
  todoList: "待办事项列表",
  timeTracking: "时间追踪",

  // 专业
  meetingTimer: "会议计时器",
  timeBilling: "计时计费",

  // 健身
  workoutIntervals: "锻炼间隔",
  exerciseTemplates: "锻炼模板",

  // 核心功能
  multiLanguage: "多语言",
  pwaOffline: "PWA/离线",
  fullscreen: "全屏",
  soundEffects: "音效",
  searchLanguages: "搜索语言...",
  selectLanguage: "选择语言",

  // 计时器/秒表
  stopwatch: "秒表",
  lap: "计次",
  laps: "计次",
  start: "开始",
  pause: "暂停",
  reset: "重置",

  // 倒计时
  selectTime: "选择时间",
  customTime: "自定义时间",
  minutes: "分钟",

  // 全屏
  enterFullscreen: "进入全屏",
  exitFullscreen: "退出全屏",

  // 主题
  lightTheme: "浅色",
  darkTheme: "深色",
  systemTheme: "系统",

  // 声音
  soundOn: "声音开启",
  soundOff: "声音关闭",
  bellSound: "铃声",
  alarmSound: "闹钟",
  notificationSound: "通知",

  // PWA
  installApp: "安装应用",
  appScreenshotDesktop: "Timer Kit 仪表板 - 桌面视图",
  appScreenshotMobile: "Timer Kit 仪表板 - 移动视图",

  // 通用界面
  add: "添加",
  remove: "移除",
  edit: "编辑",
  save: "保存",
  cancel: "取消",
  delete: "删除",
  loading: "加载中...",
  search: "搜索",
  filter: "筛选",
  sort: "排序",
  settings: "设置",
  profile: "个人资料",
  logout: "登出",
  login: "登录",
  register: "注册",

  // 会议计时器
  meetingTitle: "会议标题",
  participants: "参与者",
  addParticipant: "添加参与者",
  participantName: "参与者姓名",
  hourlyCost: "每小时成本",
  totalCost: "总成本",
  name: "姓名",
  hourlyRate: "小时费率",
  addNewParticipant: "添加新参与者",
  plannedDuration: "计划时长",
  agenda: "议程",
  notes: "笔记",
  current: "当前",
  approachingTimeLimit: "接近时间限制",
  currentAgendaItem: "当前议程项目",
  loadSavedMeeting: "加载已保存的会议",
  role: "角色",
  agendaItemTitle: "议程项目标题",
  addAgendaItem: "添加议程项目",
  meetingNotes: "会议笔记",
  enterNotesHere: "在此处输入您的会议笔记...",
  saveMeeting: "保存会议",
  export: "导出",
  backToTimer: "返回计时器",
  title: "标题",
  nextAgendaItem: "下一个议程项目",
  confirmDeleteMeeting: '您确定要删除会议 "{title}" 吗？此操作不可撤销。',
  nextItem: "下一个",
  estimatedCost: "预估成本（当前）",
  basedOnElapsedTime: "基于已用时间",
  newMeeting: "新会议",
  noAgendaItemsAdded: "尚未添加议程项目。",
  notesAreAutoSaved: "笔记会自动随活动会议保存。",
  noParticipantsAdded: "尚未添加参与者。",
  enterMeetingTitle: "输入会议标题",
  pleaseEnterTitle: "请输入会议标题。",
  errorCannotSave: "错误：无法保存，因为没有活动会议。",
  errorSaveFailed: "保存会议时出错。",
  errorLoadFailed: "错误：无法加载会议 ID {id} 的数据。",
  noActiveMeetingToExport: "没有活动会议可供导出。",
  meetingDataNotFound: "未找到会议数据。",
  moveToNextItem: "移至下一项（将当前项标记为已完成）",
  markAsCompleted: "标记为已完成",
  markAsNotCompleted: "标记为未完成",
  currentMeeting: "当前会议",
  optional: "可选",
  noActiveMeeting: "无活动会议",
  confirmDeleteMeetingTitle: "确认删除",
  noTitle: "无标题",


  // 计时计费
  entries: "条目",
  newEntry: "新条目",
  addEntry: "添加条目",
  timeEntries: "时间条目",
  exportCSV: "导出 CSV",
  date: "日期",
  client: "客户",
  project: "项目",
  description: "描述",
  hours: "小时",
  rate: "费率",
  amount: "金额",
  total: "总计",
  hourlyRateDollar: "小时费率 ($)",
  noEntriesYet: "尚无时间条目。添加一个新条目。",
  clientName: "客户名称",
  projectName: "项目名称",
  taskDescription: "任务描述",

  // 锻炼
  workout: "锻炼",
  interval: "间隔",
  work: "工作",
  rest: "休息",
  warmUp: "热身",
  coolDown: "放松",
  sprint: "冲刺",
  recovery: "恢复",
  stretching: "拉伸",
  loadTemplate: "加载模板",
  saveTemplate: "保存模板",
  templateName: "模板名称",
  duration: "时长",
  remaining: "剩余",
  type: "类型",
  intervalType: "间隔类型",
  addInterval: "添加间隔",
  intervalName: "间隔名称",
  durationSeconds: "时长（秒）",
  totalDuration: "总时长",
  noIntervalsYet: "尚未定义间隔。在下方添加一个。",
  noTemplatesYet: "尚无模板。创建一个新模板。",
  templates: "模板",
  createTemplate: "创建模板",
  exercises: "练习",
  sets: "组",
  reps: "次",
  restTime: "休息时间",
  estimatedTime: "预估时间",
  startWorkout: "开始锻炼",
  exerciseName: "练习名称",
  addExercise: "添加练习",
  editTemplate: "编辑模板",
  duplicateTemplate: "复制",
  deleteTemplate: "删除",
  play: "播放",
  loadQuickTemplate: "快速加载",
  updateTemplateName: '更新 "{name}"',
  noIntervalsDefine: "未定义间隔。",
  addNewInterval: "添加新间隔",
  intervalNamePlaceholder: "例如，高抬腿",
  selectType: "选择类型...",
  saveCurrentListAsTemplate: "将当前列表另存为模板",
  saveTemplateHint: "将上面的当前间隔列表保存为可重复使用的模板。",
  manageTemplates: "管理模板",
  confirmDeleteTemplate: '您确定要删除模板 "{name}" 吗？',
  noIntervals: "无间隔",
  defaultTemplateName: "HIIT 4 分钟",
  error: "错误",
  finishWorkout: "完成锻炼",
  completeSet: "完成组",
  stopWorkout: "停止锻炼",
  createOneNow: "现在创建一个？",
  noDescription: "无描述",
  exercise: "练习",
  estimatedTotalTime: "预估总时间",
  templateNamePlaceholder: "例如，晨间例行",
  descriptionPlaceholder: "例如，快速锻炼以开始新的一天",
  noExercisesAddedYet: "尚未添加练习。",
  addNewExercise: "添加新练习",
  exerciseNamePlaceholder: "例如，俯卧撑",
  addExerciseToList: "将练习添加到列表",
  saveNewTemplate: "保存新模板",
  saveChanges: "保存更改",



  // 世界时钟
  addCity: "添加城市",
  today: "今天",
  yesterday: "昨天",
  tomorrow: "明天",
  timeFormat: "时间格式",
  hour24: "24 小时制",
  hour12: "12 小时制",
  dst: "夏令时",
  std: "标准时",
  daylightTime: "夏令时",
  standardTime: "标准时间",
  selectTimezone: "选择时区",
  localTime: "本地时间",

  // 间隔
  intervalsTitle: "间隔",
  noIntervalsDefinedYet: "尚未定义间隔。在下方添加一个。",
  saveIntervalSet: "保存间隔组",
  intervalSetName: "组名称",

  // 番茄钟
  workDuration: "工作时长",
  shortBreak: "短时休息",
  longBreak: "长时休息",
  longBreakInterval: "长时休息间隔",
  completedPomodoros: "已完成的番茄钟",
  pomodoros: "个番茄钟",
  timer: "计时器",
  workPhase: "工作",
  shortBreakPhase: "短时休息",
  longBreakPhase: "长时休息",

  // 待办事项列表
  addTask: "添加任务",
  noTasksYet: "尚无任务。添加一个！",

  // 时间追踪
  tracker: "追踪器",
  statistics: "统计",
  whatAreYouDoing: "您在做什么？",
  startedAt: "开始于",
  inProgress: "进行中",
  timeByCategory: "按类别分类的时间",
  timeByTag: "按标签分类的时间",
  tags: "标签",
  addTags: "添加标签",
  meetingCategory: "会议",
  learningCategory: "学习",
  timeTrackingFooter: "高效地跟踪您的时间",
  exportData: "导出数据",
  editing: "编辑中",
  updateSet: "更新组",
  savedIntervalSets: "已保存的间隔组",
  noSavedSets: "尚无已保存的间隔组",
  createNewSet: "创建新组",
  intervalSets: "间隔组",
  lastUsed: "上次使用",
  duplicate: "复制",
  load: "加载",
  every: "每",
  set: "组",
  complete: "完成",
  stop: "停止",
  workoutProgress: "锻炼进度",

  // 功能描述
  featuresTitle: "全面的时间管理功能",
  featuresDescription:
    "探索我们完整的时​​间管理工具套件，旨在提高您的效率和组织能力。",
  timeToolsDesc: "用于测量和管理时间的基本工具",
  productivitySuiteDesc: "提高您的效率并跟踪您的进度",
  professionalToolsDesc: "适用于专业工作环境的解决方案",
  coreFeaturesDesc: "增强您体验的功能",
  fitnessFeaturesDesc: "优化锻炼和跟踪表现的工具",

  // SEO 内容
  seoTitle: "您需要了解的关于时间管理的一切",
  seoIntro: "我们的免费在线时间管理工具帮助您保持组织、专注和高效。无论您需要计时器、秒表、倒计时还是番茄工作法，我们的工具套件都能满足您有效管理时间的所有需求。",
  whatIsTimer: "什么是计时器和倒计时？",
  timerExplanation:
    "计时器是从零开始测量经过时间的工具，而倒计时是从设定的持续时间倒数到零。这些工具对于各种日常、专业和体育活动至关重要。",
  timerDeviceInfo: "我们的在线计时器和秒表工具设计直观且易于在任何设备上使用。它们在台式电脑、平板电脑和手机上都能完美运行，让您随时随地都能使用。",
  benefitsOfTimers: "使用时间管理工具的好处",
  benefit1: "提高效率和专注力",
  benefit2: "更好地管理任务和项目",
  benefit3: "减少与截止日期相关的压力",
  benefit4: "优化日常时间使用",
  benefitsExplanation: "使用在线时间管理工具可以显著提高您的生产力，帮助您保持更好的工作与生活平衡。通过跟踪您如何使用时间，您可以确定需要改进的领域并优化您的日常安排。",
  howToUse: "如何有效使用我们的时间工具",
  howToUseExplanation:
    "我们的时间管理工具设计直观且易于使用。以下是如何充分利用我们的应用程序：",
  step1: "根据您的需求选择合适的工具（秒表、倒计时等）",
  step2: "根据您的偏好配置设置",
  step3: "使用间隔或闹钟等高级功能",
  step4: "检查您的统计数据以分析您的时间使用情况",
  toolsOfflineInfo: "我们所有的在线工具都完全免费，无需注册。它们作为渐进式 Web 应用程序 (PWA) 可以离线工作，因此您可以将它们安装在您的设备上，即使没有互联网连接也能使用。",
  faq: "常见问题解答",

  // General FAQ
  GeneralQuestions: "常见问题",
  faqQuestion1: "我可以离线使用该应用程序吗？",
  faqAnswer1:
    "是的，我们的应用程序是 PWA（渐进式 Web 应用程序），可以安装在您的设备上并在没有互联网连接的情况下使用。安装后所有工具都可以离线工作。",

  faqQuestion2: "应用程序完全免费吗？",
  faqAnswer2:
    "是的，我们所有的时间管理工具都完全免费使用，无需注册。没有隐藏费用或高级功能。",

  faqQuestion3: "如何在我的设备上安装应用程序？",
  faqAnswer3:
    "您可以通过点击浏览器中的'安装应用'按钮或使用访问我们网站时出现的安装提示来安装我们的 PWA。",

  faqQuestion4: "支持哪些设备和浏览器？",
  faqAnswer4:
    "我们的应用程序适用于所有现代浏览器（Chrome、Firefox、Safari、Edge）和设备，包括台式机、平板电脑和手机。",

  // Tools FAQ
  ToolsQuestions: "工具和功能",
  faqQuestion5: "计时器和倒计时有什么区别？",
  faqAnswer5:
    "计时器/秒表从零开始向上计数，非常适合测量经过的时间，而倒计时从设定时间向下计数到零，非常适合时间限制和截止日期。",

  faqQuestion6: "我可以为倒计时器保存自定义预设吗？",
  faqAnswer6:
    "是的，您可以创建和保存自定义时间预设，以便快速访问您经常使用的倒计时持续时间。",

  faqQuestion7: "番茄钟计时器如何工作？",
  faqAnswer7:
    "番茄钟计时器在工作会话（默认 25 分钟）和休息（5 分钟）之间交替，每 4 个工作会话后有更长的休息时间。所有持续时间都可以自定义。",

  faqQuestion8: "我可以创建自定义锻炼间隔吗？",
  faqAnswer8:
    "是的，间隔和锻炼间隔工具都允许您创建具有不同工作和休息时间的自定义序列，并将它们保存为模板以供将来使用。",

  faqQuestion9: "会议计时器如何计算成本？",
  faqAnswer9:
    "会议计时器通过将每个参与者的小时费率乘以会议持续时间来计算成本，实时为您提供会议的总成本。",

  faqQuestion10: "我可以导出我的时间跟踪数据吗？",
  faqAnswer10:
    "是的，时间跟踪和时间计费工具都允许您以 CSV 格式导出数据，以便进一步分析或报告。",

  // Technical FAQ
  TechnicalQuestions: "技术和设置",
  faqQuestion11: "如何自定义声音警报？",
  faqAnswer11:
    "您可以使用每个计时器工具中可用的声音设置从不同的内置声音（铃声、闹钟、通知）中进行选择。",

  faqQuestion12: "应用程序在全屏模式下工作吗？",
  faqAnswer12:
    "是的，大多数工具都支持全屏模式以获得更好的可见性。在工具界面中查找全屏按钮。",

  faqQuestion13: "支持多少种语言？",
  faqAnswer13:
    "我们的应用程序支持 60 多种语言，可根据您的浏览器设置自动检测。您也可以手动选择您的首选语言。",

  faqQuestion14: "我的数据会自动保存吗？",
  faqAnswer14:
    "是的，您的所有设置、预设、模板和数据都会自动保存在浏览器的本地存储中，并在会话之间保持。",

  faqQuestion15: "我可以同时使用多个计时器吗？",
  faqAnswer15:
    "您可以打开多个浏览器标签页使用不同的工具，但每个工具都独立运行。对于复杂的计时需求，请考虑使用间隔或会议计时器工具。",

  // Tool SEO Content
  keyFeatures: "主要功能",
  whyUseOurTool: "为什么使用我们的{toolName}？",
  toolFreeInfo: "我们的{toolName}完全免费，无需注册。它适用于所有设备，包括台式电脑、平板电脑和手机。您甚至可以将其安装为渐进式 Web 应用程序以便离线使用，使其成为满足您所有时间管理需求的完美工具。",
  toolProductivityInfo: "凭借用户友好的界面和强大的功能，我们的{toolName}帮助您保持组织和高效。立即尝试，看看它如何提升您的时间管理技能！",

  // Tool-specific content
  timerWhyUse: "我们的在线计时器和秒表非常适合运动训练、烹饪、学习以及任何需要精确时间测量的活动。圈速功能允许您跟踪多个段落，非常适合间隔训练和比赛计时。",
  countdownWhyUse: "我们的倒计时器非常适合烹饪、锻炼、演示和时间管理。借助可自定义的预设和声音提醒，您再也不会错过重要的截止日期或过度烹饪一餐。",
  worldClockWhyUse: "我们的世界时钟对于国际团队、旅行者以及任何在不同时区工作的人都是必不可少的。它消除了时差混淆，帮助您在对所有参与者都方便的时间安排会议。",
  intervalsWhyUse: "我们的间隔计时器专为 HIIT 锻炼、循环训练和番茄工作法等生产力技术而设计。创建自定义间隔序列以优化您的训练或工作会话。",
  pomodoroWhyUse: "番茄工作法是一种经过验证的生产力方法，它使用计时的工作会话和休息时间来保持专注并防止精疲力竭。我们的番茄钟计时器使您能够轻松地将这种技术融入日常生活。",
  todoWhyUse: "一个组织良好的待办事项列表是良好时间管理的基础。我们的待办事项列表工具帮助您确定任务优先级、设置截止日期并跟踪进度，确保不会遗漏任何重要事项。",
  timeTrackingWhyUse: "了解您如何使用时间是提高生产力的第一步。我们的时间追踪工具提供有关您的时间使用模式的见解，并帮助您确定需要改进的领域。",
  meetingTimerWhyUse: "如果管理不当，会议可能会浪费大量时间。我们的会议计时器帮助讨论保持在正轨上，并根据参与者的小时费率计算会议的实际成本。",
  timeBillingWhyUse: "对于按小时收费的自由职业者和专业人士，准确的时间跟踪至关重要。我们的计时计费工具使跟踪可计费小时数和为客户生成报告变得容易。",
  workoutIntervalsWhyUse: "间隔训练是改善健康状况和燃烧卡路里的最有效训练方法之一。我们的锻炼间隔工具帮助您设计和执行完美的间隔训练会话。",
  exerciseTemplatesWhyUse: "一致性是健身进步的关键。我们的锻炼模板工具帮助您创建和遵循结构化的训练例程，确保您在训练中保持良好的形式和进步。",

  // Timer Features
  timerFeature1: "毫秒精度的精确时间测量",
  timerFeature2: "圈速记录以跟踪多个段落",
  timerFeature3: "可自定义的声音提醒",
  timerFeature4: "全屏模式以提高可见性",
  timerFeature5: "作为渐进式 Web 应用程序离线工作",

  // Countdown Features
  countdownFeature1: "常用时间段的可自定义预设",
  countdownFeature2: "倒计时结束时的声音提醒",
  countdownFeature3: "用于间隔训练的自动重启选项",
  countdownFeature4: "后台通知",
  countdownFeature5: "作为渐进式 Web 应用程序离线工作",

  // World Clock Features
  worldClockFeature1: "同时跟踪多个时区",
  worldClockFeature2: "12 小时和 24 小时时间格式选项",
  worldClockFeature3: "夏令时指示器",
  worldClockFeature4: "添加位置的用户友好界面",
  worldClockFeature5: "作为渐进式 Web 应用程序离线工作",

  // Intervals Features
  intervalsFeature1: "创建自定义间隔序列",
  intervalsFeature2: "保存和加载间隔集",
  intervalsFeature3: "间隔转换的声音提醒",
  intervalsFeature4: "视觉进度指示器",
  intervalsFeature5: "作为渐进式 Web 应用程序离线工作",

  // Pomodoro Features
  pomodoroFeature1: "可自定义的工作和休息时长",
  pomodoroFeature2: "多个工作会话后的长休息",
  pomodoroFeature3: "会话转换的声音提醒",
  pomodoroFeature4: "会话计数器以跟踪生产力",
  pomodoroFeature5: "作为渐进式 Web 应用程序离线工作",

  // Todo Features
  todoFeature1: "按优先级和类型对任务进行分类",
  todoFeature2: "设置截止日期以便更好地规划",
  todoFeature3: "任务管理的筛选和排序选项",
  todoFeature4: "任务完成跟踪",
  todoFeature5: "作为渐进式 Web 应用程序离线工作",

  // Time Tracking Features
  timeTrackingFeature1: "跟踪花在不同活动上的时间",
  timeTrackingFeature2: "使用标签对条目进行分类",
  timeTrackingFeature3: "生成详细的报告和统计数据",
  timeTrackingFeature4: "导出数据以进行进一步分析",
  timeTrackingFeature5: "作为渐进式 Web 应用程序离线工作",

  // Meeting Timer Features
  meetingTimerFeature1: "跟踪会议的持续时间和成本",
  meetingTimerFeature2: "使用时间分配管理议程项目",
  meetingTimerFeature3: "根据参与者计算会议成本",
  meetingTimerFeature4: "制作和保存会议笔记",
  meetingTimerFeature5: "作为渐进式 Web 应用程序离线工作",

  // Time Billing Features
  timeBillingFeature1: "跟踪客户和项目的可计费小时数",
  timeBillingFeature2: "根据小时费率计算收入",
  timeBillingFeature3: "生成用于计费目的的报告",
  timeBillingFeature4: "以 CSV 格式导出数据",
  timeBillingFeature5: "作为渐进式 Web 应用程序离线工作",

  // Workout Intervals Features
  workoutIntervalsFeature1: "设计自定义锻炼间隔序列",
  workoutIntervalsFeature2: "保存和加载锻炼模板",
  workoutIntervalsFeature3: "跟踪锻炼进度",
  workoutIntervalsFeature4: "间隔转换的声音提醒",
  workoutIntervalsFeature5: "作为渐进式 Web 应用程序离线工作",

  // Exercise Templates Features
  exerciseTemplatesFeature1: "创建和管理锻炼例程",
  exerciseTemplatesFeature2: "跟踪组数、重复次数和休息时间",
  exerciseTemplatesFeature3: "估计锻炼时长",
  exerciseTemplatesFeature4: "保存和加载锻炼模板",
  exerciseTemplatesFeature5: "作为渐进式 Web 应用程序离线工作",

  // Tool Titles
  timerToolTitle: "在线计时器和秒表",
  countdownToolTitle: "在线倒计时器",
  worldClockToolTitle: "在线世界时钟",
  intervalsToolTitle: "在线间隔计时器",
  pomodoroToolTitle: "在线番茄钟计时器",
  todoToolTitle: "在线待办事项列表",
  timeTrackingToolTitle: "在线时间追踪",
  meetingTimerToolTitle: "在线会议计时器",
  timeBillingToolTitle: "在线计时计费",
  workoutIntervalsToolTitle: "在线锻炼间隔",
  exerciseTemplatesToolTitle: "在线锻炼模板",
  defaultToolTitle: "在线时间管理工具",

  // 预设
  presets: "预设",
  apply: "应用",
  seconds: "秒",
  autoRestart: "自动重启",
  maxRestarts: "最大重启次数",
  infinite: "无限",
  cycle: "循环",
  saveCurrentAsPreset: "将当前设置另存为预设",
  presetName: "预设名称",
  quickBreak: "快速休息",
  meeting: "会议",
  lunch: "午餐",

  // 待办事项列表（补充）
  exampleTask1: "团队会议",
  exampleTask2: "去购物",
  searchTasks: "搜索任务",
  status: "状态",
  all: "全部",
  active: "活动",
  completed: "已完成",
  category: "类别",
  selectCategory: "选择类别",
  workCategory: "工作",
  personalCategory: "个人",
  shoppingCategory: "购物",
  healthCategory: "健康",
  otherCategory: "其他",
  priority: "优先级",
  selectPriority: "选择优先级",
  highPriority: "高",
  mediumPriority: "中",
  lowPriority: "低",
  noPriority: "无",
  sortBy: "排序方式",
  alphabetical: "按字母顺序",
  direction: "方向",
  ascending: "升序",
  descending: "降序",
  clearCompleted: "清除已完成",
  taskTitle: "任务标题",
  addNotes: "添加笔记",
  dueDate: "截止日期",
  pickDate: "选择日期",
  overdue: "已过期",
  dueToday: "今天到期",
  dueTomorrow: "明天到期",
  task: "任务",
  tasks: "任务",
  displayed: "已显示",

  // 时间追踪（补充/更新）
  entry: "条目",
  noEntriesFound: "未找到条目",
  editEntry: "编辑条目",
  totalTrackedTime: "总跟踪时间",
  entriesCount: "条目计数",
  averageDuration: "平均时长",
  searchEntries: "搜索条目",
  allCategories: "所有类别",
  allDates: "所有日期",
  clearDate: "清除日期",
  history: "历史记录",
  exampleEntry1: "前端开发",
  exampleEntry2: "团队会议",
  exampleNotes: "讨论了即将推出的功能",

  // 锻炼间隔（补充/更新）
  moreTemplates: "更多模板",
  updateTemplate: "更新模板",
  moveUp: "上移",
  moveDown: "下移",
  saveAsTemplate: "另存为模板",
  currentlySelected: "当前选中",
  saveTemplateDescription: "将您当前的间隔保存为模板以便以后重复使用",
  copy: "复制",

  // 通知（为倒计时器添加）
  notificationsBlockedTitle: "通知被阻止",
  notificationsBlockedDesc: "通知被阻止。声音可能无法在后台播放。",
  countdownFinished: "倒计时结束！",
  timerEndedBody: "计时器已结束。",

  // SEO 页面描述
  timerDescription: "免费在线秒表和计时器工具，可进行精确的时间测量，具有圈速跟踪和可自定义的提醒功能。适用于运动、烹饪、学习和专业时间测量需求。",
  countdownDescription: "免费在线倒计时器，具有可自定义的预设和声音提醒，帮助您有效管理时间。非常适合烹饪、锻炼、演示和生产力会话。",
  worldClockDescription: "免费在线世界时钟工具，通过我们直观的界面跟踪多个时区的时间。非常适合全球团队、旅行者和国际协调。",
  intervalsDescription: "免费在线间隔计时器创建工具，用于锻炼、生产力会话和时间管理，具有灵活的设置。自定义工作和休息时间段以获得最佳效果。",
  pomodoroDescription: "免费在线番茄钟计时器，通过可自定义的工作和休息间隔提高您的生产力。基于经过验证的番茄工作法，实现最大专注度和效率。",
  todoDescription: "免费在线待办事项列表工具，通过类别、优先级和截止日期整理您的任务。简单而强大的任务管理，适用于个人和专业生产力。",
  timeTrackingDescription: "免费在线时间跟踪工具，用于跟踪和分析花在活动上的时间，提供全面的报告和统计数据。非常适合自由职业者、团队和个人生产力。",
  meetingTimerDescription: "免费在线会议计时器，跟踪时间、成本和议程项目，使会议更加高效。保持团队专注并计算会议的实际成本。",
  timeBillingDescription: "免费在线时间计费工具，供自由职业者和企业管理可计费小时数并为客户和项目生成专业报告。轻松跟踪您的收入。",
  workoutIntervalsDescription: "免费在线锻炼间隔计时器，用于设计有效的训练例程，非常适合高强度间歇训练、循环训练和健身计划。创建、保存和分享您的自定义锻炼。",
  exerciseTemplatesDescription: "免费在线锻炼模板工具，用于创建和管理锻炼计划并有效跟踪您的健身进度。为任何健身目标构建自定义锻炼例程库。",

  // Internal linking and navigation
  relatedTools: "相关工具",
  exploreOurTools: "探索我们的工具：",
  exploreMoreTools: "探索更多工具",
  discoverAllFeatures: "发现我们的所有功能",
  improveProductivity: "提高您的生产力",
  timeManagementTools: "时间管理工具",
  allRightsReserved: "版权所有",
  madeWithLove: "用 ❤️ 制作",
  freeToUse: "免费使用",
  quickAccess: "快速访问",
  previous: "上一个",
  next: "下一个",

  // Sitemap page
  sitemap: "网站地图",
  sitemapDescription: "发现我们所有的时间管理和生产力工具。完整的网站地图，包含所有可用工具的直接链接。",
  sitemapIntro: "发现我们所有的时间管理和生产力工具。轻松导航到适合您需求的工具。",
  aboutOurTools: "关于我们的工具",
  toolsOverview: "我们完整的时间管理和生产力工具套件旨在帮助您优化时间并提高效率。我们所有的工具都是免费的，可离线工作，并提供多种语言版本。",
  timeToolsOverview: "有效测量、跟踪和管理时间的基本工具。",
  productivityToolsOverview: "通过我们的任务管理和时间跟踪工具提高您的生产力。",
  professionalToolsOverview: "专为专业人士和团队设计的专业工具。",
  fitnessToolsOverview: "通过我们的健身和间隔工具优化您的锻炼。",
  professionalTools: "专业工具"
};
export default zhTranslations;
