'use client';

import React from 'react';
import { useRTL } from '@/hooks/useRTL';
import { useLanguage } from './language-provider';

interface RTLAppWrapperProps {
  children: React.ReactNode;
  appName?: string;
  className?: string;
}

/**
 * Composant wrapper pour adapter les mini-applications au mode RTL
 * Ajoute des classes et attributs nécessaires pour le support RTL
 */
export function RTLAppWrapper({
  children,
  appName,
  className = '',
}: RTLAppWrapperProps) {
  const { language } = useLanguage();
  const isRTL = useRTL(language);

  return (
    <div 
      className={`rtl-app-wrapper ${className} ${isRTL ? 'rtl-enabled' : ''}`}
      data-rtl-app="true"
      data-mini-app="true"
      data-app-name={appName}
      dir={isRTL ? 'rtl' : 'ltr'}
    >
      {children}
    </div>
  );
}

/**
 * Composant pour les sections internes d'une mini-application qui nécessitent
 * une attention particulière en mode RTL (comme les sections de contrôle, formulaires, etc.)
 */
export function RTLAppSection({
  children,
  sectionType,
  className = '',
}: {
  children: React.ReactNode;
  sectionType: 'controls' | 'content' | 'form' | 'results' | 'header' | 'footer';
  className?: string;
}) {
  const { language } = useLanguage();
  const isRTL = useRTL(language);

  return (
    <div 
      className={`rtl-app-section rtl-app-${sectionType} ${className} ${isRTL ? 'rtl-enabled' : ''}`}
      data-rtl-section={sectionType}
      dir={isRTL ? 'rtl' : 'ltr'}
    >
      {children}
    </div>
  );
}

/**
 * Hook personnalisé pour obtenir les classes RTL appropriées
 * Utile pour appliquer des styles conditionnels basés sur la direction RTL
 */
export function useRTLClasses(baseClasses: string = ''): string {
  const { language } = useLanguage();
  const isRTL = useRTL(language);
  
  return `${baseClasses} ${isRTL ? 'rtl-enabled' : ''}`;
}

export default RTLAppWrapper; 