"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { getTranslations } from "@/lib/i18n/translations"
import { getTranslatedRoute } from "@/lib/i18n/route-translations"
import { motion } from "framer-motion"
import { Clock, Timer, Globe, ListTodo, Users, FileText, Activity, Smartphone, Languages, Wifi } from "lucide-react"
import Link from "next/link"
import { useLanguage } from "@/components/language-provider"

interface FeatureHighlightsProps {
  lang?: string
}

export function FeatureHighlights({ lang = "fr" }: FeatureHighlightsProps) {
  const t = getTranslations(lang)
  const { language } = useLanguage()

  const getLocalizedLink = (route: string) => {
    const translatedRoute = getTranslatedRoute(language, route)
    return `/${language}/${translatedRoute}`
  }

  const features = [
    {
      title: t.timeTools,
      description: t.timeToolsDesc,
      icon: <Clock className="h-8 w-8" />,
      items: [
        { name: t.timer<PERSON>topwatch, icon: <Timer className="h-5 w-5" />, href: getLocalizedLink("timer") },
        { name: t.countdown, icon: <Clock className="h-5 w-5" />, href: getLocalizedLink("countdown") },
        { name: t.worldClock, icon: <Globe className="h-5 w-5" />, href: getLocalizedLink("world-clock") },
        { name: t.intervals, icon: <Activity className="h-5 w-5" />, href: getLocalizedLink("intervals") },
        { name: t.pomodoro, icon: <Timer className="h-5 w-5" />, href: getLocalizedLink("pomodoro") },
      ],
    },
    {
      title: t.productivitySuite,
      description: t.productivitySuiteDesc,
      icon: <ListTodo className="h-8 w-8" />,
      items: [
        { name: t.todoList, icon: <ListTodo className="h-5 w-5" />, href: getLocalizedLink("todo") },
        { name: t.timeTracking, icon: <Clock className="h-5 w-5" />, href: getLocalizedLink("time-tracking") },
      ],
    },
    {
      title: t.professionalTools,
      description: t.professionalToolsDesc,
      icon: <Users className="h-8 w-8" />,
      items: [
        { name: t.meetingTimer, icon: <Users className="h-5 w-5" />, href: getLocalizedLink("meeting-timer") },
        { name: t.timeBilling, icon: <FileText className="h-5 w-5" />, href: getLocalizedLink("time-billing") },
      ],
    },
    {
      title: t.fitnessTools,
      description: t.fitnessFeaturesDesc,
      icon: <Activity className="h-8 w-8" />,
      items: [
        {
          name: t.workoutIntervals,
          icon: <Activity className="h-5 w-5" />,
          href: getLocalizedLink("workout-intervals"),
        },
        {
          name: t.exerciseTemplates,
          icon: <ListTodo className="h-5 w-5" />,
          href: getLocalizedLink("exercise-templates"),
        },
      ],
    },
    {
      title: t.coreFeatures,
      description: t.coreFeaturesDesc,
      icon: <Smartphone className="h-8 w-8" />,
      items: [
        { name: t.multiLanguage, icon: <Languages className="h-5 w-5" /> },
        { name: t.pwaOffline, icon: <Wifi className="h-5 w-5" /> },
        { name: t.fullscreen, icon: <Smartphone className="h-5 w-5" /> },
        { name: t.soundEffects, icon: <Clock className="h-5 w-5" /> },
      ],
    },
  ]

  return (
    <div className="py-12">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold mb-4">{t.featuresTitle}</h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">{t.featuresDescription}</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {features.map((feature, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            viewport={{ once: true }}
          >
            <Card className="h-full flex flex-col">
              <CardHeader>
                <div className="flex items-center gap-3 mb-2">
                  <div className="p-2 rounded-lg bg-primary/10 text-primary">{feature.icon}</div>
                  <CardTitle>{feature.title}</CardTitle>
                </div>
                <CardDescription>{feature.description}</CardDescription>
              </CardHeader>
              <CardContent className="flex-1">
                <ul className="grid grid-cols-1 gap-3">
                  {feature.items.map((item, i) => (
                    <li key={i}>
                      {'href' in item ? (
                        <Link
                          href={item.href}
                          className="flex items-center gap-2 p-2 rounded-md hover:bg-muted transition-colors"
                        >
                          <div className="text-primary">{item.icon}</div>
                          <span>{item.name}</span>
                        </Link>
                      ) : (
                        <div className="flex items-center gap-2 p-2">
                          <div className="text-primary">{item.icon}</div>
                          <span>{item.name}</span>
                        </div>
                      )}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  )
}
