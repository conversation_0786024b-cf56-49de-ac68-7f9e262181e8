<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cache Test - Timer Kit</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .online { background: #d4edda; color: #155724; }
        .offline { background: #f8d7da; color: #721c24; }
        .cache-info {
            background: #e2e3e5;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .language-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .language-item {
            padding: 5px;
            text-align: center;
            border-radius: 4px;
            font-size: 12px;
        }
        .cached { background: #d4edda; color: #155724; }
        .not-cached { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>PWA Cache Test - Timer Kit</h1>
        
        <div id="connection-status" class="status">
            Checking connection...
        </div>
        
        <div class="cache-info">
            <h3>Cache Information</h3>
            <div id="cache-info">Loading cache information...</div>
        </div>
        
        <div>
            <h3>Test Actions</h3>
            <button onclick="testCacheStatus()">Check Cache Status</button>
            <button onclick="testLanguagePages()">Test Language Pages</button>
            <button onclick="clearCache()">Clear Cache</button>
            <button onclick="enableCacheDebug()">Enable Cache Debug</button>
        </div>
        
        <div class="test-results">
            <h3>Test Results</h3>
            <div id="test-results">Click a test button to see results...</div>
        </div>
        
        <div class="cache-info">
            <h3>Language Cache Status</h3>
            <div id="language-status" class="language-grid">Loading...</div>
        </div>
    </div>

    <script>
        // Check online/offline status
        function updateConnectionStatus() {
            const statusDiv = document.getElementById('connection-status');
            if (navigator.onLine) {
                statusDiv.className = 'status online';
                statusDiv.textContent = '🟢 Online - Full functionality available';
            } else {
                statusDiv.className = 'status offline';
                statusDiv.textContent = '🔴 Offline - Testing cached content';
            }
        }

        // Test cache status
        async function testCacheStatus() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = 'Testing cache status...';
            
            try {
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    const timerKitCache = cacheNames.find(name => name.includes('timerkit-cache'));
                    
                    if (timerKitCache) {
                        const cache = await caches.open(timerKitCache);
                        const cachedRequests = await cache.keys();
                        
                        const pages = cachedRequests
                            .map(req => new URL(req.url).pathname)
                            .filter(path => path.startsWith('/') && !path.includes('/_next/'))
                            .sort();
                        
                        resultsDiv.innerHTML = `
                            <strong>Cache Name:</strong> ${timerKitCache}<br>
                            <strong>Total Cached Items:</strong> ${cachedRequests.length}<br>
                            <strong>Cached Pages:</strong> ${pages.length}<br>
                            <details>
                                <summary>View all cached pages</summary>
                                <pre>${pages.join('\n')}</pre>
                            </details>
                        `;
                    } else {
                        resultsDiv.innerHTML = '❌ No Timer Kit cache found';
                    }
                } else {
                    resultsDiv.innerHTML = '❌ Cache API not supported';
                }
            } catch (error) {
                resultsDiv.innerHTML = `❌ Error checking cache: ${error.message}`;
            }
        }

        // Test language pages
        async function testLanguagePages() {
            const resultsDiv = document.getElementById('test-results');
            const languageStatusDiv = document.getElementById('language-status');
            
            resultsDiv.innerHTML = 'Testing language pages...';
            languageStatusDiv.innerHTML = 'Testing...';
            
            const languages = [
                'en', 'fr', 'es', 'de', 'it', 'pt', 'nl', 'pl', 'uk', 'tr', 'ru', 'ar', 'he', 'fa', 'hi', 'bn',
                'te', 'ta', 'mr', 'gu', 'kn', 'ml', 'pa', 'ur', 'id', 'ms', 'th', 'vi', 'km', 'my', 'zh', 'ja',
                'ko', 'el', 'bg', 'cs', 'sk', 'hu', 'ro', 'hr', 'sr', 'bs', 'sl', 'mk', 'et', 'lv', 'lt', 'da',
                'fi', 'nb', 'sv', 'ca', 'gl', 'eu', 'af', 'sw', 'am', 'ka', 'hy', 'az', 'uz', 'kk', 'tg', 'tk', 'ky'
            ];
            
            const results = [];
            const languageResults = [];
            
            for (const lang of languages) {
                try {
                    const response = await fetch(`/${lang}`, { method: 'HEAD' });
                    const isCached = response.headers.get('x-cache') === 'HIT' || 
                                   (await caches.match(`/${lang}`)) !== undefined;
                    
                    results.push(`${lang}: ${response.ok ? '✅' : '❌'} (${isCached ? 'cached' : 'not cached'})`);
                    languageResults.push({
                        lang,
                        available: response.ok,
                        cached: isCached
                    });
                } catch (error) {
                    results.push(`${lang}: ❌ Error - ${error.message}`);
                    languageResults.push({
                        lang,
                        available: false,
                        cached: false
                    });
                }
            }
            
            resultsDiv.innerHTML = `
                <strong>Language Page Test Results:</strong><br>
                <details>
                    <summary>View all results (${results.length} languages)</summary>
                    <pre>${results.join('\n')}</pre>
                </details>
            `;
            
            // Update language grid
            languageStatusDiv.innerHTML = languageResults.map(result => 
                `<div class="language-item ${result.cached ? 'cached' : 'not-cached'}">
                    ${result.lang}
                    ${result.cached ? '✅' : '❌'}
                </div>`
            ).join('');
        }

        // Clear cache
        async function clearCache() {
            const resultsDiv = document.getElementById('test-results');
            
            try {
                const cacheNames = await caches.keys();
                for (const cacheName of cacheNames) {
                    await caches.delete(cacheName);
                }
                resultsDiv.innerHTML = '✅ All caches cleared successfully';
                
                // Clear localStorage as well
                localStorage.removeItem('cachedLanguages');
                
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } catch (error) {
                resultsDiv.innerHTML = `❌ Error clearing cache: ${error.message}`;
            }
        }

        // Enable cache debug
        function enableCacheDebug() {
            localStorage.setItem('showCacheDebug', 'true');
            document.getElementById('test-results').innerHTML = '✅ Cache debug enabled. Reload the page to see debug info.';
        }

        // Initialize
        updateConnectionStatus();
        testCacheStatus();
        
        // Update connection status when it changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);
        
        // Auto-refresh cache info every 30 seconds
        setInterval(testCacheStatus, 30000);
    </script>
</body>
</html>
