"use client"

import { useEffect, useRef, useState, useCallback } from "react"
import { RobustAudioManager, type SoundType } from "@/components/robust-audio-manager"

// Re-export SoundType for external use
export type { SoundType }

interface UseRobustAudioReturn {
  playSound: (type: SoundType) => Promise<boolean>
  isAudioReady: boolean
  isPlaying: boolean
  hasWakeLock: boolean
  audioState: string
  lastError: string | null
  forceUnlock: () => Promise<boolean>
  cleanup: () => void
}

export function useRobustAudio(): UseRobustAudioReturn {
  const audioManagerRef = useRef<RobustAudioManager | null>(null)
  const [isAudioReady, setIsAudioReady] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [hasWakeLock, setHasWakeLock] = useState(false)
  const [audioState, setAudioState] = useState<string>("unknown")
  const [lastError, setLastError] = useState<string | null>(null)

  // Initialiser le gestionnaire audio
  useEffect(() => {
    if (typeof window === "undefined") return

    console.log("[useRobustAudio] Initializing audio manager")
    audioManagerRef.current = new RobustAudioManager()

    // S'abonner aux changements d'état
    const unsubscribe = audioManagerRef.current.onStateChange((state) => {
      setIsAudioReady(state.isReady)
      setIsPlaying(state.isPlaying)
      setHasWakeLock(state.hasWakeLock)
      setAudioState(state.contextState || "unknown")
      setLastError(state.lastError)
    })

    // Configurer les événements de déverrouillage
    const unlockEvents = ["touchstart", "touchend", "mousedown", "keydown", "click", "focus"]
    
    const handleUserInteraction = async () => {
      if (audioManagerRef.current) {
        console.log("[useRobustAudio] User interaction detected, ensuring audio context")
        await audioManagerRef.current.playSound("notification")
      }
    }

    // Ajouter les écouteurs une seule fois
    unlockEvents.forEach((event) => {
      document.addEventListener(event, handleUserInteraction, { once: true, passive: true })
    })

    // Nettoyer lors du démontage
    return () => {
      console.log("[useRobustAudio] Cleaning up")
      unlockEvents.forEach((event) => {
        document.removeEventListener(event, handleUserInteraction)
      })
      unsubscribe()
      if (audioManagerRef.current) {
        audioManagerRef.current.cleanup()
        audioManagerRef.current = null
      }
    }
  }, [])

  // Fonction pour jouer un son
  const playSound = useCallback(async (type: SoundType): Promise<boolean> => {
    if (!audioManagerRef.current) {
      console.warn("[useRobustAudio] Audio manager not initialized")
      return false
    }

    console.log(`[useRobustAudio] Playing ${type} sound`)
    return await audioManagerRef.current.playSound(type)
  }, [])

  // Fonction pour forcer le déverrouillage
  const forceUnlock = useCallback(async (): Promise<boolean> => {
    if (!audioManagerRef.current) return false

    console.log("[useRobustAudio] Force unlocking audio")
    // Jouer un son silencieux pour débloquer
    return await audioManagerRef.current.playSound("notification")
  }, [])

  // Fonction de nettoyage manuelle
  const cleanup = useCallback(() => {
    if (audioManagerRef.current) {
      audioManagerRef.current.cleanup()
      audioManagerRef.current = null
    }
  }, [])

  return {
    playSound,
    isAudioReady,
    isPlaying,
    hasWakeLock,
    audioState,
    lastError,
    forceUnlock,
    cleanup
  }
}

// Hook spécialisé pour les minuteries
export function useTimerAudio() {
  const robustAudio = useRobustAudio()
  const [soundEnabled, setSoundEnabled] = useState(true)

  const playTimerSound = useCallback(async (type: SoundType = "bell"): Promise<boolean> => {
    if (!soundEnabled) {
      console.log("[useTimerAudio] Sound disabled, skipping")
      return false
    }

    console.log(`[useTimerAudio] Playing timer sound: ${type}`)
    
    // Essayer plusieurs fois si nécessaire
    let success = false
    for (let attempt = 0; attempt < 3 && !success; attempt++) {
      if (attempt > 0) {
        console.log(`[useTimerAudio] Retry attempt ${attempt + 1}`)
        await new Promise(resolve => setTimeout(resolve, 100 * attempt))
      }
      
      success = await robustAudio.playSound(type)
    }

    if (!success) {
      console.error("[useTimerAudio] Failed to play timer sound after all attempts")
    }

    return success
  }, [robustAudio, soundEnabled])

  const toggleSound = useCallback(() => {
    setSoundEnabled(prev => !prev)
    console.log(`[useTimerAudio] Sound ${!soundEnabled ? 'enabled' : 'disabled'}`)
  }, [soundEnabled])

  return {
    ...robustAudio,
    playTimerSound,
    soundEnabled,
    toggleSound
  }
}

// Hook pour la gestion des événements de visibilité
export function useVisibilityAudio() {
  const [isVisible, setIsVisible] = useState(true)
  const [wasHidden, setWasHidden] = useState(false)

  useEffect(() => {
    if (typeof document === "undefined") return

    const handleVisibilityChange = () => {
      const hidden = document.hidden
      setIsVisible(!hidden)
      
      if (hidden) {
        setWasHidden(true)
        console.log("[useVisibilityAudio] Page hidden - audio may need special handling")
      } else if (wasHidden) {
        console.log("[useVisibilityAudio] Page visible again after being hidden")
        // La page redevient visible après avoir été cachée
        // C'est un bon moment pour s'assurer que l'audio fonctionne
      }
    }

    document.addEventListener("visibilitychange", handleVisibilityChange)
    
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange)
    }
  }, [wasHidden])

  return {
    isVisible,
    wasHidden,
    isHidden: !isVisible
  }
}
