"use client"

import { useState, useEffect, useRef } from "react"
import axios from "axios"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { getTranslations } from "@/lib/i18n/translations"
import { Globe, Trash2 } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { memo } from "react" // Import memo
import { ToolControls } from "@/components/tool-controls"
import { ToolFullscreenWrapper } from "@/components/tool-fullscreen-wrapper"

interface TimezoneData {
  datetime: string
  dst: boolean
  timezone: string
  utc_offset: string
}

interface WorldClockProps {
  lang: string
}

interface TimeZone {
  id: string
  city: string
  translated?: boolean
  isLocal?: boolean
  timezone?: string
  utcOffset?: string // Store "+1", "-5" etc.
}

// Utility function to get UTC offset for a timezone
const getUtcOffset = (timezone: string | undefined): number => {
  try {
    if (!timezone) return 0;
    const now = new Date();
    const tzDate = new Date(now.toLocaleString('en-US', { timeZone: timezone }));
    return -tzDate.getTimezoneOffset() / 60;
  } catch (error) {
    console.warn(`Failed to get offset for timezone ${timezone}`);
    return 0;
  }
};

// Ajouter plus de villes à la liste worldCities
const worldCities = [
  // UTC-12 à UTC-8
  {
    city: "Baker Island",
    translations: { fr: "Île Baker", es: "Isla Baker", de: "Baker-Insel", pt: "Ilha Baker", it: "Isola Baker", nl: "Baker", pl: "Baker", uk: "Острів Бейкер", tr: "Baker Adası", ru: "Остров Бейкер", ar: "جزيرة بيكر", he: "איי בייקר", fa: "جزیره بیکر", hi: "बेकर द्वीप", bn: "বেকার দ্বীপ", te: "బేకర్ ద్వీపం", ta: "பேக்கர் தீவு", mr: "बेकर बेट", gu: "બેકર આઇલેન્ડ", kn: "ಬೇಕರ್ ದ್ವೀಪ", ml: "ബേക്കർ ദ്വീപ്", pa: "ਬੇਕਰ ਟਾਪੂ", ur: "بیکر جزیرہ", id: "Pulau Baker", ms: "Pulau Baker", th: "เกาะเบเกอร์", vi: "Đảo Baker", km: "កោះ Baker", my: "ဘေကာကျွန်း", zh: "贝克岛", ja: "ベーカー島", ko: "베이커 섬", el: "Νήσος Μπέικερ", bg: "Остров Бейкър", cs: "Bakerův ostrov", sk: "Bakerov ostrov", hu: "Baker-sziget", ro: "Insula Baker", hr: "Otok Baker", sr: "Бејкер острво", bs: "Ostrvo Baker", sl: "Bakerjev otok", mk: "Бејкерски Остров", et: "Bakeri saar", lv: "Beikera sala", lt: "Beikerio sala", da: "Baker Island", fi: "Bakerinsaari", nb: "Bakerøya", sv: "Bakerön", ca: "Illa Baker", gl: "Illa Baker", eu: "Baker uhartea", af: "Bakereiland", sw: "Kisiwa cha Baker", am: "ቤከር ደሴት", ka: "ბეიკერის კუნძული", hy: "Բեյքեր կղզի", az: "Beyker adası", uz: "Beyker oroli", kk: "Бейкер аралы", tg: "Ҷазираи Бейкер", tk: "Beýker adasy", ky: "Бейкер аралы" },
    timezone: "Pacific/Wake",
  },
  {
    city: "Pago Pago",
    translations: { fr: "Pago Pago", es: "Pago Pago", de: "Pago Pago", pt: "Pago Pago", it: "Pago Pago", nl: "Pago Pago", pl: "Pago Pago", uk: "Паго-Паго", tr: "Pago Pago", ru: "Паго-Паго", ar: "باغو باغو", he: "פאגו פאגו", fa: "پاگو پاگو", hi: "पागो पागो", bn: "পাগো পাগো", te: "పాగో పాగో", ta: "பாகோ பாகோ", mr: "पागो पागो", gu: "પાગો પાગો", kn: "ಪಾಗೊ ಪಾಗೊ", ml: "പാഗോ പാഗോ", pa: "ਪਾਗੋ ਪਾਗੋ", ur: "پاگو پاگو", id: "Pago Pago", ms: "Pago Pago", th: "ปาโกปาโก", vi: "Pago Pago", km: "Pago Pago", my: "ပါဂိုပါဂိုမြို့", zh: "帕果帕果", ja: "パゴパゴ", ko: "파고파고", el: "Πάγκο Πάγκο", bg: "Паго Паго", cs: "Pago Pago", sk: "Pago Pago", hu: "Pago Pago", ro: "Pago Pago", hr: "Pago Pago", sr: "Паго Паго", bs: "Pago Pago", sl: "Pago Pago", mk: "Паго Паго", et: "Pago Pago", lv: "Pago Pago", lt: "Pago Pago", da: "Pago Pago", fi: "Pago Pago", nb: "Pago Pago", sv: "Pago Pago", ca: "Pago Pago", gl: "Pago Pago", eu: "Pago Pago", af: "Pago Pago", sw: "Pago Pago", am: "ፓጎ ፓጎ", ka: "პაგო-პაგო", hy: "Պագո Պագո", az: "Paqo Paqo", uz: "Pago Pago", kk: "Паго-Паго", tg: "Паго Паго", tk: "Pago Pago", ky: "Паго-Паго" },
    timezone: "Pacific/Pago_Pago",
  },
  {
    city: "Honolulu",
    translations: { fr: "Honolulu", es: "Honolulu", de: "Honolulu", pt: "Honolulu", it: "Honolulu", nl: "Honolulu", pl: "Honolulu", uk: "Гонолулу", tr: "Honolulu", ru: "Гонолулу", ar: "هونولولو", he: "הונולולו", fa: "هونولولو", hi: "होनोलूलू", bn: "হনলুলু", te: "హోనోలులు", ta: "ஹொனலுலு", mr: "होनोलुलु", gu: "હોનોલુલુ", kn: "ಹೊನೊಲುಲು", ml: "ഹോണോലുലു", pa: "ਹੋਨੋਲੂਲੂ", ur: "ہونولولو", id: "Honolulu", ms: "Honolulu", th: "โฮโนลูลู", vi: "Honolulu", km: "ហូណូលូលូ", my: "ဟိုနိုလူလူမြို့", zh: "火奴鲁鲁", ja: "ホノルル", ko: "호놀룰루", el: "Χονολουλού", bg: "Хонолулу", cs: "Honolulu", sk: "Honolulu", hu: "Honolulu", ro: "Honolulu", hr: "Honolulu", sr: "Хонолулу", bs: "Honolulu", sl: "Honolulu", mk: "Хонолулу", et: "Honolulu", lv: "Honolulu", lt: "Honolulu", da: "Honolulu", fi: "Honolulu", nb: "Honolulu", sv: "Honolulu", ca: "Honolulu", gl: "Honolulu", eu: "Honolulu", af: "Honolulu", sw: "Honolulu", am: "ሆኖሉሉ", ka: "ჰონოლულუ", hy: "Հոնոլուլու", az: "Honolulu", uz: "Gonolulu", kk: "Гонолулу", tg: "Гонолулу", tk: "Gonolulu", ky: "Гонолулу" },
    timezone: "Pacific/Honolulu",
  },
  {
    city: "Marquesas Islands",
    translations: { fr: "Îles Marquises", es: "Islas Marquesas", de: "Marquesas-Inseln", pt: "Ilhas Marquesas", it: "Isole Marchesi", nl: "Marquesaseilanden", pl: "Markizy", uk: "Маркізькі острови", tr: "Markiz Adaları", ru: "Маркизские острова", ar: "جزر ماركيساس", he: "איי מרקיז", fa: "جزایر مارکیز", hi: "मार्केसास द्वीप समूह", bn: "মার্কেসাস দ্বীপপুঞ্জ", te: "మార్క్వేసాస్ దీవులు", ta: "மார்க்கெசசுத் தீவுகள்", mr: "मार्केसास बेटे", gu: "માર્ક્વેસાસ ટાપુઓ", kn: "ಮಾರ್ಕ್ವೆಸಾಸ್ ದ್ವೀಪಗಳು", ml: "മാർക്വേസസ് ദ്വീപുകൾ", pa: "ਮਾਰਕੇਸਾਸ ਟਾਪੂ", ur: "مارکیز جزائر", id: "Kepulauan Marquesas", ms: "Kepulauan Marquesas", th: "หมู่เกาะมาร์เคซัส", vi: "Quần đảo Marquises", km: "កោះ Marquesas", my: "မာကေးဆပ်ကျွန်းစု", zh: "马克萨斯群岛", ja: "マルキーズ諸島", ko: "마르키즈 제도", el: "Νήσοι Μαρκέζας", bg: "Маркизки острови", cs: "Markézy", sk: "Markézy", hu: "Marquises-szigetek", ro: "Insulele Marchize", hr: "Markižansko otočje", sr: "Маркиска острва", bs: "Ostrva Marquesas", sl: "Markizini otoki", mk: "Маркиски Острови", et: "Markiisaared", lv: "Marķīza salas", lt: "Markizų salos", da: "Marquesasøerne", fi: "Marquesassaaret", nb: "Marquesasøyene", sv: "Marquesasöarna", ca: "Illes Marqueses", gl: "Illas Marquesas", eu: "Markesak uharteak", af: "Marquesaseilande", sw: "Visiwa vya Marquesas", am: "ማርኬሳስ ደሴቶች", ka: "მარკიზის კუნძულები", hy: "Մարկիզյան կղզիներ", az: "Markiz adaları", uz: "Markiz orollari", kk: "Маркиз аралдары", tg: "Ҷазираҳои Маркиз", tk: "Markiz adalary", ky: "Маркиз аралдары" },
    timezone: "Pacific/Marquesas",
  },
  {
    city: "Anchorage",
    translations: { fr: "Anchorage", es: "Anchorage", de: "Anchorage", pt: "Anchorage", it: "Anchorage", nl: "Anchorage", pl: "Anchorage", uk: "Анкоридж", tr: "Anchorage", ru: "Анкоридж", ar: "أنكوريج", he: "אנקורג'", fa: "انکوریج", hi: "एंकरेज", bn: "অ্যাঙ্কোরেজ", te: "యాంకోరేజ్", ta: "ஆங்கரேஜ்", mr: "अँकरेज", gu: "એન્કોરેજ", kn: "ಆಂಕಾರೇಜ್", ml: "ആങ്കറേജ്", pa: "ਐਂਕਰੇਜ", ur: "اینکرایج", id: "Anchorage", ms: "Anchorage", th: "แองเคอเรจ", vi: "Anchorage", km: "Anchorage", my: "အန်ကာရေ့ခ်ျမြို့", zh: "安克雷奇", ja: "アンカレッジ", ko: "앵커리지", el: "Άνκορατζ", bg: "Анкоридж", cs: "Anchorage", sk: "Anchorage", hu: "Anchorage", ro: "Anchorage", hr: "Anchorage", sr: "Енкориџ", bs: "Anchorage", sl: "Anchorage", mk: "Енкориџ", et: "Anchorage", lv: "Ankoridža", lt: "Ankoridžas", da: "Anchorage", fi: "Anchorage", nb: "Anchorage", sv: "Anchorage", ca: "Anchorage", gl: "Anchorage", eu: "Anchorage", af: "Anchorage", sw: "Anchorage", am: "አንኮሬጅ", ka: "ანკორიჯი", hy: "Անքորեջ", az: "Ankoric", uz: "Ankorij", kk: "Анкоридж", tg: "Анкоридж", tk: "Ankoridž", ky: "Анкоридж" },
    timezone: "America/Anchorage",
  },
  { city: "Juneau", translations: { fr: "Juneau", es: "Juneau", de: "Juneau", pt: "Juneau", it: "Juneau", nl: "Juneau", pl: "Juneau", uk: "Джуно", tr: "Juneau", ru: "Джуно", ar: "جونو", he: "ג'ונו", fa: "جونو", hi: "जूनो", bn: "জুনো", te: "జునో", ta: "ஜூனோ", mr: "जुनो", gu: "જુનો", kn: "ಜುನೊ", ml: "ജൂനോ", pa: "ਜੂਨੋ", ur: "جونو", id: "Juneau", ms: "Juneau", th: "จูโน", vi: "Juneau", km: "Juneau", my: "ဂျူနိုမြို့", zh: "朱诺", ja: "ジュノー", ko: "주노", el: "Τζούνο", bg: "Джуно", cs: "Juneau", sk: "Juneau", hu: "Juneau", ro: "Juneau", hr: "Juneau", sr: "Џуно", bs: "Juneau", sl: "Juneau", mk: "Џуно", et: "Juneau", lv: "Džūno", lt: "Džunas", da: "Juneau", fi: "Juneau", nb: "Juneau", sv: "Juneau", ca: "Juneau", gl: "Juneau", eu: "Juneau", af: "Juneau", sw: "Juneau", am: "ጁኖ", ka: "ჯუნო", hy: "Ջունո", az: "Cuno", uz: "Juno", kk: "Джуно", tg: "Ҷуно", tk: "Žuno", ky: "Жуно" }, timezone: "America/Juneau" },
  {
    city: "Los Angeles",
    translations: { fr: "Los Angeles", es: "Los Ángeles", de: "Los Angeles", pt: "Los Angeles", it: "Los Angeles", nl: "Los Angeles", pl: "Los Angeles", uk: "Лос-Анджелес", tr: "Los Angeles", ru: "Лос-Анджелес", ar: "لوس أنجلوس", he: "לוס אנג'לס", fa: "لس آنجلس", hi: "लॉस एंजेलिस", bn: "লস অ্যাঞ্জেলেস", te: "లాస్ ఏంజిల్స్", ta: "லாஸ் ஏஞ்சலஸ்", mr: "लॉस एंजेलस", gu: "લોસ એન્જલસ", kn: "ಲಾಸ್ ಏಂಜಲೀಸ್", ml: "ലോസ് ഏഞ്ചൽസ്", pa: "ਲਾਸ ਏਂਜਲਸ", ur: "لاس اینجلس", id: "Los Angeles", ms: "Los Angeles", th: "ลอสแอนเจลิส", vi: "Los Angeles", km: "ឡូស​អាន់​ជ័រ​លេស", my: "လော့စ်အိန်ဂျယ်လိစ်မြို့", zh: "洛杉矶", ja: "ロサンゼルス", ko: "로스앤젤레스", el: "Λος Άντζελες", bg: "Лос Анджелис", cs: "Los Angeles", sk: "Los Angeles", hu: "Los Angeles", ro: "Los Angeles", hr: "Los Angeles", sr: "Лос Анђелес", bs: "Los Angeles", sl: "Los Angeles", mk: "Лос Анџелес", et: "Los Angeles", lv: "Losandželosa", lt: "Los Andželas", da: "Los Angeles", fi: "Los Angeles", nb: "Los Angeles", sv: "Los Angeles", ca: "Los Angeles", gl: "Os Ánxeles", eu: "Los Angeles", af: "Los Angeles", sw: "Los Angeles", am: "ሎስ አንጀለስ", ka: "ლოს-ანჯელესი", hy: "Լոս Անջելես", az: "Los-Anceles", uz: "Los Anjeles", kk: "Лос-Анджелес", tg: "Лос Анҷелес", tk: "Los-Anjeles", ky: "Лос-Анжелес" },
    timezone: "America/Los_Angeles",
  },
  {
    city: "Vancouver",
    translations: { fr: "Vancouver", es: "Vancouver", de: "Vancouver", pt: "Vancouver", it: "Vancouver", nl: "Vancouver", pl: "Vancouver", uk: "Ванкувер", tr: "Vancouver", ru: "Ванкувер", ar: "فانكوفر", he: "ונקובר", fa: "ونکوور", hi: "वैंकूवर", bn: "ভ্যানকুভার", te: "వాంకోవర్", ta: "வான்சுவர்", mr: "व्हँकुव्हर", gu: "વેનકુવર", kn: "ವ್ಯಾಂಕೋವರ್", ml: "വാൻകൂവർ", pa: "ਵੈਨਕੂਵਰ", ur: "وینکوور", id: "Vancouver", ms: "Vancouver", th: "แวนคูเวอร์", vi: "Vancouver", km: "Vancouver", my: "ဗန်ကူးဗားမြို့", zh: "温哥华", ja: "バンクーバー", ko: "밴쿠버", el: "Βανκούβερ", bg: "Ванкувър", cs: "Vancouver", sk: "Vancouver", hu: "Vancouver", ro: "Vancouver", hr: "Vancouver", sr: "Ванкувер", bs: "Vancouver", sl: "Vancouver", mk: "Ванкувер", et: "Vancouver", lv: "Vankūvera", lt: "Vankuveris", da: "Vancouver", fi: "Vancouver", nb: "Vancouver", sv: "Vancouver", ca: "Vancouver", gl: "Vancouver", eu: "Vancouver", af: "Vancouver", sw: "Vancouver", am: "ቫንኩቨር", ka: "ვანკუვერი", hy: "Վանկուվեր", az: "Vankuver", uz: "Vankuver", kk: "Ванкувер", tg: "Ванкувер", tk: "Wankuwer", ky: "Ванкувер" },
    timezone: "America/Vancouver",
  },
  {
    city: "San Francisco",
    translations: { fr: "San Francisco", es: "San Francisco", de: "San Francisco", pt: "São Francisco", it: "San Francisco", nl: "San Francisco", pl: "San Francisco", uk: "Сан-Франциско", tr: "San Francisco", ru: "Сан-Франциско", ar: "سان فرانسيسكو", he: "סן פרנסיסקו", fa: "سان فرانسیسکو", hi: "सैन फ्रांसिस्को", bn: "সান ফ্রান্সিস্কো", te: "శాన్ ఫ్రాన్సిస్కో", ta: "சான் பிரான்சிஸ்கோ", mr: "सॅन फ्रान्सिस्को", gu: "સાન ફ્રાન્સિસ્કો", kn: "ಸ್ಯಾನ್ ಫ್ರಾನ್ಸಿಸ್ಕೋ", ml: "സാൻ ഫ്രാൻസിസ്കോ", pa: "ਸਾਨ ਫਰਾਂਸਿਸਕੋ", ur: "سان فرانسسکو", id: "San Francisco", ms: "San Francisco", th: "ซานฟรานซิสโก", vi: "San Francisco", km: "សាន់ហ្វ្រាន់ស៊ីស្កូ", my: "ဆန်ဖရန်စစ္စကိုမြို့", zh: "旧金山", ja: "サンフランシスコ", ko: "샌프란시스코", el: "Σαν Φρανσίσκο", bg: "Сан Франциско", cs: "San Francisco", sk: "San Francisco", hu: "San Francisco", ro: "San Francisco", hr: "San Francisco", sr: "Сан Франциско", bs: "San Francisco", sl: "San Francisco", mk: "Сан Франциско", et: "San Francisco", lv: "Sanfrancisko", lt: "San Fransiskas", da: "San Francisco", fi: "San Francisco", nb: "San Francisco", sv: "San Francisco", ca: "San Francisco", gl: "San Francisco", eu: "San Francisco", af: "San Francisco", sw: "San Francisco", am: "ሳን ፍራንሲስኮ", ka: "სან-ფრანცისკო", hy: "Սան Ֆրանցիսկո", az: "San-Fransisko", uz: "San Fransisko", kk: "Сан-Франциско", tg: "Сан Франсиско", tk: "San-Fransisko", ky: "Сан-Франциско" },
    timezone: "America/Los_Angeles",
  },
  { city: "Seattle", translations: { fr: "Seattle", es: "Seattle", de: "Seattle", pt: "Seattle", it: "Seattle", nl: "Seattle", pl: "Seattle", uk: "Сіетл", tr: "Seattle", ru: "Сиэтл", ar: "سياتل", he: "סיאטל", fa: "سیاتل", hi: "सिएटल", bn: "সিয়াটল", te: "సియాటెల్", ta: "சியாட்டில்", mr: "सिॲटल", gu: "સિએટલ", kn: "ಸಿಯಾಟಲ್", ml: "സിയാറ്റിൽ", pa: "ਸੀਐਟਲ", ur: "سیئٹل", id: "Seattle", ms: "Seattle", th: "ซีแอตเทิล", vi: "Seattle", km: "ស៊ីថល", my: "ဆီအက်တယ်မြို့", zh: "西雅图", ja: "シアトル", ko: "시애틀", el: "Σιάτλ", bg: "Сиатъл", cs: "Seattle", sk: "Seattle", hu: "Seattle", ro: "Seattle", hr: "Seattle", sr: "Сијетл", bs: "Seattle", sl: "Seattle", mk: "Сиетл", et: "Seattle", lv: "Sietla", lt: "Sietlas", da: "Seattle", fi: "Seattle", nb: "Seattle", sv: "Seattle", ca: "Seattle", gl: "Seattle", eu: "Seattle", af: "Seattle", sw: "Seattle", am: "ሲያትል", ka: "სიეტლი", hy: "Սիեթլ", az: "Sietl", uz: "Sietl", kk: "Сиэтл", tg: "Сиэтл", tk: "Sietl", ky: "Сиэтл" }, timezone: "America/Los_Angeles" },
  { city: "Tijuana", translations: { fr: "Tijuana", es: "Tijuana", de: "Tijuana", pt: "Tijuana", it: "Tijuana", nl: "Tijuana", pl: "Tijuana", uk: "Тіхуана", tr: "Tijuana", ru: "Тихуана", ar: "تيخوانا", he: "טיחואנה", fa: "تیخوانا", hi: "तिहुआना", bn: "তিহুয়ানা", te: "టిజువానా", ta: "தியுவானா", mr: "तिहुआना", gu: "તિજુઆના", kn: "ಟಿಜುವಾನಾ", ml: "ടിജുവാന", pa: "ਤਿਜੁਆਨਾ", ur: "تیخوانا", id: "Tijuana", ms: "Tijuana", th: "ตีฮัวนา", vi: "Tijuana", km: "Tijuana", my: "တီဟွာနာမြို့", zh: "蒂华纳", ja: "ティフアナ", ko: "티후아나", el: "Τιχουάνα", bg: "Тихуана", cs: "Tijuana", sk: "Tijuana", hu: "Tijuana", ro: "Tijuana", hr: "Tijuana", sr: "Тихуана", bs: "Tijuana", sl: "Tijuana", mk: "Тихуана", et: "Tijuana", lv: "Tihuana", lt: "Tichuana", da: "Tijuana", fi: "Tijuana", nb: "Tijuana", sv: "Tijuana", ca: "Tijuana", gl: "Tijuana", eu: "Tijuana", af: "Tijuana", sw: "Tijuana", am: "ቲዋና", ka: "ტიხუანა", hy: "Տիխուանա", az: "Tixuana", uz: "Tixuana", kk: "Тихуана", tg: "Тихуана", tk: "Tihuana", ky: "Тихуана" }, timezone: "America/Tijuana" },

  // UTC-7 à UTC-5
  { city: "Denver", translations: { fr: "Denver", es: "Denver", de: "Denver", pt: "Denver", it: "Denver", nl: "Denver", pl: "Denver", uk: "Денвер", tr: "Denver", ru: "Денвер", ar: "دنفر", he: "דנוור", fa: "دنور", hi: "डेनवर", bn: "ডেনভার", te: "డెన్వర్", ta: "டென்வர்", mr: "डेन्व्हर", gu: "ડેન્વર", kn: "ಡೆನ್ವರ್", ml: "ഡെൻവർ", pa: "ਡੈਨਵਰ", ur: "ڈینور", id: "Denver", ms: "Denver", th: "เดนเวอร์", vi: "Denver", km: "Denver", my: "ဒန်ဗာမြို့", zh: "丹佛", ja: "デンバー", ko: "덴버", el: "Ντένβερ", bg: "Денвър", cs: "Denver", sk: "Denver", hu: "Denver", ro: "Denver", hr: "Denver", sr: "Денвер", bs: "Denver", sl: "Denver", mk: "Денвер", et: "Denver", lv: "Denvera", lt: "Denveris", da: "Denver", fi: "Denver", nb: "Denver", sv: "Denver", ca: "Denver", gl: "Denver", eu: "Denver", af: "Denver", sw: "Denver", am: "ዴንቨር", ka: "დენვერი", hy: "Դենվեր", az: "Denver", uz: "Denver", kk: "Денвер", tg: "Денвер", tk: "Denver", ky: "Денвер" }, timezone: "America/Denver" },
  { city: "Phoenix", translations: { fr: "Phoenix", es: "Phoenix", de: "Phoenix", pt: "Phoenix", it: "Phoenix", nl: "Phoenix", pl: "Phoenix", uk: "Фінікс", tr: "Phoenix", ru: "Финикс", ar: "فينيكس", he: "פיניקס", fa: "فینیکس", hi: "फीनिक्स", bn: "ফিনিক্স", te: "ఫీనిక్స్", ta: "பீனிக்ஸ்", mr: "फिनिक्स", gu: "ફિનિક્સ", kn: "ಫೀನಿಕ್ಸ್", ml: "ഫീനിക്സ്", pa: "ਫੀਨਿਕਸ", ur: "فینکس", id: "Phoenix", ms: "Phoenix", th: "ฟีนิกซ์", vi: "Phoenix", km: "Phoenix", my: "ဖီးနစ်မြို့", zh: "菲尼克斯", ja: "フェニックス", ko: "피닉스", el: "Φοίνιξ", bg: "Финикс", cs: "Phoenix", sk: "Phoenix", hu: "Phoenix", ro: "Phoenix", hr: "Phoenix", sr: "Финикс", bs: "Phoenix", sl: "Phoenix", mk: "Феникс", et: "Phoenix", lv: "Fīniksa", lt: "Finiksas", da: "Phoenix", fi: "Phoenix", nb: "Phoenix", sv: "Phoenix", ca: "Phoenix", gl: "Phoenix", eu: "Phoenix", af: "Phoenix", sw: "Phoenix", am: "ፊኒክስ", ka: "ფინიქსი", hy: "Ֆինիքս", az: "Finiks", uz: "Feniks", kk: "Финикс", tg: "Финикс", tk: "Feniks", ky: "Финикс" }, timezone: "America/Phoenix" },
  { city: "Calgary", translations: { fr: "Calgary", es: "Calgary", de: "Calgary", pt: "Calgary", it: "Calgary", nl: "Calgary", pl: "Calgary", uk: "Калгарі", tr: "Calgary", ru: "Калгари", ar: "كالغاري", he: "קלגרי", fa: "کلگری", hi: "कैलगरी", bn: "ক্যালগারি", te: "కాల్గరీ", ta: "கால்கரி", mr: "कॅल्गारी", gu: "કેલગરી", kn: "ಕ್ಯಾಲ್ಗರಿ", ml: "കാൽഗറി", pa: "ਕੈਲਗਰੀ", ur: "کیلگری", id: "Calgary", ms: "Calgary", th: "แคลกะรี", vi: "Calgary", km: "Calgary", my: "ကယ်လ်ဂရီမြို့", zh: "卡尔加里", ja: "カルガリー", ko: "캘거리", el: "Κάλγκαρι", bg: "Калгари", cs: "Calgary", sk: "Calgary", hu: "Calgary", ro: "Calgary", hr: "Calgary", sr: "Калгари", bs: "Calgary", sl: "Calgary", mk: "Калгари", et: "Calgary", lv: "Kalgari", lt: "Kalgaris", da: "Calgary", fi: "Calgary", nb: "Calgary", sv: "Calgary", ca: "Calgary", gl: "Calgary", eu: "Calgary", af: "Calgary", sw: "Calgary", am: "ካልጋሪ", ka: "კალგარი", hy: "Կալգարի", az: "Kalqari", uz: "Kalgari", kk: "Калгари", tg: "Калгари", tk: "Kalgari", ky: "Калгари" }, timezone: "America/Edmonton" },
  { city: "Edmonton", translations: { fr: "Edmonton", es: "Edmonton", de: "Edmonton", pt: "Edmonton", it: "Edmonton", nl: "Edmonton", pl: "Edmonton", uk: "Едмонтон", tr: "Edmonton", ru: "Эдмонтон", ar: "إدمونتون", he: "אדמונטון", fa: "ادمونتون", hi: "एडमोंटन", bn: "এডমন্টন", te: "ఎడ్మంటన్", ta: "எட்மன்டன்", mr: "एडमंटन", gu: "એડમોન્ટોન", kn: "ಎಡ್ಮಂಟನ್", ml: "എഡ്മന്റൺ", pa: "ਐਡਮਿੰਟਨ", ur: "ایڈمنٹن", id: "Edmonton", ms: "Edmonton", th: "เอดมันตัน", vi: "Edmonton", km: "Edmonton", my: "အက်ဒမန်တန်မြို့", zh: "埃德蒙顿", ja: "エドモントン", ko: "에드먼턴", el: "Έντμοντον", bg: "Едмънтън", cs: "Edmonton", sk: "Edmonton", hu: "Edmonton", ro: "Edmonton", hr: "Edmonton", sr: "Едмонтон", bs: "Edmonton", sl: "Edmonton", mk: "Едмонтон", et: "Edmonton", lv: "Edmontona", lt: "Edmontonas", da: "Edmonton", fi: "Edmonton", nb: "Edmonton", sv: "Edmonton", ca: "Edmonton", gl: "Edmonton", eu: "Edmonton", af: "Edmonton", sw: "Edmonton", am: "ኤድመንተን", ka: "ედმონტონი", hy: "Էդմոնտոն", az: "Edmonton", uz: "Edmonton", kk: "Эдмонтон", tg: "Эдмонтон", tk: "Edmonton", ky: "Эдмонтон" }, timezone: "America/Edmonton" },
  {
    city: "Salt Lake City",
    translations: { fr: "Salt Lake City", es: "Salt Lake City", de: "Salt Lake City", pt: "Salt Lake City", it: "Salt Lake City", nl: "Salt Lake City", pl: "Salt Lake City", uk: "Солт-Лейк-Сіті", tr: "Salt Lake City", ru: "Солт-Лейк-Сити", ar: "سولت ليك سيتي", he: "סולט לייק סיטי", fa: "سالت لیک سیتی", hi: "सॉल्ट लेक सिटी", bn: "সল্ট লেক সিটি", te: "సాల్ట్ లేక్ సిటీ", ta: "சால்ட் லேக் நகரம்", mr: "सॉल्ट लेक सिटी", gu: "સોલ્ટ લેક સિટી", kn: "ಸಾಲ್ಟ್ ಲೇಕ್ ಸಿಟಿ", ml: "സാൾട്ട് ലേക്ക് സിറ്റി", pa: "ਸਾਲਟ ਲੇਕ ਸਿਟੀ", ur: "سالٹ لیک سٹی", id: "Salt Lake City", ms: "Salt Lake City", th: "ซอลต์เลกซิตี", vi: "Thành phố Salt Lake", km: "Salt Lake City", my: "ဆော့လ်တ်လိတ်စီးတီးမြို့", zh: "盐湖城", ja: "ソルトレイクシティ", ko: "솔트레이크시티", el: "Σολτ Λέικ Σίτι", bg: "Солт Лейк Сити", cs: "Salt Lake City", sk: "Salt Lake City", hu: "Salt Lake City", ro: "Salt Lake City", hr: "Salt Lake City", sr: "Солт Лејк Сити", bs: "Salt Lake City", sl: "Salt Lake City", mk: "Солт Лејк Сити", et: "Salt Lake City", lv: "Soltleiksitija", lt: "Solt Leik Sitis", da: "Salt Lake City", fi: "Salt Lake City", nb: "Salt Lake City", sv: "Salt Lake City", ca: "Salt Lake City", gl: "Salt Lake City", eu: "Salt Lake City", af: "Salt Lake City", sw: "Salt Lake City", am: "ሶልት ሌክ ሲቲ", ka: "სოლტ-ლეიკ-სიტი", hy: "Սոլթ Լեյք Սիթի", az: "Solt-Leyk-Siti", uz: "Solt Leyk Siti", kk: "Солт-Лейк-Сити", tg: "Солт Лейк Сити", tk: "Solt-Leýk-Siti", ky: "Солт-Лейк-Сити" },
    timezone: "America/Denver",
  },


  { city: "Chicago", translations: { fr: "Chicago", es: "Chicago", de: "Chicago", pt: "Chicago", it: "Chicago", nl: "Chicago", pl: "Chicago", uk: "Чикаго", tr: "Chicago", ru: "Чикаго", ar: "شيكاغو", he: "שיקגו", fa: "شیکاگو", hi: "शिकागो", bn: "শিকাগো", te: "చికాగో", ta: "சிகாகோ", mr: "शिकागो", gu: "શિકાગો", kn: "ಚಿಕಾಗೊ", ml: "ഷിക്കാഗോ", pa: "ਸ਼ਿਕਾਗੋ", ur: "شکاگو", id: "Chicago", ms: "Chicago", th: "ชิคาโก", vi: "Chicago", km: "ឈីកាហ្គោ", my: "ရှီကာဂို", zh: "芝加哥", ja: "シカゴ", ko: "시카고", el: "Σικάγο", bg: "Чикаго", cs: "Chicago", sk: "Chicago", hu: "Chicago", ro: "Chicago", hr: "Chicago", sr: "Чикаго", bs: "Chicago", sl: "Chicago", mk: "Чикаго", et: "Chicago", lv: "Čikāga", lt: "Čikaga", da: "Chicago", fi: "Chicago", nb: "Chicago", sv: "Chicago", ca: "Chicago", gl: "Chicago", eu: "Chicago", af: "Chicago", sw: "Chicago", am: "ቺካጎ", ka: "ჩიკაგო", hy: "Չիկագո", az: "Çikaqo", uz: "Chikago", kk: "Чикаго", tg: "Чикаго", tk: "Çikago", ky: "Чикаго" }, timezone: "America/Chicago" },
  { city: "Mexico City", translations: { fr: "Mexico", es: "Ciudad de México", de: "Mexiko-Stadt", pt: "Cidade do México", it: "Città del Messico", nl: "Mexico-Stad", pl: "Meksyk (miasto)", uk: "Мехіко", tr: "Meksiko", ru: "Мехико", ar: "مدينة مكسيكو", he: "מקסיקו סיטי", fa: "مکزیکو سیتی", hi: "मेक्सिको नगर", bn: "মেক্সিকো সিটি", te: "మెక్సికో నగరం", ta: "மெக்சிகோ நகரம்", mr: "मेक्सिको सिटी", gu: "મેક્સિકો સિટી", kn: "ಮೆಕ್ಸಿಕೋ ನಗರ", ml: "മെക്സിക്കോ സിറ്റി", pa: "ਮੈਕਸੀਕੋ ਸ਼ਹਿਰ", ur: "میکسیکو شہر", id: "Kota Meksiko", ms: "Bandar Raya Mexico", th: "เม็กซิโกซิตี", vi: "Thành phố México", km: "ទីក្រុងម៉ិកស៊ិក", my: "မက္ကဆီကိုမြို့တော်", zh: "墨西哥城", ja: "メキシコシティ", ko: "멕시코시티", el: "Πόλη του Μεξικού", bg: "Мексико Сити", cs: "Ciudad de México", sk: "Mexiko (mesto)", hu: "Mexikóváros", ro: "Ciudad de México", hr: "Ciudad de México", sr: "Мексико Сити", bs: "Mexico City", sl: "Ciudad de México", mk: "Мексико Сити", et: "México", lv: "Mehiko", lt: "Meksikas", da: "Mexico City", fi: "México", nb: "Mexico by", sv: "Mexico City", ca: "Ciutat de Mèxic", gl: "Cidade de México", eu: "Mexiko Hiria", af: "Meksikostad", sw: "Mexico City", am: "ሜክሲኮ ከተማ", ka: "მეხიკო", hy: "Մեխիկո", az: "Mexiko", uz: "Mexiko shahri", kk: "Мехико", tg: "Мехико", tk: "Mexiko şäheri", ky: "Мехико" }, timezone: "America/Mexico_City" },
  { city: "Dallas", translations: { fr: "Dallas", es: "Dallas", de: "Dallas", pt: "Dallas", it: "Dallas", nl: "Dallas", pl: "Dallas", uk: "Даллас", tr: "Dallas", ru: "Даллас", ar: "دالاس", he: "דאלאס", fa: "دالاس", hi: "डैलस", bn: "ডালাস", te: "డల్లాస్", ta: "டாலஸ்", mr: "डॅलस", gu: "ડલ્લાસ", kn: "ಡಲ್ಲಾಸ್", ml: "ഡാളസ്", pa: "ਡੈਲਸ", ur: "ڈیلاس", id: "Dallas", ms: "Dallas", th: "แดลลัส", vi: "Dallas", km: "ដាឡាស", my: "ဒဲလပ်စ်မြို့", zh: "达拉斯", ja: "ダラス", ko: "댈러스", el: "Ντάλας", bg: "Далас", cs: "Dallas", sk: "Dallas", hu: "Dallas", ro: "Dallas", hr: "Dallas", sr: "Далас", bs: "Dallas", sl: "Dallas", mk: "Далас", et: "Dallas", lv: "Dalasa", lt: "Dalasas", da: "Dallas", fi: "Dallas", nb: "Dallas", sv: "Dallas", ca: "Dallas", gl: "Dallas", eu: "Dallas", af: "Dallas", sw: "Dallas", am: "ዳላስ", ka: "დალასი", hy: "Դալաս", az: "Dallas", uz: "Dallas", kk: "Даллас", tg: "Даллас", tk: "Dallas", ky: "Даллас" }, timezone: "America/Chicago" },
  { city: "Houston", translations: { fr: "Houston", es: "Houston", de: "Houston", pt: "Houston", it: "Houston", nl: "Houston", pl: "Houston", uk: "Х'юстон", tr: "Houston", ru: "Хьюстон", ar: "هيوستن", he: "יוסטון", fa: "هیوستون", hi: "ह्यूस्टन", bn: "হিউস্টন", te: "హ్యూస్టన్", ta: "ஹியூஸ்டன்", mr: "ह्युस्टन", gu: "હ્યુસ્ટન", kn: "ಹೂಸ್ಟನ್", ml: "ഹ്യൂസ്റ്റൺ", pa: "ਹਿਊਸਟਨ", ur: "ہیوسٹن", id: "Houston", ms: "Houston", th: "ฮิวสตัน", vi: "Houston", km: "ហ៊ូស្តុន", my: "ဟιούစတန်မြို့", zh: "休斯顿", ja: "ヒューストン", ko: "휴스턴", el: "Χιούστον", bg: "Хюстън", cs: "Houston", sk: "Houston", hu: "Houston", ro: "Houston", hr: "Houston", sr: "Хјустон", bs: "Houston", sl: "Houston", mk: "Хјустон", et: "Houston", lv: "Hjūstona", lt: "Hiustonas", da: "Houston", fi: "Houston", nb: "Houston", sv: "Houston", ca: "Houston", gl: "Houston", eu: "Houston", af: "Houston", sw: "Houston", am: "ሂውስተን", ka: "ჰიუსტონი", hy: "Հյուստոն", az: "Hyuston", uz: "Xyuston", kk: "Хьюстон", tg: "Хюстон", tk: "Hýuston", ky: "Хьюстон" }, timezone: "America/Chicago" },
  { city: "Winnipeg", translations: { fr: "Winnipeg", es: "Winnipeg", de: "Winnipeg", pt: "Winnipeg", it: "Winnipeg", nl: "Winnipeg", pl: "Winnipeg", uk: "Вінніпег", tr: "Winnipeg", ru: "Виннипег", ar: "وينيبيغ", he: "ויניפג", fa: "وینیپگ", hi: "विनिपेग", bn: "উইনিপেগ", te: "విన్నిపెగ్", ta: "வினிப்பெக்", mr: "विनिपेग", gu: "વિનીપેગ", kn: "ವಿನ್ನಿಪೆಗ್", ml: "വിന്നിപെഗ്", pa: "ਵਿਨੀਪੈਗ", ur: "ونی پیگ", id: "Winnipeg", ms: "Winnipeg", th: "วินนิเพก", vi: "Winnipeg", km: "វីនីប៉ិច", my: "ဝင်းနီပက်မြို့", zh: "温尼伯", ja: "ウィニペグ", ko: "위니펙", el: "Γουίνιπεγκ", bg: "Уинипег", cs: "Winnipeg", sk: "Winnipeg", hu: "Winnipeg", ro: "Winnipeg", hr: "Winnipeg", sr: "Винипег", bs: "Winnipeg", sl: "Winnipeg", mk: "Винипег", et: "Winnipeg", lv: "Vinipega", lt: "Vinipegas", da: "Winnipeg", fi: "Winnipeg", nb: "Winnipeg", sv: "Winnipeg", ca: "Winnipeg", gl: "Winnipeg", eu: "Winnipeg", af: "Winnipeg", sw: "Winnipeg", am: "ዊኒፔግ", ka: "უინიპეგი", hy: "Վինիպեգ", az: "Vinnipeq", uz: "Vinnipeg", kk: "Виннипег", tg: "Виннипег", tk: "Winnipeg", ky: "Виннипег" }, timezone: "America/Winnipeg" },
  { city: "Guatemala City", translations: { fr: "Guatemala", es: "Ciudad de Guatemala", de: "Guatemala-Stadt", pt: "Cidade da Guatemala", it: "Città del Guatemala", nl: "Guatemala-Stad", pl: "Gwatemala (miasto)", uk: "Гватемала (місто)", tr: "Guatemala şehri", ru: "Гватемала (город)", ar: "مدينة غواتيمالا", he: "גואטמלה סיטי", fa: "گواتمالاسیتی", hi: "ग्वाटेमाला नगर", bn: "গুয়াতেমালা সিটি", te: "గ్వాటెమాలా నగరం", ta: "குவாத்தமாலா நகரம்", mr: "ग्वातेमाला सिटी", gu: "ગ્વાટેમાલા સિટી", kn: "ಗ್ವಾಟೆಮಾಲಾ ನಗರ", ml: "ഗ്വാട്ടിമാല സിറ്റി", pa: "ਗੁਆਤੇਮਾਲਾ ਸ਼ਹਿਰ", ur: "گوئٹے مالا شہر", id: "Kota Guatemala", ms: "Bandar Guatemala", th: "กัวเตมาลาซิตี", vi: "Thành phố Guatemala", km: "ទីក្រុងហ្គាតេម៉ាឡា", my: "ဂွါတီမာလာမြို့တော်", zh: "危地马拉城", ja: "グアテマラシティ", ko: "과테말라 시티", el: "Πόλη της Γουατεμάλας", bg: "Гватемала (град)", cs: "Ciudad de Guatemala", sk: "Guatemala (mesto)", hu: "Guatemalaváros", ro: "Ciudad de Guatemala", hr: "Ciudad de Guatemala", sr: "Гватемала (град)", bs: "Guatemala City", sl: "Ciudad de Guatemala", mk: "Гватемала (град)", et: "Guatemala", lv: "Gvatemala (pilsēta)", lt: "Gvatemala (miestas)", da: "Guatemala City", fi: "Guatemala City", nb: "Guatemala by", sv: "Guatemala City", ca: "Ciutat de Guatemala", gl: "Cidade de Guatemala", eu: "Guatemalako Hiria", af: "Guatemalastad", sw: "Guatemala City", am: "ጓቴማላ ከተማ", ka: "გვატემალა (ქალაქი)", hy: "Գվատեմալա (քաղաք)", az: "Qvatemala (şəhər)", uz: "Gvatemala shahri", kk: "Гватемала (қала)", tg: "Гватемала (шаҳр)", tk: "Gwatemala şäheri", ky: "Гватемала шаары" }, timezone: "America/Guatemala" },
  { city: "San Salvador", translations: { fr: "San Salvador", es: "San Salvador", de: "San Salvador", pt: "São Salvador", it: "San Salvador", nl: "San Salvador", pl: "San Salvador", uk: "Сан-Сальвадор", tr: "San Salvador", ru: "Сан-Сальвадор", ar: "سان سلفادور", he: "סן סלבדור", fa: "سان سالوادور", hi: "सान साल्वाडोर", bn: "সান সালভাদোর", te: "శాన్ సాల్వడార్", ta: "சான் சல்வதோர்", mr: "सान साल्वाडोर", gu: "સાન સાલ્વાડોર", kn: "ಸ್ಯಾನ್ ಸಾಲ್ವಡಾರ್", ml: "സാൻ സാൽവദോർ", pa: "ਸਾਨ ਸਾਲਵਾਡੋਰ", ur: "سان سلواڈور", id: "San Salvador", ms: "San Salvador", th: "ซานซัลวาดอร์", vi: "San Salvador", km: "សាន់សាល់វ៉ាឌ័រ", my: "ဆန်ဆာဗေးဒေါမြို့", zh: "圣萨尔瓦多", ja: "サンサルバドル", ko: "산살바도르", el: "Σαν Σαλβαδόρ", bg: "Сан Салвадор", cs: "San Salvador", sk: "San Salvador", hu: "San Salvador", ro: "San Salvador", hr: "San Salvador", sr: "Сан Салвадор", bs: "San Salvador", sl: "San Salvador", mk: "Сан Салвадор", et: "San Salvador", lv: "Sansalvadora", lt: "San Salvadoras", da: "San Salvador", fi: "San Salvador", nb: "San Salvador", sv: "San Salvador", ca: "San Salvador", gl: "San Salvador", eu: "San Salvador", af: "San Salvador", sw: "San Salvador", am: "ሳን ሳልቫዶር", ka: "სან-სალვადორი", hy: "Սան Սալվադոր", az: "San-Salvador", uz: "San-Salvador", kk: "Сан-Сальвадор", tg: "Сан-Салвадор", tk: "San Salwador", ky: "Сан-Сальвадор" }, timezone: "America/El_Salvador" },
  { city: "New York", translations: { fr: "New York", es: "Nueva York", de: "New York", pt: "Nova Iorque", it: "New York", nl: "New York", pl: "Nowy Jork", uk: "Нью-Йорк", tr: "New York", ru: "Нью-Йорк", ar: "نيويورك", he: "ניו יורק", fa: "نیویورک", hi: "न्यूयॉर्क", bn: "নিউ ইয়র্ক", te: "న్యూయార్క్", ta: "நியூயார்க்", mr: "न्यू यॉर्क", gu: "ન્યુ યોર્ક", kn: "ನ್ಯೂಯಾರ್ಕ್", ml: "ന്യൂയോർക്ക്", pa: "ਨਿਊਯਾਰਕ", ur: "نیو یارک", id: "New York", ms: "New York", th: "นิวยอร์ก", vi: "New York", km: "ញូវយ៉ក", my: "နယူးယောက်မြို့", zh: "纽约", ja: "ニューヨーク", ko: "뉴욕", el: "Νέα Υόρκη", bg: "Ню Йорк", cs: "New York", sk: "New York", hu: "New York", ro: "New York", hr: "New York", sr: "Њујорк", bs: "New York", sl: "New York", mk: "Њујорк", et: "New York", lv: "Ņujorka", lt: "Niujorkas", da: "New York", fi: "New York", nb: "New York", sv: "New York", ca: "Nova York", gl: "Nova York", eu: "New York", af: "New York", sw: "New York", am: "ኒው ዮርክ", ka: "ნიუ-იორკი", hy: "Նյու Յորք", az: "Nyu-York", uz: "Nyu-York", kk: "Нью-Йорк", tg: "Ню-Йорк", tk: "Nýu-Ýork", ky: "Нью-Йорк" }, timezone: "America/New_York" },
  { city: "Toronto", translations: { fr: "Toronto", es: "Toronto", de: "Toronto", pt: "Toronto", it: "Toronto", nl: "Toronto", pl: "Toronto", uk: "Торонто", tr: "Toronto", ru: "Торонто", ar: "تورونتو", he: "טורונטו", fa: "تورنتو", hi: "टोरंटो", bn: "টরন্টো", te: "టొరంటో", ta: "டொராண்டோ", mr: "टोराँटो", gu: "ટોરોન્ટો", kn: "ಟೊರೊಂಟೊ", ml: "ടൊറന്റോ", pa: "ਟੋਰਾਂਟੋ", ur: "ٹورانٹو", id: "Toronto", ms: "Toronto", th: "โทรอนโต", vi: "Toronto", km: "តូរ៉ុនតូ", my: "တိုရွန်တိုမြို့", zh: "多伦多", ja: "トロント", ko: "토론토", el: "Τορόντο", bg: "Торонто", cs: "Toronto", sk: "Toronto", hu: "Toronto", ro: "Toronto", hr: "Toronto", sr: "Торонто", bs: "Toronto", sl: "Toronto", mk: "Торонто", et: "Toronto", lv: "Toronto", lt: "Torontas", da: "Toronto", fi: "Toronto", nb: "Toronto", sv: "Toronto", ca: "Toronto", gl: "Toronto", eu: "Toronto", af: "Toronto", sw: "Toronto", am: "ቶሮንቶ", ka: "ტორონტო", hy: "Տորոնտո", az: "Toronto", uz: "Toronto", kk: "Торонто", tg: "Торонто", tk: "Toronto", ky: "Торонто" }, timezone: "America/Toronto" },
  { city: "Miami", translations: { fr: "Miami", es: "Miami", de: "Miami", pt: "Miami", it: "Miami", nl: "Miami", pl: "Miami", uk: "Маямі", tr: "Miami", ru: "Майами", ar: "ميامي", he: "מיאמי", fa: "میامی", hi: "मियामी", bn: "মিয়ামি", te: "మయామి", ta: "மியாமி", mr: "मायामी", gu: "મિયામી", kn: "ಮಿಯಾಮಿ", ml: "മയാമി", pa: "ਮਿਆਮੀ", ur: "میامی", id: "Miami", ms: "Miami", th: "ไมแอมี", vi: "Miami", km: "ម៉ៃអាមី", my: "မိုင်ယာမီမြို့", zh: "迈阿密", ja: "マイアミ", ko: "마이애미", el: "Μαϊάμι", bg: "Маями", cs: "Miami", sk: "Miami", hu: "Miami", ro: "Miami", hr: "Miami", sr: "Мајами", bs: "Miami", sl: "Miami", mk: "Мајами", et: "Miami", lv: "Maiami", lt: "Majamis", da: "Miami", fi: "Miami", nb: "Miami", sv: "Miami", ca: "Miami", gl: "Miami", eu: "Miami", af: "Miami", sw: "Miami", am: "ማያሚ", ka: "მაიამი", hy: "Մայամի", az: "Mayami", uz: "Mayami", kk: "Майами", tg: "Майами", tk: "Maýami", ky: "Майами" }, timezone: "America/New_York" },
  { city: "Atlanta", translations: { fr: "Atlanta", es: "Atlanta", de: "Atlanta", pt: "Atlanta", it: "Atlanta", nl: "Atlanta", pl: "Atlanta", uk: "Атланта", tr: "Atlanta", ru: "Атланта", ar: "أتلانتا", he: "אטלנטה", fa: "آتلانتا", hi: "अटलांटा", bn: "আটলান্টা", te: "అట్లాంటా", ta: "அட்லாண்டா", mr: "अटलांटा", gu: "એટલાન્ટા", kn: "ಅಟ್ಲಾಂಟಾ", ml: "അറ്റ്ലാന്റ", pa: "ਅਟਲਾਂਟਾ", ur: "اٹلانٹا", id: "Atlanta", ms: "Atlanta", th: "แอตแลนตา", vi: "Atlanta", km: "អាត្លង់តា", my: "အတ္တလန်တာမြို့", zh: "亚特兰大", ja: "アトランタ", ko: "애틀랜타", el: "Ατλάντα", bg: "Атланта", cs: "Atlanta", sk: "Atlanta", hu: "Atlanta", ro: "Atlanta", hr: "Atlanta", sr: "Атланта", bs: "Atlanta", sl: "Atlanta", mk: "Атланта", et: "Atlanta", lv: "Atlanta", lt: "Atlanta", da: "Atlanta", fi: "Atlanta", nb: "Atlanta", sv: "Atlanta", ca: "Atlanta", gl: "Atlanta", eu: "Atlanta", af: "Atlanta", sw: "Atlanta", am: "አትላንታ", ka: "ატლანტა", hy: "Ատլանտա", az: "Atlanta", uz: "Atlanta", kk: "Атланта", tg: "Атланта", tk: "Atlanta", ky: "Атланта" }, timezone: "America/New_York" },
  { city: "Boston", translations: { fr: "Boston", es: "Boston", de: "Boston", pt: "Boston", it: "Boston", nl: "Boston", pl: "Boston", uk: "Бостон", tr: "Boston", ru: "Бостон", ar: "بوسطن", he: "בוסטון", fa: "بوستون", hi: "बोस्टन", bn: "বস্টন", te: "బోస్టన్", ta: "பாஸ்டன்", mr: "बॉस्टन", gu: "બોસ્ટન", kn: "ಬೋಸ್ಟನ್", ml: "ബോസ്റ്റൺ", pa: "ਬੋਸਟਨ", ur: "بوسٹن", id: "Boston", ms: "Boston", th: "บอสตัน", vi: "Boston", km: "បូស្តុន", my: "ဘော်စတွန်မြို့", zh: "波士顿", ja: "ボストン", ko: "보스턴", el: "Βοστώνη", bg: "Бостън", cs: "Boston", sk: "Boston", hu: "Boston", ro: "Boston", hr: "Boston", sr: "Бостон", bs: "Boston", sl: "Boston", mk: "Бостон", et: "Boston", lv: "Bostona", lt: "Bostonas", da: "Boston", fi: "Boston", nb: "Boston", sv: "Boston", ca: "Boston", gl: "Boston", eu: "Boston", af: "Boston", sw: "Boston", am: "ቦስተን", ka: "ბოსტონი", hy: "Բոստոն", az: "Boston", uz: "Boston", kk: "Бостон", tg: "Бостон", tk: "Boston", ky: "Бостон" }, timezone: "America/New_York" },
  { city: "Washington DC", translations: { fr: "Washington DC", es: "Washington DC", de: "Washington DC", pt: "Washington DC", it: "Washington DC", nl: "Washington D.C.", pl: "Waszyngton", uk: "Вашингтон", tr: "Washington, D.C.", ru: "Вашингтон", ar: "واشنطن العاصمة", he: "וושינגטון די. סי.", fa: "واشینگتن، دی.سی.", hi: "वाशिंगटन, डी॰ सी॰", bn: "ওয়াশিংটন, ডি.সি.", te: "వాషింగ్టన్, డి.సి.", ta: "வாசிங்டன், டி. சி.", mr: "वॉशिंग्टन, डी.सी.", gu: "વોશિંગ્ટન, ડી.સી.", kn: "ವಾಷಿಂಗ್ಟನ್, ಡಿ.ಸಿ.", ml: "വാഷിംഗ്ടൺ, ഡി.സി.", pa: "ਵਾਸ਼ਿੰਗਟਨ, ਡੀ.ਸੀ.", ur: "واشنگٹن ڈی سی", id: "Washington, D.C.", ms: "Washington, D.C.", th: "วอชิงตัน ดี.ซี.", vi: "Washington, D.C.", km: "វ៉ាស៊ីនតោន ឌីស៊ី", my: "ဝါရှင်တန်ဒီစီမြို့", zh: "华盛顿特区", ja: "ワシントンD.C.", ko: "워싱턴 D.C.", el: "Ουάσινγκτον, Π.Κ.", bg: "Вашингтон", cs: "Washington, D.C.", sk: "Washington D.C.", hu: "Washington D.C.", ro: "Washington, D.C.", hr: "Washington, D.C.", sr: "Вашингтон", bs: "Washington, D.C.", sl: "Washington, D.C.", mk: "Вашингтон", et: "Washington", lv: "Vašingtona", lt: "Vašingtonas", da: "Washington D.C.", fi: "Washington D.C.", nb: "Washington D.C.", sv: "Washington D.C.", ca: "Washington DC", gl: "Washington, D.C.", eu: "Washington", af: "Washington, D.C.", sw: "Washington, D.C.", am: "ዋሽንግተን ዲሲ", ka: "ვაშინგტონი", hy: "Վաշինգտոն", az: "Vaşinqton", uz: "Vashington", kk: "Вашингтон", tg: "Вашингтон", tk: "Waşington", ky: "Вашингтон" }, timezone: "America/New_York" },
  { city: "Montreal", translations: { fr: "Montréal", es: "Montreal", de: "Montreal", pt: "Montreal", it: "Montréal", nl: "Montreal", pl: "Montreal", uk: "Монреаль", tr: "Montreal", ru: "Монреаль", ar: "مونتريال", he: "מונטריאול", fa: "مونترآل", hi: "मॉन्ट्रियल", bn: "মন্ট্রিয়ল", te: "మాంట్రియల్", ta: "மொண்ட்ரியால்", mr: "माँत्रियाल", gu: "મોન્ટ્રીયલ", kn: "ಮಾಂಟ್ರಿಯಲ್", ml: "മോൺട്രിയാൽ", pa: "ਮਾਂਟਰੀਆਲ", ur: "مانٹریال", id: "Montreal", ms: "Montreal", th: "มอนทรีออล", vi: "Montréal", km: "ម៉ុងត្រេអាល់", my: "မွန်းထရီးအောမြို့", zh: "蒙特利尔", ja: "モントリオール", ko: "몬트리올", el: "Μόντρεαλ", bg: "Монреал", cs: "Montreal", sk: "Montreal", hu: "Montréal", ro: "Montreal", hr: "Montréal", sr: "Монтреал", bs: "Montreal", sl: "Montreal", mk: "Монтреал", et: "Montréal", lv: "Monreāla", lt: "Monrealis", da: "Montreal", fi: "Montreal", nb: "Montreal", sv: "Montréal", ca: "Mont-real", gl: "Montreal", eu: "Montreal", af: "Montreal", sw: "Montreal", am: "ሞንትሪያል", ka: "მონრეალი", hy: "Մոնրեալ", az: "Monreal", uz: "Monreal", kk: "Монреаль", tg: "Монреал", tk: "Monreal", ky: "Монреаль" }, timezone: "America/Montreal" },
  { city: "Ottawa", translations: { fr: "Ottawa", es: "Ottawa", de: "Ottawa", pt: "Ottawa", it: "Ottawa", nl: "Ottawa", pl: "Ottawa", uk: "Оттава", tr: "Ottawa", ru: "Оттава", ar: "أوتاوا", he: "אוטווה", fa: "اتاوا", hi: "ओटावा", bn: "অটোয়া", te: "ఒట్టావా", ta: "ஒட்டாவா", mr: "ओटावा", gu: "ઓટાવા", kn: "ಒಟ್ಟಾವಾ", ml: "ഒട്ടാവ", pa: "ਓਟਾਵਾ", ur: "اوٹاوا", id: "Ottawa", ms: "Ottawa", th: "ออตตาวา", vi: "Ottawa", km: "អូតាវ៉ា", my: "အော့တဝါမြို့", zh: "渥太华", ja: "オタワ", ko: "오타와", el: "Οττάβα", bg: "Отава", cs: "Ottawa", sk: "Ottawa", hu: "Ottawa", ro: "Ottawa", hr: "Ottawa", sr: "Отава", bs: "Ottawa", sl: "Ottawa", mk: "Отава", et: "Ottawa", lv: "Otava", lt: "Otava", da: "Ottawa", fi: "Ottawa", nb: "Ottawa", sv: "Ottawa", ca: "Ottawa", gl: "Otava", eu: "Ottawa", af: "Ottawa", sw: "Ottawa", am: "ኦታዋ", ka: "ოტავა", hy: "Օտտավա", az: "Ottava", uz: "Ottava", kk: "Оттава", tg: "Оттава", tk: "Ottawa", ky: "Оттава" }, timezone: "America/Toronto" },
  { city: "Havana", translations: { fr: "La Havane", es: "La Habana", de: "Havanna", pt: "Havana", it: "L'Avana", nl: "Havana", pl: "Hawana", uk: "Гавана", tr: "Havana", ru: "Гавана", ar: "هافانا", he: "הוואנה", fa: "هاوانا", hi: "हवाना", bn: "হাভানা", te: "హవానా", ta: "ஹவானா", mr: "हवाना", gu: "હવાના", kn: "ಹವಾನಾ", ml: "ഹവാന", pa: "ਹਵਾਨਾ", ur: "ہوانا", id: "Havana", ms: "Havana", th: "ฮาวานา", vi: "La Habana", km: "ឡាហាវ៉ាន", my: "ဟာဗားနားမြို့", zh: "哈瓦那", ja: "ハバナ", ko: "아바나", el: "Αβάνα", bg: "Хавана", cs: "Havana", sk: "Havana", hu: "Havanna", ro: "Havana", hr: "Havana", sr: "Хавана", bs: "Havana", sl: "Havana", mk: "Хавана", et: "Havanna", lv: "Havana", lt: "Havana", da: "Havana", fi: "Havanna", nb: "Havanna", sv: "Havanna", ca: "L'Havana", gl: "A Habana", eu: "Habana", af: "Havana", sw: "Havana", am: "ሃቫና", ka: "ჰავანა", hy: "Հավանա", az: "Havana", uz: "Gavana", kk: "Гавана", tg: "Гавана", tk: "Gawana", ky: "Гавана" }, timezone: "America/Havana" },
  { city: "Lima", translations: { fr: "Lima", es: "Lima", de: "Lima", pt: "Lima", it: "Lima", nl: "Lima", pl: "Lima", uk: "Ліма", tr: "Lima", ru: "Лима", ar: "ليما", he: "לימה", fa: "لیما", hi: "लीमा", bn: "লিমা", te: "లిమా", ta: "லிமா", mr: "लिमा", gu: "લિમા", kn: "ಲಿಮಾ", ml: "ലിമ", pa: "ਲੀਮਾ", ur: "لیما", id: "Lima", ms: "Lima", th: "ลิมา", vi: "Lima", km: "លីម៉ា", my: "လီမာမြို့", zh: "利马", ja: "リマ", ko: "리마", el: "Λίμα", bg: "Лима", cs: "Lima", sk: "Lima", hu: "Lima", ro: "Lima", hr: "Lima", sr: "Лима", bs: "Lima", sl: "Lima", mk: "Лима", et: "Lima", lv: "Lima", lt: "Lima", da: "Lima", fi: "Lima", nb: "Lima", sv: "Lima", ca: "Lima", gl: "Lima", eu: "Lima", af: "Lima", sw: "Lima", am: "ሊማ", ka: "ლიმა", hy: "Լիմա", az: "Lima", uz: "Lima", kk: "Лима", tg: "Лима", tk: "Lima", ky: "Лима" }, timezone: "America/Lima" },
  { city: "Bogota", translations: { fr: "Bogota", es: "Bogotá", de: "Bogotá", pt: "Bogotá", it: "Bogotà", nl: "Bogota", pl: "Bogota", uk: "Богота", tr: "Bogotá", ru: "Богота", ar: "بوغوتا", he: "בוגוטה", fa: "بوگوتا", hi: "बोगोटा", bn: "বোগোতা", te: "బొగోటా", ta: "பொகோட்டா", mr: "बोगोटा", gu: "બોગોટા", kn: "ಬೊಗೋಟಾ", ml: "ബൊഗോട്ട", pa: "ਬੋਗੋਟਾ", ur: "بوگوتا", id: "Bogotá", ms: "Bogotá", th: "โบโกตา", vi: "Bogotá", km: "បូហ្គោតា", my: "ဘိုဂိုတာမြို့", zh: "波哥大", ja: "ボゴタ", ko: "보고타", el: "Μπογκοτά", bg: "Богота", cs: "Bogotá", sk: "Bogota", hu: "Bogotá", ro: "Bogotá", hr: "Bogotá", sr: "Богота", bs: "Bogotá", sl: "Bogota", mk: "Богота", et: "Bogotá", lv: "Bogota", lt: "Bogota", da: "Bogotá", fi: "Bogotá", nb: "Bogotá", sv: "Bogotá", ca: "Bogotà", gl: "Bogotá", eu: "Bogota", af: "Bogotá", sw: "Bogotá", am: "ቦጎታ", ka: "ბოგოტა", hy: "Բոգոտա", az: "Boqota", uz: "Bogota", kk: "Богота", tg: "Богота", tk: "Bogota", ky: "Богота" }, timezone: "America/Bogota" },

  // UTC-4 à UTC-2
  { city: "Santiago", translations: { fr: "Santiago", es: "Santiago", de: "Santiago", pt: "Santiago", it: "Santiago", nl: "Santiago", pl: "Santiago", uk: "Сантьяго", tr: "Santiago", ru: "Сантьяго", ar: "سانتياغو", he: "סנטיאגו", fa: "سانتیاگو", hi: "सैंटियागो", bn: "সান্তিয়াগো", te: "శాంటియాగో", ta: "சாண்டியாகோ", mr: "सान्तियागो", gu: "સેન્ટિયાગો", kn: "ಸ್ಯಾಂಟಿಯಾಗೊ", ml: "സാന്റിയാഗോ", pa: "ਸੈਂਟੀਆਗੋ", ur: "سینٹیاگو", id: "Santiago", ms: "Santiago", th: "ซันติอาโก", vi: "Santiago", km: "សាន់ត្យាហ្គោ", my: "စန်တီယာဂိုမြို့", zh: "圣地亚哥", ja: "サンティアゴ", ko: "산티아고", el: "Σαντιάγο", bg: "Сантяго", cs: "Santiago", sk: "Santiago", hu: "Santiago", ro: "Santiago", hr: "Santiago", sr: "Сантијаго", bs: "Santiago", sl: "Santiago", mk: "Сантјаго", et: "Santiago", lv: "Santjago", lt: "Santjagas", da: "Santiago", fi: "Santiago", nb: "Santiago", sv: "Santiago", ca: "Santiago", gl: "Santiago", eu: "Santiago", af: "Santiago", sw: "Santiago", am: "ሳንቲያጎ", ka: "სანტიაგო", hy: "Սանտյագո", az: "Santyaqo", uz: "Santyago", kk: "Сантьяго", tg: "Сантяго", tk: "Santýago", ky: "Сантьяго" }, timezone: "America/Santiago" },
  { city: "Caracas", translations: { fr: "Caracas", es: "Caracas", de: "Caracas", pt: "Caracas", it: "Caracas", nl: "Caracas", pl: "Caracas", uk: "Каракас", tr: "Karakas", ru: "Каракас", ar: "كاراكاس", he: "קראקס", fa: "کاراکاس", hi: "काराकास", bn: "কারাকাস", te: "కరాకస్", ta: "கராகஸ்", mr: "काराकास", gu: "કારાકાસ", kn: "ಕಾರಕಾಸ್", ml: "കാരക്കാസ്", pa: "ਕਾਰਾਕਸ", ur: "کاراکاس", id: "Caracas", ms: "Caracas", th: "การากัส", vi: "Caracas", km: "ការ៉ាកាស", my: "ကရာကတ်မြို့", zh: "加拉加斯", ja: "カラカス", ko: "카라카스", el: "Καράκας", bg: "Каракас", cs: "Caracas", sk: "Caracas", hu: "Caracas", ro: "Caracas", hr: "Caracas", sr: "Каракас", bs: "Caracas", sl: "Caracas", mk: "Каракас", et: "Caracas", lv: "Karakasa", lt: "Karakasas", da: "Caracas", fi: "Caracas", nb: "Caracas", sv: "Caracas", ca: "Caracas", gl: "Caracas", eu: "Caracas", af: "Caracas", sw: "Caracas", am: "ካራካስ", ka: "კარაკასი", hy: "Կարակաս", az: "Karakas", uz: "Karakas", kk: "Каракас", tg: "Каракас", tk: "Karakas", ky: "Каракас" }, timezone: "America/Caracas" },
  { city: "Halifax", translations: { fr: "Halifax", es: "Halifax", de: "Halifax", pt: "Halifax", it: "Halifax", nl: "Halifax", pl: "Halifax", uk: "Галіфакс", tr: "Halifax", ru: "Галифакс", ar: "هاليفاكس", he: "הליפקס", fa: "هالیفاکس", hi: "हैलिफ़ैक्स", bn: "হ্যালিফ্যাক্স", te: "హాలిఫాక్స్", ta: "ஹாலிஃபாக்ஸ்", mr: "हॅलिफॅक्स", gu: "હેલિફેક્સ", kn: "ಹ್ಯಾಲಿಫ್ಯಾಕ್ಸ್", ml: "ഹാലിഫാക്സ്", pa: "ਹੈਲੀਫ਼ੈਕਸ", ur: "ہیلی فیکس", id: "Halifax", ms: "Halifax", th: "แฮลิแฟกซ์", vi: "Halifax", km: "ហាលីហ្វាក់", my: "ဟာလီဖက်မြို့", zh: "哈利法克斯", ja: "ハリファックス", ko: "핼리팩스", el: "Χάλιφαξ", bg: "Халифакс", cs: "Halifax", sk: "Halifax", hu: "Halifax", ro: "Halifax", hr: "Halifax", sr: "Халифакс", bs: "Halifax", sl: "Halifax", mk: "Халифакс", et: "Halifax", lv: "Halifaksa", lt: "Halifaksas", da: "Halifax", fi: "Halifax", nb: "Halifax", sv: "Halifax", ca: "Halifax", gl: "Halifax", eu: "Halifax", af: "Halifax", sw: "Halifax", am: "ሃሊፋክስ", ka: "ჰალიფაქსი", hy: "Հալիֆաքս", az: "Halifaks", uz: "Galifaks", kk: "Галифакс", tg: "Галифакс", tk: "Halifaks", ky: "Галифакс" }, timezone: "America/Halifax" },
  { city: "Santo Domingo", translations: { fr: "Saint-Domingue", es: "Santo Domingo", de: "Santo Domingo", pt: "São Domingos", it: "Santo Domingo", nl: "Santo Domingo", pl: "Santo Domingo", uk: "Санто-Домінго", tr: "Santo Domingo", ru: "Санто-Доминго", ar: "سانتو دومينغو", he: "סנטו דומינגו", fa: "سانتو دومینگو", hi: "सैंटो डोमिंगो", bn: "সান্তো দোমিঙ্গো", te: "శాంటో డొమింగో", ta: "சான்டோ டொமிங்கோ", mr: "सांतो दॉमिंगो", gu: "સેન્ટો ડોમિંગો", kn: "ಸ್ಯಾಂಟೋ ಡೊಮಿಂಗೊ", ml: "സാന്റോ ഡൊമിംഗോ", pa: "ਸੈਂਟੋ ਡੋਮਿੰਗੋ", ur: "سانتو دومنگو", id: "Santo Domingo", ms: "Santo Domingo", th: "ซานโตโดมิงโก", vi: "Santo Domingo", km: "សាន់តូដូមីងហ្គោ", my: "ဆန်တိုဒိုမင်ဂိုမြို့", zh: "圣多明各", ja: "サントドミンゴ", ko: "산토도밍고", el: "Σάντο Ντομίνγκο", bg: "Санто Доминго", cs: "Santo Domingo", sk: "Santo Domingo", hu: "Santo Domingo", ro: "Santo Domingo", hr: "Santo Domingo", sr: "Санто Доминго", bs: "Santo Domingo", sl: "Santo Domingo", mk: "Санто Доминго", et: "Santo Domingo", lv: "Santodomingo", lt: "Santo Domingas", da: "Santo Domingo", fi: "Santo Domingo", nb: "Santo Domingo", sv: "Santo Domingo", ca: "Santo Domingo", gl: "Santo Domingo", eu: "Santo Domingo", af: "Santo Domingo", sw: "Santo Domingo", am: "ሳንቶ ዶሚንጎ", ka: "სანტო-დომინგო", hy: "Սանտո Դոմինգո", az: "Santo-Dominqo", uz: "Santo-Domingo", kk: "Санто-Доминго", tg: "Санто-Доминго", tk: "Santo Domingo", ky: "Санто-Доминго" }, timezone: "America/Santo_Domingo" },
  { city: "La Paz", translations: { fr: "La Paz", es: "La Paz", de: "La Paz", pt: "La Paz", it: "La Paz", nl: "La Paz", pl: "La Paz", uk: "Ла-Пас", tr: "La Paz", ru: "Ла-Пас", ar: "لا باز", he: "לה פאס", fa: "لاپاز", hi: "ला पाज़", bn: "লা পাজ", te: "లా పాజ్", ta: "லா பாஸ்", mr: "ला पाझ", gu: "લા પાઝ", kn: "ಲಾ ಪಾಜ್", ml: "ലാ പാസ്", pa: "ਲਾ ਪਾਜ਼", ur: "لا پاز", id: "La Paz", ms: "La Paz", th: "ลาปาซ", vi: "La Paz", km: "ឡា ប៉ាស", my: "လာပတ်ဇ်မြို့", zh: "拉巴斯", ja: "ラパス", ko: "라파스", el: "Λα Πας", bg: "Ла Пас", cs: "La Paz", sk: "La Paz", hu: "La Paz", ro: "La Paz", hr: "La Paz", sr: "Ла Паз", bs: "La Paz", sl: "La Paz", mk: "Ла Паз", et: "La Paz", lv: "Lapasa", lt: "La Pasas", da: "La Paz", fi: "La Paz", nb: "La Paz", sv: "La Paz", ca: "La Paz", gl: "A Paz", eu: "La Paz", af: "La Paz", sw: "La Paz", am: "ላ ፓዝ", ka: "ლა-პასი", hy: "Լա Պաս", az: "La-Pas", uz: "La-Pas", kk: "Ла-Пас", tg: "Ла-Пас", tk: "La Pas", ky: "Ла-Пас" }, timezone: "America/La_Paz" },
  { city: "Manaus", translations: { fr: "Manaus", es: "Manaos", de: "Manaus", pt: "Manaus", it: "Manaus", nl: "Manaus", pl: "Manaus", uk: "Манаус", tr: "Manaus", ru: "Манаус", ar: "ماناوس", he: "מנאוס", fa: "مانائوس", hi: "मनौस", bn: "মানাউশ", te: "మనాస్", ta: "மனாவுஸ்", mr: "मानौस", gu: "મૅનૉસ", kn: "ಮನೌಸ್", ml: "മനോസ്", pa: "ਮਾਨੌਸ", ur: "میناس", id: "Manaus", ms: "Manaus", th: "มาเนาส์", vi: "Manaus", km: "ម៉ាណូស", my: "မနော့စ်မြို့", zh: "马瑙斯", ja: "マナウス", ko: "마나우스", el: "Μανάους", bg: "Манаус", cs: "Manaus", sk: "Manaus", hu: "Manaus", ro: "Manaus", hr: "Manaus", sr: "Манаус", bs: "Manaus", sl: "Manaus", mk: "Манаус", et: "Manaus", lv: "Manausa", lt: "Manausas", da: "Manaus", fi: "Manaus", nb: "Manaus", sv: "Manaus", ca: "Manaus", gl: "Manaus", eu: "Manaus", af: "Manaus", sw: "Manaus", am: "ማናውስ", ka: "მანაუსი", hy: "Մանաուս", az: "Manaus", uz: "Manaus", kk: "Манаус", tg: "Манаус", tk: "Manaus", ky: "Манаус" }, timezone: "America/Manaus" },
  { city: "São Paulo", translations: { fr: "São Paulo", es: "São Paulo", de: "São Paulo", pt: "São Paulo", it: "San Paolo", nl: "São Paulo", pl: "São Paulo", uk: "Сан-Паулу", tr: "São Paulo", ru: "Сан-Паулу", ar: "ساو باولو", he: "סאו פאולו", fa: "سائوپائولو", hi: "साओ पाउलो", bn: "সাও পাওলো", te: "సావో పాలో", ta: "சாவோ பாவுலோ", mr: "साओ पाउलो", gu: "સાઓ પાઉલો", kn: "ಸಾವೊ ಪಾಲೊ", ml: "സാവോ പോളോ", pa: "ਸਾਓ ਪਾਓਲੋ", ur: "ساؤ پالو", id: "São Paulo", ms: "São Paulo", th: "เซาเปาลู", vi: "São Paulo", km: "សៅប៉ូឡូ", my: "ဆောပိုလိုမြို့", zh: "圣保罗", ja: "サンパウロ", ko: "상파울루", el: "Σάο Πάολο", bg: "Сао Пауло", cs: "São Paulo", sk: "São Paulo", hu: "São Paulo", ro: "São Paulo", hr: "São Paulo", sr: "Сао Пауло", bs: "São Paulo", sl: "São Paulo", mk: "Сао Паоло", et: "São Paulo", lv: "Sanpaulu", lt: "San Paulas", da: "São Paulo", fi: "São Paulo", nb: "São Paulo", sv: "São Paulo", ca: "São Paulo", gl: "São Paulo", eu: "São Paulo", af: "São Paulo", sw: "São Paulo", am: "ሳኦ ፓውሎ", ka: "სან-პაულუ", hy: "Սան Պաուլու", az: "San-Paulu", uz: "San-Paulu", kk: "Сан-Паулу", tg: "Сан-Паулу", tk: "San-Paulu", ky: "Сан-Паулу" }, timezone: "America/Sao_Paulo" },
  { city: "Buenos Aires", translations: { fr: "Buenos Aires", es: "Buenos Aires", de: "Buenos Aires", pt: "Buenos Aires", it: "Buenos Aires", nl: "Buenos Aires", pl: "Buenos Aires", uk: "Буенос-Айрес", tr: "Buenos Aires", ru: "Буэнос-Айрес", ar: "بوينس آيرس", he: "בואנוס איירס", fa: "بوئنوس آیرس", hi: "ब्यूनस आयर्स", bn: "বুয়েনোস আইরেস", te: "బ్యూనస్ ఎయిర్స్", ta: "புவெனஸ் ஐரிஸ்", mr: "बुएनोस आइरेस", gu: "બ્યુનોસ એરેસ", kn: "ಬ್ಯೂನಸ್ ಐರಿಸ್", ml: "ബ്യൂണസ് അയേഴ്സ്", pa: "ਬੁਏਨਸ ਆਇਰਸ", ur: "بیونس آئرس", id: "Buenos Aires", ms: "Buenos Aires", th: "บัวโนสไอเรส", vi: "Buenos Aires", km: "ប៊ុយណូស៊ែ", my: "ဗျူနိုအေးရိစ်မြို့", zh: "布宜诺斯艾利斯", ja: "ブエノスアイレス", ko: "부에노스아이레스", el: "Μπουένος Άιρες", bg: "Буенос Айрес", cs: "Buenos Aires", sk: "Buenos Aires", hu: "Buenos Aires", ro: "Buenos Aires", hr: "Buenos Aires", sr: "Буенос Ајрес", bs: "Buenos Aires", sl: "Buenos Aires", mk: "Буенос Аирес", et: "Buenos Aires", lv: "Buenosairesa", lt: "Buenos Airės", da: "Buenos Aires", fi: "Buenos Aires", nb: "Buenos Aires", sv: "Buenos Aires", ca: "Buenos Aires", gl: "Bos Aires", eu: "Buenos Aires", af: "Buenos Aires", sw: "Buenos Aires", am: "ቡэнос አይረስ", ka: "ბუენოს-აირესი", hy: "Բուենոս Այրես", az: "Buenos-Ayres", uz: "Buenos-Ayres", kk: "Буэнос-Айрес", tg: "Буэнос-Айрес", tk: "Buenos-Aýres", ky: "Буэнос-Айрес" }, timezone: "America/Argentina/Buenos_Aires" },
  { city: "Rio de Janeiro", translations: { fr: "Rio de Janeiro", es: "Río de Janeiro", de: "Rio de Janeiro", pt: "Rio de Janeiro", it: "Rio de Janeiro", nl: "Rio de Janeiro", pl: "Rio de Janeiro", uk: "Ріо-де-Жанейро", tr: "Rio de Janeiro", ru: "Рио-де-Жанейро", ar: "ريو دي جانيرو", he: "ריו דה ז'ניירו", fa: "ریودوژانیرو", hi: "रियो डि जेनेरो", bn: "রিউ দি জানেইরু", te: "రియో డి జనీరో", ta: "ரியோ டி ஜனேரோ", mr: "रियो दि जानेरो", gu: "રિયો ડી જાનેરો", kn: "ರಿಯೊ ಡಿ ಜನೈರೊ", ml: "റിയോ ഡി ജനീറോ", pa: "ਰੀਓ ਡੀ ਜਨੇਰੀਓ", ur: "ریو دے جینیرو", id: "Rio de Janeiro", ms: "Rio de Janeiro", th: "รีโอเดจาเนโร", vi: "Rio de Janeiro", km: "រីយ៉ូ ដេ ចាណេរ៉ូ", my: "ရီယိုဒီဂျနေးရိုးမြို့", zh: "里约热内卢", ja: "リオデジャネイロ", ko: "리우데자네이루", el: "Ρίο ντε Τζανέιρο", bg: "Рио де Жанейро", cs: "Rio de Janeiro", sk: "Rio de Janeiro", hu: "Rio de Janeiro", ro: "Rio de Janeiro", hr: "Rio de Janeiro", sr: "Рио де Жанеиро", bs: "Rio de Janeiro", sl: "Rio de Janeiro", mk: "Рио де Жанеиро", et: "Rio de Janeiro", lv: "Riodežaneiro", lt: "Rio de Žaneiras", da: "Rio de Janeiro", fi: "Rio de Janeiro", nb: "Rio de Janeiro", sv: "Rio de Janeiro", ca: "Rio de Janeiro", gl: "Río de Xaneiro", eu: "Rio de Janeiro", af: "Rio de Janeiro", sw: "Rio de Janeiro", am: "ሪዮ ዲ ጃኔሮ", ka: "რიო-დე-ჟანეირო", hy: "Ռիո դե Ժանեյրո", az: "Rio-de-Janeyro", uz: "Rio-de-Janeyro", kk: "Рио-де-Жанейро", tg: "Рио-де-Жанейро", tk: "Rio-de-Žaneýro", ky: "Рио-де-Жанейро" }, timezone: "America/Sao_Paulo" },
  { city: "Montevideo", translations: { fr: "Montevideo", es: "Montevideo", de: "Montevideo", pt: "Montevidéu", it: "Montevideo", nl: "Montevideo", pl: "Montevideo", uk: "Монтевідео", tr: "Montevideo", ru: "Монтевидео", ar: "مونتيفيديو", he: "מונטווידאו", fa: "مونته‌ویدئو", hi: "मोंटेवीडियो", bn: "মোন্তেভিদেও", te: "మాంటెవీడియో", ta: "மொண்டிவிடியோ", mr: "मोन्तेविदेओ", gu: "મોન્ટેવિડિયો", kn: "ಮಾಂಟೆವಿಡಿಯೊ", ml: "മൊണ്ടേവീഡിയോ", pa: "ਮੋਂਤੇਵੀਦਿਓ", ur: "مونتیبیدیو", id: "Montevideo", ms: "Montevideo", th: "มอนเตวิเดโอ", vi: "Montevideo", km: "ម៉ុងតេវីដេអូ", my: "မွန်တီဗီဒီယိုမြို့", zh: "蒙得维的亚", ja: "モンテビデオ", ko: "몬테비데오", el: "Μοντεβιδέο", bg: "Монтевидео", cs: "Montevideo", sk: "Montevideo", hu: "Montevideo", ro: "Montevideo", hr: "Montevideo", sr: "Монтевидео", bs: "Montevideo", sl: "Montevideo", mk: "Монтевидео", et: "Montevideo", lv: "Montevideo", lt: "Montevidėjas", da: "Montevideo", fi: "Montevideo", nb: "Montevideo", sv: "Montevideo", ca: "Montevideo", gl: "Montevideo", eu: "Montevideo", af: "Montevideo", sw: "Montevideo", am: "ሞንቴቪዴዎ", ka: "მონტევიდეო", hy: "Մոնտեվիդեո", az: "Montevideo", uz: "Montevideo", kk: "Монтевидео", tg: "Монтевидео", tk: "Montevideo", ky: "Монтевидео" }, timezone: "America/Montevideo" },
  { city: "Brasilia", translations: { fr: "Brasilia", es: "Brasilia", de: "Brasília", pt: "Brasília", it: "Brasilia", nl: "Brasilia", pl: "Brasília", uk: "Бразиліа", tr: "Brasília", ru: "Бразилиа", ar: "برازيليا", he: "ברזיליה", fa: "برازیلیا", hi: "ब्रासीलिया", bn: "ব্রাসিলিয়া", te: "బ్రెసిలియా", ta: "பிரசிலியா", mr: "ब्राझिलिया", gu: "બ્રાઝિલિયા", kn: "ಬ್ರೆಸಿಲಿಯಾ", ml: "ബ്രസീലിയ", pa: "ਬ੍ਰਾਜ਼ੀਲੀਆ", ur: "براسیلیا", id: "Brasília", ms: "Brasília", th: "บราซีเลีย", vi: "Brasília", km: "ប្រាស៊ីលីយ៉ា", my: "ဘရာစေးလေးယားမြို့", zh: "巴西利亚", ja: "ブラジリア", ko: "브라질리아", el: "Μπραζίλια", bg: "Бразилия", cs: "Brasília", sk: "Brazília", hu: "Brazíliaváros", ro: "Brasília", hr: "Brasília", sr: "Бразилија", bs: "Brasília", sl: "Brasilia", mk: "Бразилија", et: "Brasília", lv: "Brazilja", lt: "Brazilija", da: "Brasília", fi: "Brasília", nb: "Brasília", sv: "Brasília", ca: "Brasília", gl: "Brasilia", eu: "Brasilia", af: "Brasília", sw: "Brasília", am: "ብራዚሊያ", ka: "ბრაზილია", hy: "Բրազիլիա", az: "Brazilia", uz: "Brazilia", kk: "Бразилиа", tg: "Бразилиа", tk: "Brazilia", ky: "Бразилиа" }, timezone: "America/Sao_Paulo" },
  { city: "St. John's", translations: { fr: "Saint-Jean", es: "San Juan", de: "St. John's", pt: "St. John's", it: "St. John's", nl: "St. John's", pl: "St. John's", uk: "Сент-Джонс", tr: "St. John's", ru: "Сент-Джонс", ar: "سانت جونز", he: "סנט ג'ונס", fa: "سینت جانز", hi: "सेंट जॉन", bn: "সেন্ট জন'স", te: "సెయింట్ జాన్స్", ta: "செயின்ட் ஜான்ஸ்", mr: "सेंट जॉन्स", gu: "સેન્ટ જોન્સ", kn: "ಸೇಂಟ್ ಜಾನ್ಸ್", ml: "സെന്റ് ജോൺസ്", pa: "ਸੇਂਟ ਜੌਨਜ਼", ur: "سینٹ جانز", id: "St. John's", ms: "St. John's", th: "เซนต์จอนส์", vi: "St. John's", km: "សាំងចន", my: "စိန့်ဂျွန်မြို့", zh: "圣约翰斯", ja: "セントジョンズ", ko: "세인트존스", el: "Σεντ Τζονς", bg: "Сейнт Джонс", cs: "St. John's", sk: "St. John’s", hu: "St. John's", ro: "St. John's", hr: "St. John's", sr: "Сент Џонс", bs: "St. John's", sl: "St. John's", mk: "Сент Џонс", et: "Saint John's", lv: "Sentdžonsa", lt: "Sent Džonsas", da: "St. John's", fi: "St. John's", nb: "St. John's", sv: "St. John's", ca: "St. John's", gl: "St. John's", eu: "St. John's", af: "St. John's", sw: "St. John's", am: "ሴንት ጆንስ", ka: "სენტ-ჯონზი", hy: "Սենտ Ջոնս", az: "Sent-Cons", uz: "Sent-Jons", kk: "Сент-Джонс", tg: "Сент-Ҷонс", tk: "Sent-Jons", ky: "Сент-Жонс" }, timezone: "America/St_Johns" },
  { city: "Nuuk", translations: { fr: "Nuuk", es: "Nuuk", de: "Nuuk", pt: "Nuuk", it: "Nuuk", nl: "Nuuk", pl: "Nuuk", uk: "Нуук", tr: "Nuuk", ru: "Нуук", ar: "نوك", he: "נוק", fa: "نوک", hi: "नूक", bn: "নুক", te: "నూక్", ta: "நூக்", mr: "नूक", gu: "નૂક", kn: "ನೂಕ್", ml: "നൂക്ക്", pa: "ਨੂਕ", ur: "نوک", id: "Nuuk", ms: "Nuuk", th: "นุก", vi: "Nuuk", km: "នូក", my: "နုခ်မြို့", zh: "努克", ja: "ヌーク", ko: "누크", el: "Νουούκ", bg: "Нуук", cs: "Nuuk", sk: "Nuuk", hu: "Nuuk", ro: "Nuuk", hr: "Nuuk", sr: "Нук", bs: "Nuuk", sl: "Nuuk", mk: "Нук", et: "Nuuk", lv: "Nūka", lt: "Nukas", da: "Nuuk", fi: "Nuuk", nb: "Nuuk", sv: "Nuuk", ca: "Nuuk", gl: "Nuuk", eu: "Nuuk", af: "Nuuk", sw: "Nuuk", am: "ኑክ", ka: "ნუუკი", hy: "Նուուկ", az: "Nuuk", uz: "Nuuk", kk: "Нуук", tg: "Нуук", tk: "Nuuk", ky: "Нуук" }, timezone: "America/Nuuk" },
  { city: "Fernando de Noronha", translations: { fr: "Fernando de Noronha", es: "Fernando de Noronha", de: "Fernando de Noronha", pt: "Fernando de Noronha", it: "Fernando de Noronha", nl: "Fernando de Noronha", pl: "Fernando de Noronha", uk: "Фернанду-ді-Норонья", tr: "Fernando de Noronha", ru: "Фернанду-ди-Норонья", ar: "فرناندو دي نورونيا", he: "פרננדו די נורוניה", fa: "فرناندو دی نورونیا", hi: "फर्नांडो डी नोरोन्हा", bn: "ফার্নান্দো দে নোরোনহা", te: "ఫెర్నాండో డి నోరోన్హా", ta: "பெர்னாண்டோ டி நோரோன்ஹா", mr: "फर्नांडो दे नोरोन्हा", gu: "ફર્નાન્ડો ડી નોરોન્હા", kn: "ಫೆರ್ನಾಂಡೊ ಡಿ ನೊರೊನ್ಹಾ", ml: "ഫെർണാണ്ടോ ഡി നൊറോൻഹ", pa: "ਫਰਨਾਂਡੋ ਡੀ ਨੋਰੋਨਹਾ", ur: "فرنانڈو ڈی نورونہا", id: "Fernando de Noronha", ms: "Fernando de Noronha", th: "เฟร์นันดูจีนอโรนยา", vi: "Fernando de Noronha", km: "ហ្វឺណាន់ដូ ដឺ ណូរ៉ូណា", my: "ဖာနန်ဒိုဒီနိုရွန်ဟာကျွန်း", zh: "费尔南多·迪诺罗尼亚", ja: "フェルナンド・デ・ノローニャ", ko: "페르난두 지 노로냐", el: "Φερνάντο ντε Νορόνια", bg: "Фернандо ди Нороня", cs: "Fernando de Noronha", sk: "Fernando de Noronha", hu: "Fernando de Noronha", ro: "Fernando de Noronha", hr: "Fernando de Noronha", sr: "Фернандо де Нороња", bs: "Fernando de Noronha", sl: "Fernando de Noronha", mk: "Фернандо де Нороња", et: "Fernando de Noronha", lv: "Fernandu di Noroņa", lt: "Fernando de Noronija", da: "Fernando de Noronha", fi: "Fernando de Noronha", nb: "Fernando de Noronha", sv: "Fernando de Noronha", ca: "Fernando de Noronha", gl: "Fernando de Noronha", eu: "Fernando de Noronha", af: "Fernando de Noronha", sw: "Fernando de Noronha", am: "ፌርናንዶ ዴ ኖሮንሃ", ka: "ფერნანდუ-დი-ნორონია", hy: "Ֆեռնանդու դի Նորոնյա", az: "Fernandu-di-Noronya", uz: "Fernandu-di-Noronya", kk: "Фернанду-ди-Норонья", tg: "Фернанду-ди-Нороня", tk: "Fernandu-di-Noronýa", ky: "Фернанду-ди-Норонья" }, timezone: "America/Noronha" },
  { city: "South Georgia", translations: { fr: "Géorgie du Sud", es: "Georgia del Sur", de: "Südgeorgien", pt: "Geórgia do Sul", it: "Georgia del Sud", nl: "Zuid-Georgia", pl: "Georgia Południowa", uk: "Південна Джорджія", tr: "Güney Georgia", ru: "Южная Георгия", ar: "جورجيا الجنوبية", he: "ג'ורג'יה הדרומית", fa: "جورجیای جنوبی", hi: "दक्षिण जॉर्जिया", bn: "দক্ষিণ জর্জিয়া", te: "దక్షిణ జార్జియా", ta: "தென் ஜார்ஜியா", mr: "दक्षिण जॉर्जिया", gu: "દક્ષિણ જ્યોર્જિયા", kn: "ದಕ್ಷಿಣ ಜಾರ್ಜಿಯಾ", ml: "തെക്കൻ ജോർജിയ", pa: "ਦੱਖਣੀ ਜਾਰਜੀਆ", ur: "جنوبی جارجیا", id: "Georgia Selatan", ms: "Georgia Selatan", th: "เกาะเซาท์จอร์เจีย", vi: "Nam Georgia", km: "ហ្សកហ្ស៊ីខាងត្បូង", my: "တောင်ဂျော်ဂျီယာကျွန်း", zh: "南乔治亚", ja: "サウスジョージア", ko: "사우스조지아", el: "Νότια Γεωργία", bg: "Южна Джорджия", cs: "Jižní Georgie", sk: "Južná Georgia", hu: "Déli-Georgia", ro: "Georgia de Sud", hr: "Južna Georgija", sr: "Јужна Џорџија", bs: "Južna Džordžija", sl: "Južna Georgia", mk: "Јужна Џорџија", et: "Lõuna-Georgia", lv: "Dienviddžordžija", lt: "Pietų Džordžija", da: "South Georgia", fi: "Etelä-Georgia", nb: "Sør-Georgia", sv: "Sydgeorgien", ca: "Geòrgia del Sud", gl: "Xeorxia do Sur", eu: "Hegoaldeko Georgiak", af: "Suid-Georgië", sw: "Georgia Kusini", am: "ደቡብ ጆርጂያ", ka: "სამხრეთი ჯორჯია", hy: "Հարավային Ջորջիա", az: "Cənubi Georgiya", uz: "Janubiy Georgiya", kk: "Оңтүстік Георгия", tg: "Ҷорҷияи Ҷанубӣ", tk: "Günorta Georgiýa", ky: "Түштүк Георгия" }, timezone: "Atlantic/South_Georgia" },

  // UTC-1 à UTC+1
  { city: "Azores", translations: { fr: "Açores", es: "Azores", de: "Azoren", pt: "Açores", it: "Azzorre", nl: "Azoren", pl: "Azory", uk: "Азорські острови", tr: "Azorlar", ru: "Азорские острова", ar: "جزر الأزور", he: "האיים האזוריים", fa: "آزور", hi: "अज़ोरेस", bn: "আজোরস", te: "అజోర్స్", ta: "அசோரஸ்", mr: "अझोर्स", gu: "એઝોર્સ", kn: "ಅಜೋರ್ಸ್", ml: "അസോറസ്", pa: "ਅਜ਼ੋਰਸ", ur: "آزورس", id: "Azores", ms: "Azores", th: "อะโซร์ส", vi: "Azores", km: "អាហ្សូស", my: "အဇောရက်စ်ကျွန်းစု", zh: "亚速尔群岛", ja: "アゾレス諸島", ko: "아소르스 제도", el: "Αζόρες", bg: "Азорски острови", cs: "Azory", sk: "Azory", hu: "Azori-szigetek", ro: "Azore", hr: "Azori", sr: "Азорска острва", bs: "Azori", sl: "Azori", mk: "Азори", et: "Assoorid", lv: "Azoru salas", lt: "Azorai", da: "Azorerne", fi: "Azorit", nb: "Azorene", sv: "Azorerna", ca: "Açores", gl: "Azores", eu: "Azoreak", af: "Asore", sw: "Azores", am: "አዞረስ", ka: "აზორის კუნძულები", hy: "Ազորյան կղզիներ", az: "Azor adaları", uz: "Azor orollari", kk: "Азор аралдары", tg: "Ҷазираҳои Азор", tk: "Azor adalary", ky: "Азор аралдары" }, timezone: "Atlantic/Azores" },
  { city: "Cape Verde", translations: { fr: "Cap-Vert", es: "Cabo Verde", de: "Kap Verde", pt: "Cabo Verde", it: "Capo Verde", nl: "Kaapverdië", pl: "Republika Zielonego Przylądka", uk: "Кабо-Верде", tr: "Yeşil Burun Adaları", ru: "Кабо-Верде", ar: "الرأس الأخضر", he: "כף ורדה", fa: "کیپ ورد", hi: "केप वर्दे", bn: "কেপ ভার্দে", te: "కేప్ వెర్డే", ta: "கேப் வெர்டி", mr: "काबो व्हर्दे", gu: "કેપ વર્ડે", kn: "ಕೇಪ್ ವರ್ಡೆ", ml: "കേപ്പ് വെർഡെ", pa: "ਕੇਪ ਵਰਡੇ", ur: "کیپ ورڈی", id: "Tanjung Verde", ms: "Tanjung Verde", th: "กาบูเวร์ดี", vi: "Cabo Verde", km: "កាបវែរ", my: "ကိတ်ဗာဒီနိုင်ငံ", zh: "佛得角", ja: "カーボベルデ", ko: "카보베르데", el: "Πράσινο Ακρωτήριο", bg: "Кабо Верде", cs: "Kapverdy", sk: "Kapverdy", hu: "Zöld-foki Köztársaság", ro: "Capul Verde", hr: "Zelenortska Republika", sr: "Зеленортска Острва", bs: "Zelenortska Ostrva", sl: "Zelenortski otoki", mk: "Зелен ’Рт", et: "Roheneemesaared", lv: "Kaboverde", lt: "Žaliasis Kyšulys", da: "Kap Verde", fi: "Kap Verde", nb: "Kapp Verde", sv: "Kap Verde", ca: "Cap Verd", gl: "Cabo Verde", eu: "Cabo Verde", af: "Kaap Verde", sw: "Cabo Verde", am: "ኬፕ ቨርድ", ka: "კაბო-ვერდე", hy: "Կաբո Վերդե", az: "Kabo-Verde", uz: "Kabo-Verde", kk: "Кабо-Верде", tg: "Кабо-Верде", tk: "Kabo-Werde", ky: "Кабо-Верде" }, timezone: "Atlantic/Cape_Verde" },
  { city: "Reykjavik", translations: { fr: "Reykjavik", es: "Reikiavik", de: "Reykjavik", pt: "Reykjavík", it: "Reykjavík", nl: "Reykjavik", pl: "Reykjavík", uk: "Рейк'явік", tr: "Reykjavík", ru: "Рейкьявик", ar: "ريكيافيك", he: "רייקיאוויק", fa: "ریکیاویک", hi: "रेक्याविक", bn: "রেইকিয়াভিক", te: "రేక్జావిక్", ta: "ரெய்க்யவிக்", mr: "रेक्याविक", gu: "રેકજાવિક", kn: "ರೇಕ್ಜಾವಿಕ್", ml: "റെയ്ക്യാവിക്", pa: "ਰੇਕਿਆਵਿਕ", ur: "ریکیاوک", id: "Reykjavík", ms: "Reykjavík", th: "เรคยาวิก", vi: "Reykjavík", km: "រ៉េក្យាវីក", my: "ရေခ်ျာဗစ်မြို့", zh: "雷克雅未克", ja: "レイキャヴィク", ko: "레이캬비크", el: "Ρέικιαβικ", bg: "Рейкявик", cs: "Reykjavík", sk: "Reykjavík", hu: "Reykjavík", ro: "Reykjavík", hr: "Reykjavík", sr: "Рејкјавик", bs: "Reykjavík", sl: "Reykjavík", mk: "Рејкјавик", et: "Reykjavík", lv: "Reikjavika", lt: "Reikjavikas", da: "Reykjavik", fi: "Reykjavík", nb: "Reykjavík", sv: "Reykjavik", ca: "Reykjavík", gl: "Reiquiavik", eu: "Reykjavík", af: "Reykjavik", sw: "Reykjavík", am: "ሬይኪያቪክ", ka: "რეიკიავიკი", hy: "Ռեյկյավիկ", az: "Reykyavik", uz: "Reykyavik", kk: "Рейкьявик", tg: "Рейкявик", tk: "Reýkýawik", ky: "Рейкьявик" }, timezone: "Atlantic/Reykjavik" },
  { city: "London", translations: { fr: "Londres", es: "Londres", de: "London", pt: "Londres", it: "Londra", nl: "Londen", pl: "Londyn", uk: "Лондон", tr: "Londra", ru: "Лондон", ar: "لندن", he: "לונדון", fa: "لندن", hi: "लंदन", bn: "লন্ডন", te: "లండన్", ta: "இலண்டன்", mr: "लंडन", gu: "લંડન", kn: "ಲಂಡನ್", ml: "ലണ്ടൻ", pa: "ਲੰਡਨ", ur: "لندن", id: "London", ms: "London", th: "ลอนดอน", vi: "Luân Đôn", km: "ឡុងដ៍", my: "လန်ဒန်မြို့", zh: "伦敦", ja: "ロンドン", ko: "런던", el: "Λονδίνο", bg: "Лондон", cs: "Londýn", sk: "Londýn", hu: "London", ro: "Londra", hr: "London", sr: "Лондон", bs: "London", sl: "London", mk: "Лондон", et: "London", lv: "Londona", lt: "Londonas", da: "London", fi: "Lontoo", nb: "London", sv: "London", ca: "Londres", gl: "Londres", eu: "Londres", af: "Londen", sw: "London", am: "ለንደን", ka: "ლონდონი", hy: "Լոնդոն", az: "London", uz: "London", kk: "Лондон", tg: "Лондон", tk: "London", ky: "Лондон" }, timezone: "Europe/London" },
  { city: "Dublin", translations: { fr: "Dublin", es: "Dublín", de: "Dublin", pt: "Dublin", it: "Dublino", nl: "Dublin", pl: "Dublin", uk: "Дублін", tr: "Dublin", ru: "Дублин", ar: "دبلن", he: "דבלין", fa: "دوبلین", hi: "डबलिन", bn: "ডাবলিন", te: "డబ్లిన్", ta: "டப்லின்", mr: "डब्लिन", gu: "ડબલિન", kn: "ಡಬ್ಲಿನ್", ml: "ഡബ്ലിൻ", pa: "ਡਬਲਿਨ", ur: "ڈبلن", id: "Dublin", ms: "Dublin", th: "ดับลิน", vi: "Dublin", km: "ឌុយប្លាំង", my: "ဒပ်ဗလင်မြို့", zh: "都柏林", ja: "ダブリン", ko: "더블린", el: "Δουβλίνο", bg: "Дъблин", cs: "Dublin", sk: "Dublin", hu: "Dublin", ro: "Dublin", hr: "Dublin", sr: "Даблин", bs: "Dublin", sl: "Dublin", mk: "Даблин", et: "Dublin", lv: "Dublina", lt: "Dublinas", da: "Dublin", fi: "Dublin", nb: "Dublin", sv: "Dublin", ca: "Dublín", gl: "Dublín", eu: "Dublin", af: "Dublin", sw: "Dublin", am: "ደብሊን", ka: "დუბლინი", hy: "Դուբլին", az: "Dublin", uz: "Dublin", kk: "Дублин", tg: "Дублин", tk: "Dublin", ky: "Дублин" }, timezone: "Europe/Dublin" },
  { city: "Lisbon", translations: { fr: "Lisbonne", es: "Lisboa", de: "Lissabon", pt: "Lisboa", it: "Lisbona", nl: "Lissabon", pl: "Lizbona", uk: "Лісабон", tr: "Lizbon", ru: "Лиссабон", ar: "لشبونة", he: "ליסבון", fa: "لیسبون", hi: "लिस्बन", bn: "লিসবন", te: "లిస్బన్", ta: "லிஸ்பன்", mr: "लिस्बन", gu: "લિસ્બન", kn: "ಲಿಸ್ಬನ್", ml: "ലിസ്ബൺ", pa: "ਲਿਸਬਨ", ur: "لزبن", id: "Lisboa", ms: "Lisbon", th: "ลิสบอน", vi: "Lisboa", km: "លីសបោន", my: "လစ်စဘွန်းမြို့", zh: "里斯本", ja: "リスボン", ko: "리스본", el: "Λισαβόνα", bg: "Лисабон", cs: "Lisabon", sk: "Lisabon", hu: "Lisszabon", ro: "Lisabona", hr: "Lisabon", sr: "Лисабон", bs: "Lisabon", sl: "Lizbona", mk: "Лисабон", et: "Lissabon", lv: "Lisabona", lt: "Lisabona", da: "Lissabon", fi: "Lissabon", nb: "Lisboa", sv: "Lissabon", ca: "Lisboa", gl: "Lisboa", eu: "Lisboa", af: "Lissabon", sw: "Lisbon", am: "ሊዝበን", ka: "ლისაბონი", hy: "Լիսաբոն", az: "Lissabon", uz: "Lissabon", kk: "Лиссабон", tg: "Лиссабон", tk: "Lissabon", ky: "Лиссабон" }, timezone: "Europe/Lisbon" },
  { city: "Casablanca", translations: { fr: "Casablanca", es: "Casablanca", de: "Casablanca", pt: "Casablanca", it: "Casablanca", nl: "Casablanca", pl: "Casablanca", uk: "Касабланка", tr: "Kazablanka", ru: "Касабланка", ar: "الدار البيضاء", he: "קזבלנקה", fa: "کازابلانکا", hi: "कासाब्लांका", bn: "কাসাব্লাঙ্কা", te: "కాసాబ్లాంకా", ta: "காசாபிளாங்கா", mr: "कासाब्लांका", gu: "કાસાબ્લાન્કા", kn: "ಕಾಸಾಬ್ಲಾಂಕಾ", ml: "കാസാബ്ലാങ്ക", pa: "ਕਾਸਾਬਲਾਂਕਾ", ur: "کاسابلانکا", id: "Casablanca", ms: "Casablanca", th: "กาซาบลังกา", vi: "Casablanca", km: "កាសាប្លង់កា", my: "ကာဆာဘလန်ကာမြို့", zh: "卡萨布兰卡", ja: "カサブランカ", ko: "카사블랑카", el: "Καζαμπλάνκα", bg: "Казабланка", cs: "Casablanca", sk: "Casablanca", hu: "Casablanca", ro: "Casablanca", hr: "Casablanca", sr: "Казабланка", bs: "Casablanca", sl: "Casablanca", mk: "Казабланка", et: "Casablanca", lv: "Kasablanka", lt: "Kasablanka", da: "Casablanca", fi: "Casablanca", nb: "Casablanca", sv: "Casablanca", ca: "Casablanca", gl: "Casablanca", eu: "Casablanca", af: "Casablanca", sw: "Casablanca", am: "ካዛብላንካ", ka: "კასაბლანკა", hy: "Կասաբլանկա", az: "Kasablanka", uz: "Kasablanka", kk: "Касабланка", tg: "Касабланка", tk: "Kasablanka", ky: "Касабланка" }, timezone: "Africa/Casablanca" },
  { city: "Dakar", translations: { fr: "Dakar", es: "Dakar", de: "Dakar", pt: "Dacar", it: "Dakar", nl: "Dakar", pl: "Dakar", uk: "Дакар", tr: "Dakar", ru: "Дакар", ar: "داكار", he: "דקר", fa: "داکار", hi: "डकार", bn: "ডাকার", te: "డాకర్", ta: "டக்கார்", mr: "डकार", gu: "ડાકાર", kn: "ಡಾಕರ್", ml: "ഡാകർ", pa: "ਡਕਾਰ", ur: "ڈاکار", id: "Dakar", ms: "Dakar", th: "ดาการ์", vi: "Dakar", km: "ដាកា", my: "ဒါကာမြို့", zh: "达喀尔", ja: "ダカール", ko: "다카르", el: "Ντακάρ", bg: "Дакар", cs: "Dakar", sk: "Dakar", hu: "Dakar", ro: "Dakar", hr: "Dakar", sr: "Дакар", bs: "Dakar", sl: "Dakar", mk: "Дакар", et: "Dakar", lv: "Dakara", lt: "Dakaras", da: "Dakar", fi: "Dakar", nb: "Dakar", sv: "Dakar", ca: "Dakar", gl: "Dakar", eu: "Dakar", af: "Dakar", sw: "Dakar", am: "ዳካር", ka: "დაკარი", hy: "Դակար", az: "Dakar", uz: "Dakar", kk: "Дакар", tg: "Дакар", tk: "Dakar", ky: "Дакар" }, timezone: "Africa/Dakar" },
  { city: "Accra", translations: { fr: "Accra", es: "Accra", de: "Accra", pt: "Acra", it: "Accra", nl: "Accra", pl: "Akra", uk: "Аккра", tr: "Akra", ru: "Аккра", ar: "أكرا", he: "אקרה", fa: "آکرا", hi: "अक्करा", bn: "আক্রা", te: "అక్రా", ta: "அக்ரா", mr: "आक्रा", gu: "અક્રા", kn: "ಅಕ್ರಾ", ml: "അക്ര", pa: "ਅਕਰਾ", ur: "اکرا", id: "Accra", ms: "Accra", th: "อักกรา", vi: "Accra", km: "អាក្រា", my: "အက်ခရာမြို့", zh: "阿克拉", ja: "アクラ", ko: "아크라", el: "Άκκρα", bg: "Акра", cs: "Accra", sk: "Accra", hu: "Accra", ro: "Accra", hr: "Accra", sr: "Акра", bs: "Accra", sl: "Akra", mk: "Акра", et: "Accra", lv: "Akra", lt: "Akra", da: "Accra", fi: "Accra", nb: "Accra", sv: "Accra", ca: "Accra", gl: "Accra", eu: "Akkra", af: "Accra", sw: "Accra", am: "አክራ", ka: "აკრა", hy: "Աքրա", az: "Akkra", uz: "Akkra", kk: "Аккра", tg: "Аккра", tk: "Akkra", ky: "Аккра" }, timezone: "Africa/Accra" },
  { city: "Paris", translations: { fr: "Paris", es: "París", de: "Paris", pt: "Paris", it: "Parigi", nl: "Parijs", pl: "Paryż", uk: "Париж", tr: "Paris", ru: "Париж", ar: "باريس", he: "פריז", fa: "پاریس", hi: "पेरिस", bn: "প্যারিস", te: "పారిస్", ta: "பாரிஸ்", mr: "पॅरिस", gu: "પેરિસ", kn: "ಪ್ಯಾರಿಸ್", ml: "പാരിസ്", pa: "ਪੈਰਿਸ", ur: "پیرس", id: "Paris", ms: "Paris", th: "ปารีส", vi: "Paris", km: "ប៉ារីស", my: "ပဲရစ်မြို့", zh: "巴黎", ja: "パリ", ko: "파리", el: "Παρίσι", bg: "Париж", cs: "Paříž", sk: "Paríž", hu: "Párizs", ro: "Paris", hr: "Pariz", sr: "Париз", bs: "Pariz", sl: "Pariz", mk: "Париз", et: "Pariis", lv: "Parīze", lt: "Paryžius", da: "Paris", fi: "Pariisi", nb: "Paris", sv: "Paris", ca: "París", gl: "París", eu: "Paris", af: "Parys", sw: "Paris", am: "ፓሪስ", ka: "პარიზი", hy: "Փարիզ", az: "Paris", uz: "Parij", kk: "Париж", tg: "Париж", tk: "Pariž", ky: "Париж" }, timezone: "Europe/Paris" },
  { city: "Berlin", translations: { fr: "Berlin", es: "Berlín", de: "Berlin", pt: "Berlim", it: "Berlino", nl: "Berlijn", pl: "Berlin", uk: "Берлін", tr: "Berlin", ru: "Берлин", ar: "برلين", he: "ברלין", fa: "برلین", hi: "बर्लिन", bn: "বার্লিন", te: "బెర్లిన్", ta: "பெர்லின்", mr: "बर्लिन", gu: "બર્લિન", kn: "ಬರ್ಲಿನ್", ml: "ബെർലിൻ", pa: "ਬਰਲਿਨ", ur: "برلن", id: "Berlin", ms: "Berlin", th: "เบอร์ลิน", vi: "Berlin", km: "ប៊ែរឡាំង", my: "ဘာလင်မြို့", zh: "柏林", ja: "ベルリン", ko: "베를린", el: "Βερολίνο", bg: "Берлин", cs: "Berlín", sk: "Berlín", hu: "Berlin", ro: "Berlin", hr: "Berlin", sr: "Берлин", bs: "Berlin", sl: "Berlin", mk: "Берлин", et: "Berliin", lv: "Berlīne", lt: "Berlynas", da: "Berlin", fi: "Berliini", nb: "Berlin", sv: "Berlin", ca: "Berlín", gl: "Berlín", eu: "Berlin", af: "Berlyn", sw: "Berlin", am: "በርሊን", ka: "ბერლინი", hy: "Բեռլին", az: "Berlin", uz: "Berlin", kk: "Берлин", tg: "Берлин", tk: "Berlin", ky: "Берлин" }, timezone: "Europe/Berlin" },
  { city: "Madrid", translations: { fr: "Madrid", es: "Madrid", de: "Madrid", pt: "Madrid", it: "Madrid", nl: "Madrid", pl: "Madryt", uk: "Мадрид", tr: "Madrid", ru: "Мадрид", ar: "مدريد", he: "מדריד", fa: "مادرید", hi: "मैड्रिड", bn: "মাদ্রিদ", te: "మాడ్రిడ్", ta: "மத்ரித்", mr: "माद्रिद", gu: "મેડ્રિડ", kn: "ಮ್ಯಾಡ್ರಿಡ್", ml: "മാഡ്രിഡ്", pa: "ਮਾਦਰੀਦ", ur: "میدرد", id: "Madrid", ms: "Madrid", th: "มาดริด", vi: "Madrid", km: "ម៉ាឌ្រីដ", my: "မက်ဒရစ်မြို့", zh: "马德里", ja: "マドリード", ko: "마드리드", el: "Μαδρίτη", bg: "Мадрид", cs: "Madrid", sk: "Madrid", hu: "Madrid", ro: "Madrid", hr: "Madrid", sr: "Мадрид", bs: "Madrid", sl: "Madrid", mk: "Мадрид", et: "Madrid", lv: "Madride", lt: "Madridas", da: "Madrid", fi: "Madrid", nb: "Madrid", sv: "Madrid", ca: "Madrid", gl: "Madrid", eu: "Madril", af: "Madrid", sw: "Madrid", am: "ማድሪድ", ka: "მადრიდი", hy: "Մադրիդ", az: "Madrid", uz: "Madrid", kk: "Мадрид", tg: "Мадрид", tk: "Madrid", ky: "Мадрид" }, timezone: "Europe/Madrid" },
  { city: "Rome", translations: { fr: "Rome", es: "Roma", de: "Rom", pt: "Roma", it: "Roma", nl: "Rome", pl: "Rzym", uk: "Рим", tr: "Roma", ru: "Рим", ar: "روما", he: "רומא", fa: "رم", hi: "रोम", bn: "রোম", te: "రోమ్", ta: "உரோம்", mr: "रोम", gu: "રોમ", kn: "ರೋಮ್", ml: "റോം", pa: "ਰੋਮ", ur: "روم", id: "Roma", ms: "Rom", th: "โรม", vi: "Roma", km: "រ៉ូម", my: "ရώမမြို့", zh: "罗马", ja: "ローマ", ko: "로마", el: "Ρώμη", bg: "Рим", cs: "Řím", sk: "Rím", hu: "Róma", ro: "Roma", hr: "Rim", sr: "Рим", bs: "Rim", sl: "Rim", mk: "Рим", et: "Rooma", lv: "Roma", lt: "Roma", da: "Rom", fi: "Rooma", nb: "Roma", sv: "Rom", ca: "Roma", gl: "Roma", eu: "Erroma", af: "Rome", sw: "Roma", am: "ሮም", ka: "რომი", hy: "Հռոմ", az: "Roma", uz: "Rim", kk: "Рим", tg: "Рим", tk: "Rim", ky: "Рим" }, timezone: "Europe/Rome" },
  { city: "Amsterdam", translations: { fr: "Amsterdam", es: "Ámsterdam", de: "Amsterdam", pt: "Amesterdão", it: "Amsterdam", nl: "Amsterdam", pl: "Amsterdam", uk: "Амстердам", tr: "Amsterdam", ru: "Амстердам", ar: "أمستردام", he: "אמסטרדם", fa: "آمستردام", hi: "एम्सटर्डम", bn: "আমস্টারডাম", te: "ఆమ్స్టర్డ్యామ్", ta: "ஆம்ஸ்டர்டம்", mr: "ऍम्स्टरडॅम", gu: "એમ્સ્ટર્ડમ", kn: "ಆಮ್ಸ್ಟರ್‌ಡ್ಯಾಮ್", ml: "ആംസ്റ്റർഡാം", pa: "ਐਮਸਟਰਡੈਮ", ur: "ایمسٹرڈیم", id: "Amsterdam", ms: "Amsterdam", th: "อัมสเตอร์ดัม", vi: "Amsterdam", km: "អាំស្ទែរដាំ", my: "အမ်စတာဒမ်မြို့", zh: "阿姆斯特丹", ja: "アムステルダム", ko: "암스테르담", el: "Άμστερνταμ", bg: "Амстердам", cs: "Amsterdam", sk: "Amsterdam", hu: "Amszterdam", ro: "Amsterdam", hr: "Amsterdam", sr: "Амстердам", bs: "Amsterdam", sl: "Amsterdam", mk: "Амстердам", et: "Amsterdam", lv: "Amsterdama", lt: "Amsterdamas", da: "Amsterdam", fi: "Amsterdam", nb: "Amsterdam", sv: "Amsterdam", ca: "Amsterdam", gl: "Ámsterdam", eu: "Amsterdam", af: "Amsterdam", sw: "Amsterdam", am: "አምስተርዳም", ka: "ამსტერდამი", hy: "Ամստերդամ", az: "Amsterdam", uz: "Amsterdam", kk: "Амстердам", tg: "Амстердам", tk: "Amsterdam", ky: "Амстердам" }, timezone: "Europe/Amsterdam" },
  { city: "Brussels", translations: { fr: "Bruxelles", es: "Bruselas", de: "Brüssel", pt: "Bruxelas", it: "Bruxelles", nl: "Brussel", pl: "Bruksela", uk: "Брюссель", tr: "Brüksel", ru: "Брюссель", ar: "بروكسل", he: "בריסל", fa: "بروکسل", hi: "ब्रूसेल्स", bn: "ব্রাসেলস", te: "బ్రసెల్స్", ta: "பிரசெல்சு", mr: "ब्रसेल्स", gu: "બ્રસેલ્સ", kn: "ಬ್ರಸೆಲ್ಸ್", ml: "ബ്രസൽസ്", pa: "ਬਰੂਸਲ", ur: "برسلز", id: "Brussel", ms: "Brussels", th: "บรัสเซลส์", vi: "Bruxelles", km: "ព្រុចសែល", my: "ဘရပ်ဆဲလ်မြို့", zh: "布鲁塞尔", ja: "ブリュッセル", ko: "브뤼셀", el: "Βρυξέλλες", bg: "Брюксел", cs: "Brusel", sk: "Brusel", hu: "Brüsszel", ro: "Bruxelles", hr: "Bruxelles", sr: "Брисел", bs: "Brisel", sl: "Bruselj", mk: "Брисел", et: "Brüssel", lv: "Brisele", lt: "Briuselis", da: "Bruxelles", fi: "Bryssel", nb: "Brussel", sv: "Bryssel", ca: "Brussel·les", gl: "Bruxelas", eu: "Brusela", af: "Brussel", sw: "Brussels", am: "ብራስልስ", ka: "ბრიუსელი", hy: "Բրյուսել", az: "Brüssel", uz: "Brussel", kk: "Брюссель", tg: "Брюссел", tk: "Brýussel", ky: "Брюссель" }, timezone: "Europe/Brussels" },
  { city: "Vienna", translations: { fr: "Vienne", es: "Viena", de: "Wien", pt: "Viena", it: "Vienna", nl: "Wenen", pl: "Wiedeń", uk: "Відень", tr: "Viyana", ru: "Вена", ar: "فيينا", he: "וינה", fa: "وین", hi: "वियना", bn: "ভিয়েনা", te: "వియన్నా", ta: "வியன்னா", mr: "व्हियेना", gu: "વિયેના", kn: "ವಿಯೆನ್ನಾ", ml: "വിയന്ന", pa: "ਵਿਆਨਾ", ur: "ویانا", id: "Wina", ms: "Vienna", th: "เวียนนา", vi: "Viên", km: "វីយែន", my: "ဗီယင်နာမြို့", zh: "维也纳", ja: "ウィーン", ko: "빈", el: "Βιέννη", bg: "Виена", cs: "Vídeň", sk: "Viedeň", hu: "Bécs", ro: "Viena", hr: "Beč", sr: "Беч", bs: "Beč", sl: "Dunaj", mk: "Виена", et: "Viin", lv: "Vīne", lt: "Viena", da: "Wien", fi: "Wien", nb: "Wien", sv: "Wien", ca: "Viena", gl: "Viena", eu: "Viena", af: "Wene", sw: "Vienna", am: "ቪየና", ka: "ვენა", hy: "Վիեննա", az: "Vyana", uz: "Vena", kk: "Вена", tg: "Вена", tk: "Wena", ky: "Вена" }, timezone: "Europe/Vienna" },
  { city: "Stockholm", translations: { fr: "Stockholm", es: "Estocolmo", de: "Stockholm", pt: "Estocolmo", it: "Stoccolma", nl: "Stockholm", pl: "Sztokholm", uk: "Стокгольм", tr: "Stokholm", ru: "Стокгольм", ar: "ستوكهولم", he: "סטוקהולם", fa: "استکهلم", hi: "स्टॉकहोम", bn: "স্টকহোম", te: "స్టాక్‌హోమ్", ta: "ஸ்டாக்ஹோம்", mr: "स्टॉकहोम", gu: "સ્ટોકહોમ", kn: "ಸ್ಟಾಕ್‌ಹೋಮ್", ml: "സ്റ്റോക്ക്ഹോം", pa: "ਸਟਾਕਹੋਮ", ur: "اسٹاک ہوم", id: "Stockholm", ms: "Stockholm", th: "สตอกโฮล์ม", vi: "Stockholm", km: "ស្តុកខុល", my: "စတော့ဟုမ်းမြို့", zh: "斯德哥尔摩", ja: "ストックホルム", ko: "스톡홀름", el: "Στοκχόλμη", bg: "Стокхолм", cs: "Stockholm", sk: "Štokholm", hu: "Stockholm", ro: "Stockholm", hr: "Stockholm", sr: "Стокхолм", bs: "Stockholm", sl: "Stockholm", mk: "Стокхолм", et: "Stockholm", lv: "Stokholma", lt: "Stokholmas", da: "Stockholm", fi: "Tukholma", nb: "Stockholm", sv: "Stockholm", ca: "Estocolm", gl: "Estocolmo", eu: "Stockholm", af: "Stockholm", sw: "Stockholm", am: "ስቶኮልም", ka: "სტოკჰოლმი", hy: "Ստոկհոլմ", az: "Stokholm", uz: "Stokgolm", kk: "Стокгольм", tg: "Стокҳолм", tk: "Stokgolm", ky: "Стокгольм" }, timezone: "Europe/Stockholm" },
  { city: "Copenhagen", translations: { fr: "Copenhague", es: "Copenhague", de: "Kopenhagen", pt: "Copenhaga", it: "Copenaghen", nl: "Kopenhagen", pl: "Kopenhaga", uk: "Копенгаген", tr: "Kopenhag", ru: "Копенгаген", ar: "كوبنهاغن", he: "קופנהגן", fa: "کپنهاگ", hi: "कोपेनहेगन", bn: "কোপেনহেগেন", te: "కోపెన్‌హాగన్", ta: "கோப்பன்கேகன்", mr: "कोपनहेगन", gu: "કોપનહેગન", kn: "ಕೋಪನ್‌ಹೇಗನ್", ml: "കോപ്പൻഹേഗൻ", pa: "ਕੋਪਨਹੈਗਨ", ur: "کوپن ہیگن", id: "Kopenhagen", ms: "Copenhagen", th: "โคเปนเฮเกน", vi: "Copenhagen", km: "កូប៉ិនហាក", my: "ကိုပင်ဟေဂင်မြို့", zh: "哥本哈根", ja: "コペンハーゲン", ko: "코펜하겐", el: "Κοπεγχάγη", bg: "Копенхаген", cs: "Kodaň", sk: "Kodaň", hu: "Koppenhága", ro: "Copenhaga", hr: "Kopenhagen", sr: "Копенхаген", bs: "Kopenhagen", sl: "København", mk: "Копенхаген", et: "Kopenhaagen", lv: "Kopenhāgena", lt: "Kopenhaga", da: "København", fi: "Kööpenhamina", nb: "København", sv: "Köpenhamn", ca: "Copenhaguen", gl: "Copenhague", eu: "Kopenhage", af: "Kopenhagen", sw: "Copenhagen", am: "ኮፐንሃገን", ka: "კოპენჰაგენი", hy: "Կոպենհագեն", az: "Kopenhagen", uz: "Kopengagen", kk: "Копенгаген", tg: "Копенҳаген", tk: "Kopengagen", ky: "Копенгаген" }, timezone: "Europe/Copenhagen" },
  { city: "Oslo", translations: { fr: "Oslo", es: "Oslo", de: "Oslo", pt: "Oslo", it: "Oslo", nl: "Oslo", pl: "Oslo", uk: "Осло", tr: "Oslo", ru: "Осло", ar: "أوسلو", he: "אוסלו", fa: "اسلو", hi: "ओस्लो", bn: "অসলো", te: "ఓస్లో", ta: "ஓஸ்லோ", mr: "ओस्लो", gu: "ઓસ્લો", kn: "ಓಸ್ಲೋ", ml: "ഓസ്ലോ", pa: "ਓਸਲੋ", ur: "اوسلو", id: "Oslo", ms: "Oslo", th: "ออสโล", vi: "Oslo", km: "អូស្លូ", my: "အော့စလိုမြို့", zh: "奥斯陆", ja: "オスロ", ko: "오슬로", el: "Όσλο", bg: "Осло", cs: "Oslo", sk: "Oslo", hu: "Oslo", ro: "Oslo", hr: "Oslo", sr: "Осло", bs: "Oslo", sl: "Oslo", mk: "Осло", et: "Oslo", lv: "Oslo", lt: "Oslas", da: "Oslo", fi: "Oslo", nb: "Oslo", sv: "Oslo", ca: "Oslo", gl: "Oslo", eu: "Oslo", af: "Oslo", sw: "Oslo", am: "ኦስሎ", ka: "ოსლო", hy: "Օսլո", az: "Oslo", uz: "Oslo", kk: "Осло", tg: "Осло", tk: "Oslo", ky: "Осло" }, timezone: "Europe/Oslo" },
  { city: "Warsaw", translations: { fr: "Varsovie", es: "Varsovia", de: "Warschau", pt: "Varsóvia", it: "Varsavia", nl: "Warschau", pl: "Warszawa", uk: "Варшава", tr: "Varşova", ru: "Варшава", ar: "وارسو", he: "ורשה", fa: "ورشو", hi: "वारसॉ", bn: "ওয়ারশ", te: "వార్సా", ta: "வார்சா", mr: "वॉर्सा", gu: "વોર્સો", kn: "ವಾರ್ಸಾ", ml: "വാഴ്സോ", pa: "ਵਾਰਸਾ", ur: "وارسا", id: "Warsawa", ms: "Warsaw", th: "วอร์ซอ", vi: "Warszawa", km: "វ៉ារស្សាវ៉ា", my: "ဝါဆောမြို့", zh: "华沙", ja: "ワルシャワ", ko: "바르샤바", el: "Βαρσοβία", bg: "Варшава", cs: "Varšava", sk: "Varšava", hu: "Varsó", ro: "Varșovia", hr: "Varšava", sr: "Варшава", bs: "Varšava", sl: "Varšava", mk: "Варшава", et: "Varssavi", lv: "Varšava", lt: "Varšuva", da: "Warszawa", fi: "Varsova", nb: "Warszawa", sv: "Warszawa", ca: "Varsòvia", gl: "Varsovia", eu: "Varsovia", af: "Warskou", sw: "Warsaw", am: "ዋርሶ", ka: "ვარშავა", hy: "Վարշավա", az: "Varşava", uz: "Varshava", kk: "Варшава", tg: "Варшава", tk: "Warşawa", ky: "Варшава" }, timezone: "Europe/Warsaw" },
  { city: "Prague", translations: { fr: "Prague", es: "Praga", de: "Prag", pt: "Praga", it: "Praga", nl: "Praag", pl: "Praga", uk: "Прага", tr: "Prag", ru: "Прага", ar: "براغ", he: "פראג", fa: "پراگ", hi: "प्राग", bn: "প্রাগ", te: "ప్రాగ్", ta: "பிராக்", mr: "प्राग", gu: "પ્રાગ", kn: "ಪ್ರೇಗ್", ml: "പ്രാഗ്", pa: "ਪਰਾਗ", ur: "پراگ", id: "Praha", ms: "Prague", th: "ปราก", vi: "Praha", km: "ប្រាក", my: "ပရာ့ဂ်မြို့", zh: "布拉格", ja: "プラハ", ko: "프라하", el: "Πράγα", bg: "Прага", cs: "Praha", sk: "Praha", hu: "Prága", ro: "Praga", hr: "Prag", sr: "Праг", bs: "Prag", sl: "Praga", mk: "Прага", et: "Praha", lv: "Prāga", lt: "Praha", da: "Prag", fi: "Praha", nb: "Praha", sv: "Prag", ca: "Praga", gl: "Praga", eu: "Praga", af: "Praag", sw: "Prague", am: "ፕራግ", ka: "პრაღა", hy: "Պրահա", az: "Praqa", uz: "Praga", kk: "Прага", tg: "Прага", tk: "Praga", ky: "Прага" }, timezone: "Europe/Prague" },
  { city: "Budapest", translations: { fr: "Budapest", es: "Budapest", de: "Budapest", pt: "Budapeste", it: "Budapest", nl: "Boedapest", pl: "Budapeszt", uk: "Будапешт", tr: "Budapeşte", ru: "Будапешт", ar: "بودابست", he: "בודפשט", fa: "بوداپست", hi: "बुडापेस्ट", bn: "বুদাপেস্ট", te: "బుడాపెస్ట్", ta: "புடாபெஸ்ட்", mr: "बुडापेस्ट", gu: "બુડાપેસ્ટ", kn: "ಬುಡಾಪೆಸ್ಟ್", ml: "ബുഡാപെസ്റ്റ്", pa: "ਬੁਡਾਪੈਸਟ", ur: "بوداپست", id: "Budapest", ms: "Budapest", th: "บูดาเปสต์", vi: "Budapest", km: "ប៊ុយដាប៉ែស", my: "ဗူးဒပက်မြို့", zh: "布达佩斯", ja: "ブダペスト", ko: "부다페스트", el: "Βουδαπέστη", bg: "Будапеща", cs: "Budapešť", sk: "Budapešť", hu: "Budapest", ro: "Budapesta", hr: "Budimpešta", sr: "Будимпешта", bs: "Budimpešta", sl: "Budimpešta", mk: "Будимпешта", et: "Budapest", lv: "Budapešta", lt: "Budapeštas", da: "Budapest", fi: "Budapest", nb: "Budapest", sv: "Budapest", ca: "Budapest", gl: "Budapest", eu: "Budapest", af: "Boedapest", sw: "Budapest", am: "ቡዳፔስት", ka: "ბუდაპეშტი", hy: "Բուդապեշտ", az: "Budapeşt", uz: "Budapesht", kk: "Будапешт", tg: "Будапешт", tk: "Budapeşt", ky: "Будапешт" }, timezone: "Europe/Budapest" },
  { city: "Belgrade", translations: { fr: "Belgrade", es: "Belgrado", de: "Belgrad", pt: "Belgrado", it: "Belgrado", nl: "Belgrado", pl: "Belgrad", uk: "Белград", tr: "Belgrad", ru: "Белград", ar: "بلغراد", he: "בלגרד", fa: "بلگراد", hi: "बेलग्रेड", bn: "বেলগ্রেড", te: "బెల్గ్రేడ్", ta: "பெல்கிறேட்", mr: "बेलग्रेड", gu: "બેલગ્રેડ", kn: "ಬೆಲ್‌ಗ್ರೇಡ್", ml: "ബെൽഗ്രേഡ്", pa: "ਬੈਲਗ੍ਰਾਡ", ur: "بلغراد", id: "Beograd", ms: "Belgrade", th: "เบลเกรด", vi: "Beograd", km: "បែលក្រាដ", my: "ဘဲလ်ဂရိတ်မြို့", zh: "贝尔格莱德", ja: "ベオグラード", ko: "베오그라드", el: "Βελιγράδι", bg: "Белград", cs: "Bělehrad", sk: "Belehrad", hu: "Belgrád", ro: "Belgrad", hr: "Beograd", sr: "Београд", bs: "Beograd", sl: "Beograd", mk: "Белград", et: "Belgrad", lv: "Belgrada", lt: "Belgradas", da: "Beograd", fi: "Belgrad", nb: "Beograd", sv: "Belgrad", ca: "Belgrad", gl: "Belgrado", eu: "Belgrad", af: "Belgrado", sw: "Belgrade", am: "ቤልግሬድ", ka: "ბელგრადი", hy: "Բելգրադ", az: "Belqrad", uz: "Belgrad", kk: "Белград", tg: "Белград", tk: "Belgrad", ky: "Белград" }, timezone: "Europe/Belgrade" },
  { city: "Zurich", translations: { fr: "Zurich", es: "Zúrich", de: "Zürich", pt: "Zurique", it: "Zurigo", nl: "Zürich", pl: "Zurych", uk: "Цюрих", tr: "Zürih", ru: "Цюрих", ar: "زيورخ", he: "ציריך", fa: "زوریخ", hi: "ज़्यूरिख़", bn: "জুরিখ", te: "జ్యూరిచ్", ta: "சூரிக்கு", mr: "झ्युरिक", gu: "ઝુરિચ", kn: "ಜ್ಯೂರಿಚ್", ml: "സൂറിച്ച്", pa: "ਜ਼ਿਊਰਿਖ", ur: "زیورخ", id: "Zürich", ms: "Zürich", th: "ซือริช", vi: "Zürich", km: "ហ្ស៊ូរិច", my: "ဇူးရစ်မြို့", zh: "苏黎世", ja: "チューリッヒ", ko: "취리히", el: "Ζυρίχη", bg: "Цюрих", cs: "Curych", sk: "Zürich", hu: "Zürich", ro: "Zürich", hr: "Zürich", sr: "Цирих", bs: "Zürich", sl: "Zürich", mk: "Цирих", et: "Zürich", lv: "Cīrihe", lt: "Ciurichas", da: "Zürich", fi: "Zürich", nb: "Zürich", sv: "Zürich", ca: "Zuric", gl: "Zürich", eu: "Zurich", af: "Zürich", sw: "Zürich", am: "ዙሪክ", ka: "ციურიხი", hy: "Ցյուրիխ", az: "Sürix", uz: "Syurix", kk: "Цюрих", tg: "Сюрих", tk: "Sýurih", ky: "Цюрих" }, timezone: "Europe/Zurich" },
  { city: "Geneva", translations: { fr: "Genève", es: "Ginebra", de: "Genf", pt: "Genebra", it: "Ginevra", nl: "Genève", pl: "Genewa", uk: "Женева", tr: "Cenevre", ru: "Женева", ar: "جنيف", he: "ז'נבה", fa: "ژنو", hi: "जिनेवा", bn: "জেনেভা", te: "జెనీవా", ta: "செனீவா", mr: "जिनिव्हा", gu: "જીનીવા", kn: "ಜಿನೀವಾ", ml: "ജനീവ", pa: "ਜਨੇਵਾ", ur: "جنیوا", id: "Jenewa", ms: "Geneva", th: "เจนีวา", vi: "Genève", km: "ហ្សឺណែវ", my: "ဂျနီဗာမြို့", zh: "日内瓦", ja: "ジュネーヴ", ko: "제네바", el: "Γενεύη", bg: "Женева", cs: "Ženeva", sk: "Ženeva", hu: "Genf", ro: "Geneva", hr: "Ženeva", sr: "Женева", bs: "Ženeva", sl: "Ženeva", mk: "Женева", et: "Genf", lv: "Ženēva", lt: "Ženeva", da: "Genève", fi: "Geneve", nb: "Genève", sv: "Genève", ca: "Ginebra", gl: "Xenebra", eu: "Geneva", af: "Genève", sw: "Geneva", am: "ጄኔቫ", ka: "ჟენევა", hy: "Ժնև", az: "Cenevrə", uz: "Jeneva", kk: "Женева", tg: "Женева", tk: "Ženewa", ky: "Женева" }, timezone: "Europe/Zurich" },
  { city: "Munich", translations: { fr: "Munich", es: "Múnich", de: "München", pt: "Munique", it: "Monaco di Baviera", nl: "München", pl: "Monachium", uk: "Мюнхен", tr: "Münih", ru: "Мюнхен", ar: "ميونخ", he: "מינכן", fa: "مونیخ", hi: "म्यूनिख", bn: "মিউনিখ", te: "మ్యూనిచ్", ta: "மியூனிக்", mr: "म्युनिक", gu: "મ્યુનિક", kn: "ಮ್ಯೂನಿಕ್", ml: "മ്യൂണിക്ക്", pa: "ਮਿਊਨਿਖ਼", ur: "میونخ", id: "München", ms: "Munich", th: "มิวนิก", vi: "München", km: "មុយនិច", my: "မြူးနစ်မြို့", zh: "慕尼黑", ja: "ミュンヘン", ko: "뮌헨", el: "Μόναχο", bg: "Мюнхен", cs: "Mnichov", sk: "Mníchov", hu: "München", ro: "München", hr: "München", sr: "Минхен", bs: "München", sl: "München", mk: "Минхен", et: "München", lv: "Minhene", lt: "Miunchenas", da: "München", fi: "München", nb: "München", sv: "München", ca: "Múnic", gl: "Múnic", eu: "Munich", af: "München", sw: "Munich", am: "ሙኒክ", ka: "მიუნხენი", hy: "Մյունխեն", az: "Münhen", uz: "Myunxen", kk: "Мюнхен", tg: "Мюнхен", tk: "Mýunhen", ky: "Мюнхен" }, timezone: "Europe/Berlin" },
  { city: "Milan", translations: { fr: "Milan", es: "Milán", de: "Mailand", pt: "Milão", it: "Milano", nl: "Milaan", pl: "Mediolan", uk: "Мілан", tr: "Milano", ru: "Милан", ar: "ميلانو", he: "מילאנו", fa: "میلان", hi: "मिलान", bn: "মিলান", te: "మిలన్", ta: "மிலான்", mr: "मिलान", gu: "મિલન", kn: "ಮಿಲಾನ್", ml: "മിലാൻ", pa: "ਮਿਲਾਨ", ur: "میلان", id: "Milan", ms: "Milan", th: "มิลาน", vi: "Milano", km: "មីឡាន", my: "မီလန်မြို့", zh: "米兰", ja: "ミラノ", ko: "밀라노", el: "Μιλάνο", bg: "Милано", cs: "Milán", sk: "Miláno", hu: "Milánó", ro: "Milano", hr: "Milano", sr: "Милано", bs: "Milano", sl: "Milano", mk: "Милано", et: "Milano", lv: "Milāna", lt: "Milanas", da: "Milano", fi: "Milano", nb: "Milano", sv: "Milano", ca: "Milà", gl: "Milán", eu: "Milan", af: "Milaan", sw: "Milan", am: "ሚላን", ka: "მილანი", hy: "Միլան", az: "Milan", uz: "Milan", kk: "Милан", tg: "Милан", tk: "Milan", ky: "Милан" }, timezone: "Europe/Rome" },
  { city: "Barcelona", translations: { fr: "Barcelone", es: "Barcelona", de: "Barcelona", pt: "Barcelona", it: "Barcellona", nl: "Barcelona", pl: "Barcelona", uk: "Барселона", tr: "Barselona", ru: "Барселона", ar: "برشلونة", he: "ברצלונה", fa: "بارسلون", hi: "बार्सिलोना", bn: "বার্সেলোনা", te: "బార్సిలోనా", ta: "பார்செலோனா", mr: "बार्सिलोना", gu: "બાર્સેલોના", kn: "ಬಾರ್ಸಿಲೋನಾ", ml: "ബാർസലോണ", pa: "ਬਾਰਸੀਲੋਨਾ", ur: "برشلونہ", id: "Barcelona", ms: "Barcelona", th: "บาร์เซโลนา", vi: "Barcelona", km: "បាសេឡូណា", my: "ဘာစီလိုနာမြို့", zh: "巴塞罗那", ja: "バルセロナ", ko: "바르셀로나", el: "Βαρκελώνη", bg: "Барселона", cs: "Barcelona", sk: "Barcelona", hu: "Barcelona", ro: "Barcelona", hr: "Barcelona", sr: "Барселона", bs: "Barcelona", sl: "Barcelona", mk: "Барселона", et: "Barcelona", lv: "Barselona", lt: "Barselona", da: "Barcelona", fi: "Barcelona", nb: "Barcelona", sv: "Barcelona", ca: "Barcelona", gl: "Barcelona", eu: "Bartzelona", af: "Barcelona", sw: "Barcelona", am: "ባርሴሎና", ka: "ბარსელონა", hy: "Բարսելոնա", az: "Barselona", uz: "Barselona", kk: "Барселона", tg: "Барселона", tk: "Barselona", ky: "Барселона" }, timezone: "Europe/Madrid" },
  { city: "Algiers", translations: { fr: "Alger", es: "Argel", de: "Algier", pt: "Argel", it: "Algeri", nl: "Algiers", pl: "Algier", uk: "Алжир", tr: "Cezayir", ru: "Алжир", ar: "الجزائر", he: "אלג'יר", fa: "الجزیره", hi: "अल्जीयर्स", bn: "আলজিয়ার্স", te: "అల్జీర్స్", ta: "அல்ஜியர்ஸ்", mr: "अल्जीयर्स", gu: "અલ્જિયર્સ", kn: "ಅಲ್ಜೀರ್ಸ್", ml: "അൽജിയേഴ്സ്", pa: "ਅਲਜੀਅਰਜ਼", ur: "الجزائر شہر", id: "Aljir", ms: "Algiers", th: "แอลเจียร์", vi: "Algiers", km: "អាល់ហ្សេរី", my: "အယ်လဂျီးယားမြို့", zh: "阿尔及尔", ja: "アルジェ", ko: "알제", el: "Αλγέρι", bg: "Алжир", cs: "Alžír", sk: "Alžír", hu: "Algír", ro: "Alger", hr: "Alžir", sr: "Алжир", bs: "Alžir", sl: "Alžir", mk: "Алжир", et: "Alžiir", lv: "Alžīra", lt: "Alžyras", da: "Algier", fi: "Alger", nb: "Alger", sv: "Alger", ca: "Alger", gl: "Alxer", eu: "Aljer", af: "Algiers", sw: "Algiers", am: "አልጀርስ", ka: "ალჟირი", hy: "Ալժիր", az: "Əlcəzair", uz: "Jazoir", kk: "Алжир", tg: "Алҷазоир", tk: "Alžir", ky: "Алжир" }, timezone: "Africa/Algiers" },
  { city: "Lagos", translations: { fr: "Lagos", es: "Lagos", de: "Lagos", pt: "Lagos", it: "Lagos", nl: "Lagos", pl: "Lagos", uk: "Лагос", tr: "Lagos", ru: "Лагос", ar: "لاغوس", he: "לאגוס", fa: "لاگوس", hi: "लागोस", bn: "লেগোস", te: "లాగోస్", ta: "லேகோஸ்", mr: "लागोस", gu: "લાગોસ", kn: "ಲಾಗೋಸ್", ml: "ലാഗോസ്", pa: "ਲਾਗੋਸ", ur: "لاگوس", id: "Lagos", ms: "Lagos", th: "เลกอส", vi: "Lagos", km: "ឡាហ្គោស", my: "လာဂို့စ်မြို့", zh: "拉各斯", ja: "ラゴス", ko: "라고스", el: "Λάγος", bg: "Лагос", cs: "Lagos", sk: "Lagos", hu: "Lagos", ro: "Lagos", hr: "Lagos", sr: "Лагос", bs: "Lagos", sl: "Lagos", mk: "Лагос", et: "Lagos", lv: "Lagosa", lt: "Lagosas", da: "Lagos", fi: "Lagos", nb: "Lagos", sv: "Lagos", ca: "Lagos", gl: "Lagos", eu: "Lagos", af: "Lagos", sw: "Lagos", am: "ሌጎስ", ka: "ლაგოსი", hy: "Լագոս", az: "Laqos", uz: "Lagos", kk: "Лагос", tg: "Лагос", tk: "Lagos", ky: "Лагос" }, timezone: "Africa/Lagos" },
  { city: "Tunis", translations: { fr: "Tunis", es: "Túnez", de: "Tunis", pt: "Tunes", it: "Tunisi", nl: "Tunis", pl: "Tunis", uk: "Туніс", tr: "Tunus", ru: "Тунис", ar: "تونس", he: "תוניס", fa: "تونس", hi: "ट्यूनिस", bn: "তিউনিস", te: "ట్యూనిస్", ta: "துனிஸ்", mr: "ट्युनिस", gu: "ટ્યુનિસ", kn: "ಟುನಿಸ್", ml: "ടുണിസ്", pa: "ਟਿਊਨਿਸ", ur: "تونس شہر", id: "Tunis", ms: "Tunis", th: "ตูนิส", vi: "Tunis", km: "ទុយនីស", my: "တျူနစ်မြို့", zh: "突尼斯", ja: "チュニス", ko: "튀니스", el: "Τύνιδα", bg: "Тунис", cs: "Tunis", sk: "Tunis", hu: "Tunisz", ro: "Tunis", hr: "Tunis", sr: "Тунис", bs: "Tunis", sl: "Tunis", mk: "Тунис", et: "Tunis", lv: "Tunisa", lt: "Tunisas", da: "Tunis", fi: "Tunis", nb: "Tunis", sv: "Tunis", ca: "Tunis", gl: "Tunes", eu: "Tunis", af: "Tunis", sw: "Tunis", am: "ቱኒስ", ka: "ტუნისი", hy: "Թունիս", az: "Tunis", uz: "Tunis", kk: "Тунис", tg: "Тунис", tk: "Tunis", ky: "Тунис" }, timezone: "Africa/Tunis" },

  // UTC+2 à UTC+4
  { city: "Athens", translations: { fr: "Athènes", es: "Atenas", de: "Athen", pt: "Atenas", it: "Atene", nl: "Athene", pl: "Ateny", uk: "Афіни", tr: "Atina", ru: "Афины", ar: "أثينا", he: "אתונה", fa: "آتن", hi: "एथेंस", bn: "এথেন্স", te: "ఏథెన్స్", ta: "ஏதென்ஸ்", mr: "अथेन्स", gu: "એથેન્સ", kn: "ಅಥೆನ್ಸ್", ml: "ഏഥൻസ്", pa: "ਏਥਨਜ਼", ur: "ایتھنز", id: "Athena", ms: "Athens", th: "เอเธนส์", vi: "Athens", km: "អាថែន", my: "အေသင်မြို့", zh: "雅典", ja: "アテネ", ko: "아테네", el: "Αθήνα", bg: "Атина", cs: "Atény", sk: "Atény", hu: "Athén", ro: "Atena", hr: "Atena", sr: "Атина", bs: "Atina", sl: "Atene", mk: "Атина", et: "Ateena", lv: "Atēnas", lt: "Atėnai", da: "Athen", fi: "Ateena", nb: "Athen", sv: "Aten", ca: "Atenes", gl: "Atenas", eu: "Atenas", af: "Athene", sw: "Athens", am: "አቴና", ka: "ათენი", hy: "Աթենք", az: "Afina", uz: "Afina", kk: "Афина", tg: "Афина", tk: "Afiny", ky: "Афины" }, timezone: "Europe/Athens" },
  { city: "Helsinki", translations: { fr: "Helsinki", es: "Helsinki", de: "Helsinki", pt: "Helsínquia", it: "Helsinki", nl: "Helsinki", pl: "Helsinki", uk: "Гельсінкі", tr: "Helsinki", ru: "Хельсинки", ar: "هلسنكي", he: "הלסינקי", fa: "هلسینکی", hi: "हेलसिंकी", bn: "হেলসিঙ্কি", te: "హెల్సింకి", ta: "ஹெல்சின்கி", mr: "हेलसिंकी", gu: "હેલસિંકી", kn: "ಹೆಲ್ಸಿಂಕಿ", ml: "ഹെൽസിങ്കി", pa: "ਹੈਲਸਿੰਕੀ", ur: "ہیلسنکی", id: "Helsinki", ms: "Helsinki", th: "เฮลซิงกิ", vi: "Helsinki", km: "ហែលស៊ីនគី", my: "ဟယ်လ်ဆင်ကီမြို့", zh: "赫尔辛基", ja: "ヘルシンキ", ko: "헬싱키", el: "Ελσίνκι", bg: "Хелзинки", cs: "Helsinky", sk: "Helsinki", hu: "Helsinki", ro: "Helsinki", hr: "Helsinki", sr: "Хелсинки", bs: "Helsinki", sl: "Helsinki", mk: "Хелсинки", et: "Helsingi", lv: "Helsinki", lt: "Helsinkis", da: "Helsinki", fi: "Helsinki", nb: "Helsingfors", sv: "Helsingfors", ca: "Hèlsinki", gl: "Helsinqui", eu: "Helsinki", af: "Helsinki", sw: "Helsinki", am: "ሄልሲንኪ", ka: "ჰელსინკი", hy: "Հելսինկի", az: "Helsinki", uz: "Xelsinki", kk: "Хельсинки", tg: "Ҳелсинкӣ", tk: "Helsinki", ky: "Хельсинки" }, timezone: "Europe/Helsinki" },
  { city: "Bucharest", translations: { fr: "Bucarest", es: "Bucarest", de: "Bukarest", pt: "Bucareste", it: "Bucarest", nl: "Boekarest", pl: "Bukareszt", uk: "Бухарест", tr: "Bükreş", ru: "Бухарест", ar: "بوخارست", he: "בוקרשט", fa: "بخارست", hi: "बुखारेस्ट", bn: "বুখারেস্ট", te: "బుకారెస్ట్", ta: "புக்கரெஸ்ட்", mr: "बुखारेस्ट", gu: "બુકારેસ્ટ", kn: "ಬುಚಾರೆಸ್ಟ್", ml: "ബുക്കാറെസ്റ്റ്", pa: "ਬੁਖ਼ਾਰੈਸਟ", ur: "بخارسٹ", id: "Bukares", ms: "Bucharest", th: "บูคาเรสต์", vi: "Bucharest", km: "ប៊ុយការ៉េស", my: "ဗူးခရက်မြို့", zh: "布加勒斯特", ja: "ブカレスト", ko: "부쿠레슈티", el: "Βουκουρέστι", bg: "Букурещ", cs: "Bukurešť", sk: "Bukurešť", hu: "Bukarest", ro: "București", hr: "Bukurešt", sr: "Букурешт", bs: "Bukurešt", sl: "Bukarešta", mk: "Букурешт", et: "Bukarest", lv: "Bukareste", lt: "Bukareštas", da: "Bukarest", fi: "Bukarest", nb: "București", sv: "Bukarest", ca: "Bucarest", gl: "Bucarest", eu: "Bukarest", af: "Boekarest", sw: "Bucharest", am: "ቡካረስት", ka: "ბუქარესტი", hy: "Բուխարեստ", az: "Buxarest", uz: "Buxarest", kk: "Бухарест", tg: "Бухарест", tk: "Buharest", ky: "Бухарест" }, timezone: "Europe/Bucharest" },
  { city: "Sofia", translations: { fr: "Sofia", es: "Sofía", de: "Sofia", pt: "Sófia", it: "Sofia", nl: "Sofia", pl: "Sofia", uk: "Софія", tr: "Sofya", ru: "София", ar: "صوفيا", he: "סופיה", fa: "صوفیه", hi: "सोफ़िया", bn: "সোফিয়া", te: "సోఫియా", ta: "சோஃபியா", mr: "सोफिया", gu: "સોફિયા", kn: "ಸೋಫಿಯಾ", ml: "സോഫിയ", pa: "ਸੋਫ਼ੀਆ", ur: "صوفیہ", id: "Sofia", ms: "Sofia", th: "โซเฟีย", vi: "Sofia", km: "សូហ្វីយ៉ា", my: "ဆိုဖီယာမြို့", zh: "索非亚", ja: "ソフィア", ko: "소피아", el: "Σόφια", bg: "София", cs: "Sofie", sk: "Sofia", hu: "Szófia", ro: "Sofia", hr: "Sofija", sr: "Софија", bs: "Sofija", sl: "Sofija", mk: "Софија", et: "Sofia", lv: "Sofija", lt: "Sofija", da: "Sofia", fi: "Sofia", nb: "Sofia", sv: "Sofia", ca: "Sofia", gl: "Sofía", eu: "Sofia", af: "Sofia", sw: "Sofia", am: "ሶፊያ", ka: "სოფია", hy: "Սոֆիա", az: "Sofiya", uz: "Sofiya", kk: "София", tg: "София", tk: "Sofiya", ky: "София" }, timezone: "Europe/Sofia" },
  { city: "Kyiv", translations: { fr: "Kyiv", es: "Kyiv", de: "Kyjiw", pt: "Kiev", it: "Kiev", nl: "Kyiv", pl: "Kijów", uk: "Київ", tr: "Kiev", ru: "Киев", ar: "كييف", he: "קייב", fa: "کی‌یف", hi: "कीव", bn: "কিয়েভ", te: "కీవ్", ta: "கீவ்", mr: "क्यीव", gu: "કિવ", kn: "ಕೀವ್", ml: "കീവ്", pa: "ਕੀਵ", ur: "کیئف", id: "Kyiv", ms: "Kyiv", th: "เคียฟ", vi: "Kyiv", km: "គីវ", my: "ကီးယက်မြို့", zh: "基辅", ja: "キーウ", ko: "키이우", el: "Κίεβο", bg: "Киев", cs: "Kyjev", sk: "Kyjev", hu: "Kijev", ro: "Kiev", hr: "Kijev", sr: "Кијев", bs: "Kijev", sl: "Kijev", mk: "Киев", et: "Kiiev", lv: "Kijiva", lt: "Kijevas", da: "Kyiv", fi: "Kiova", nb: "Kyiv", sv: "Kiev", ca: "Kíiv", gl: "Kíiv", eu: "Kiev", af: "Kijif", sw: "Kyiv", am: "ኪየቭ", ka: "კიევი", hy: "Կիև", az: "Kiyev", uz: "Kiyev", kk: "Киев", tg: "Киев", tk: "Kiýew", ky: "Киев" }, timezone: "Europe/Kyiv" },
  { city: "Cairo", translations: { fr: "Le Caire", es: "El Cairo", de: "Kairo", pt: "Cairo", it: "Il Cairo", nl: "Caïro", pl: "Kair", uk: "Каїр", tr: "Kahire", ru: "Каир", ar: "القاهرة", he: "קהיר", fa: "قاهره", hi: "काहिरा", bn: "কায়রো", te: "కైరో", ta: "கெய்ரோ", mr: "कैरो", gu: "કૈરો", kn: "ಕೈರೋ", ml: "കെയ്റോ", pa: "ਕਾਹਿਰਾ", ur: "قاہرہ", id: "Kairo", ms: "Kaherah", th: "ไคโร", vi: "Cairo", km: "គែរ", my: "ကိုင်ရိုမြို့", zh: "开罗", ja: "カイロ", ko: "카이로", el: "Κάιρο", bg: "Кайро", cs: "Káhira", sk: "Káhira", hu: "Kairó", ro: "Cairo", hr: "Kairo", sr: "Каиро", bs: "Kairo", sl: "Kairo", mk: "Каиро", et: "Kairo", lv: "Kaira", lt: "Kairas", da: "Kairo", fi: "Kairo", nb: "Kairo", sv: "Kairo", ca: "El Caire", gl: "O Cairo", eu: "Kairo", af: "Kaïro", sw: "Kairo", am: "ካይሮ", ka: "კაირო", hy: "Կահիրե", az: "Qahirə", uz: "Qohira", kk: "Каир", tg: "Қоҳира", tk: "Kair", ky: "Каир" }, timezone: "Africa/Cairo" },
  { city: "Jerusalem", translations: { fr: "Jérusalem", es: "Jerusalén", de: "Jerusalem", pt: "Jerusalém", it: "Gerusalemme", nl: "Jeruzalem", pl: "Jerozolima", uk: "Єрусалим", tr: "Kudüs", ru: "Иерусалим", ar: "القدس", he: "ירושלים", fa: "اورشلیم", hi: "यरूशलम", bn: "জেরুজালেম", te: "జెరూసలెం", ta: "எருசலேம்", mr: "जेरुसलेम", gu: "જેરૂસલેમ", kn: "ಜೆರುಸಲೆಮ್", ml: "യെരൂശലേം", pa: "ਯਰੂਸ਼ਲਮ", ur: "یروشلم", id: "Yerusalem", ms: "Baitulmaqdis", th: "เยรูซาเลม", vi: "Jerusalem", km: "យេរូសាឡឹម", my: "ဂျေရုဆလင်မြို့", zh: "耶路撒冷", ja: "エルサレム", ko: "예루살렘", el: "Ιερουσαλήμ", bg: "Йерусалим", cs: "Jeruzalém", sk: "Jeruzalem", hu: "Jeruzsálem", ro: "Ierusalim", hr: "Jeruzalem", sr: "Јерусалим", bs: "Jerusalem", sl: "Jeruzalem", mk: "Ерусалим", et: "Jeruusalemm", lv: "Jeruzaleme", lt: "Jeruzalė", da: "Jerusalem", fi: "Jerusalem", nb: "Jerusalem", sv: "Jerusalem", ca: "Jerusalem", gl: "Xerusalén", eu: "Jerusalem", af: "Jerusalem", sw: "Yerusalemu", am: "ኢየሩሳሌም", ka: "იერუსალიმი", hy: "Երուսաղեմ", az: "Qüds", uz: "Quddus", kk: "Иерусалим", tg: "Уршалим", tk: "Iýerusalim", ky: "Иерусалим" }, timezone: "Asia/Jerusalem" },
  { city: "Tel Aviv", translations: { fr: "Tel Aviv", es: "Tel Aviv", de: "Tel Aviv", pt: "Tel Aviv", it: "Tel Aviv", nl: "Tel Aviv", pl: "Tel Awiw", uk: "Тель-Авів", tr: "Tel Aviv", ru: "Тель-Авив", ar: "تل أبيب", he: "תל אביב", fa: "تل‌آویو", hi: "तेल अवीव", bn: "তেল আভিভ", te: "టెల్ అవీవ్", ta: "டெல் அவீவ்", mr: "तेल अवीव", gu: "તેલ અવીવ", kn: "ಟೆಲ್ ಅವೀವ್", ml: "ടെൽ അവീവ്", pa: "ਤੇਲ ਅਵੀਵ", ur: "تل ابیب", id: "Tel Aviv", ms: "Tel Aviv", th: "เทลอาวีฟ", vi: "Tel Aviv", km: "តែលអាវីវ", my: "တယ်လ်အဗစ်မြို့", zh: "特拉维夫", ja: "テルアビブ", ko: "텔아비브", el: "Τελ Αβίβ", bg: "Тел Авив", cs: "Tel Aviv", sk: "Tel Aviv", hu: "Tel-Aviv", ro: "Tel Aviv", hr: "Tel Aviv", sr: "Тел Авив", bs: "Tel Aviv", sl: "Tel Aviv", mk: "Тел Авив", et: "Tel Aviv", lv: "Telaviva", lt: "Tel Avivas", da: "Tel Aviv", fi: "Tel Aviv", nb: "Tel Aviv", sv: "Tel Aviv", ca: "Tel Aviv", gl: "Tel Aviv", eu: "Tel Aviv", af: "Tel Aviv", sw: "Tel Aviv", am: "ቴል አቪቭ", ka: "თელ-ავივი", hy: "Թել Ավիվ", az: "Təl-Əviv", uz: "Tel-Aviv", kk: "Тель-Авив", tg: "Тел-Авив", tk: "Tel-Awiw", ky: "Тель-Авив" }, timezone: "Asia/Jerusalem" },
  { city: "Amman", translations: { fr: "Amman", es: "Amán", de: "Amman", pt: "Amã", it: "Amman", nl: "Amman", pl: "Amman", uk: "Амман", tr: "Amman", ru: "Амман", ar: "عمان", he: "עמאן", fa: "امان", hi: "अम्मान", bn: "আম্মান", te: "అమ్మాన్", ta: "அம்மான்", mr: "अम्मान", gu: "અમ્માન", kn: "ಅಮ್ಮಾನ್", ml: "അമ്മാൻ", pa: "ਅੱਮਾਨ", ur: "عمان", id: "Amman", ms: "Amman", th: "อัมมาน", vi: "Amman", km: "អាម៉ាន", my: "အမ်မန်မြို့", zh: "安曼", ja: "アンマン", ko: "암만", el: "Αμμάν", bg: "Аман", cs: "Ammán", sk: "Ammán", hu: "Ammán", ro: "Amman", hr: "Aman", sr: "Аман", bs: "Aman", sl: "Aman", mk: "Аман", et: "Amman", lv: "Ammāna", lt: "Amanas", da: "Amman", fi: "Amman", nb: "Amman", sv: "Amman", ca: "Amman", gl: "Amán", eu: "Amman", af: "Amman", sw: "Amman", am: "አማን", ka: "ამანი", hy: "Ամման", az: "Əmman", uz: "Ammon", kk: "Амман", tg: "Аммон", tk: "Amman", ky: "Амман" }, timezone: "Asia/Amman" },
  { city: "Beirut", translations: { fr: "Beyrouth", es: "Beirut", de: "Beirut", pt: "Beirute", it: "Beirut", nl: "Beiroet", pl: "Bejrut", uk: "Бейрут", tr: "Beyrut", ru: "Бейрут", ar: "بيروت", he: "ביירות", fa: "بیروت", hi: "बेयरूत", bn: "বৈরুত", te: "బీరుట్", ta: "பெய்ரூட்", mr: "बैरूत", gu: "બેરુત", kn: "ಬೈರೂತ್", ml: "ബെയ്റൂട്ട്", pa: "ਬੈਰੂਤ", ur: "بیروت", id: "Beirut", ms: "Beirut", th: "เบรุต", vi: "Beirut", km: "បេរូត", my: "ဘေရွတ်မြို့", zh: "贝鲁特", ja: "ベイルート", ko: "베이루트", el: "Βηρυτός", bg: "Бейрут", cs: "Bejrút", sk: "Bejrút", hu: "Bejrút", ro: "Beirut", hr: "Bejrut", sr: "Бејрут", bs: "Bejrut", sl: "Bejrut", mk: "Бејрут", et: "Beirut", lv: "Beirūta", lt: "Beirutas", da: "Beirut", fi: "Beirut", nb: "Beirut", sv: "Beirut", ca: "Beirut", gl: "Beirut", eu: "Beirut", af: "Beiroet", sw: "Beirut", am: "ቤይሩት", ka: "ბეირუთი", hy: "Բեյրութ", az: "Beyrut", uz: "Bayrut", kk: "Бейрут", tg: "Байрут", tk: "Beýrut", ky: "Бейрут" }, timezone: "Asia/Beirut" },
  { city: "Damascus", translations: { fr: "Damas", es: "Damasco", de: "Damaskus", pt: "Damasco", it: "Damasco", nl: "Damascus", pl: "Damaszek", uk: "Дамаск", tr: "Şam", ru: "Дамаск", ar: "دمشق", he: "דמשק", fa: "دمشق", hi: "दमिश्क", bn: "দামেস্ক", te: "డమాస్కస్", ta: "திமிஷ்கு", mr: "दमास्कस", gu: "દમાસ્કસ", kn: "ಡಮಾಸ್ಕಸ್", ml: "ദമാസ്കസ്", pa: "ਦਮਸ਼ਕ", ur: "دمشق", id: "Damaskus", ms: "Damsyik", th: "ดามัสกัส", vi: "Damascus", km: "ដាម៉ាស", my: "ဒမားစကပ်မြို့", zh: "大马士革", ja: "ダマスカス", ko: "다마스쿠스", el: "Δαμασκός", bg: "Дамаск", cs: "Damašek", sk: "Damask", hu: "Damaszkusz", ro: "Damasc", hr: "Damask", sr: "Дамаск", bs: "Damask", sl: "Damask", mk: "Дамаск", et: "Damaskus", lv: "Damaska", lt: "Damaskas", da: "Damaskus", fi: "Damaskos", nb: "Damaskus", sv: "Damaskus", ca: "Damasc", gl: "Damasco", eu: "Damasko", af: "Damaskus", sw: "Dameski", am: "ደማስቆ", ka: "დამასკო", hy: "Դամասկոս", az: "Dəməşq", uz: "Damashq", kk: "Дамаск", tg: "Димишқ", tk: "Damask", ky: "Дамаск" }, timezone: "Asia/Damascus" },
  { city: "Johannesburg", translations: { fr: "Johannesburg", es: "Johannesburgo", de: "Johannesburg", pt: "Joanesburgo", it: "Johannesburg", nl: "Johannesburg", pl: "Johannesburg", uk: "Йоганнесбург", tr: "Johannesburg", ru: "Йоханнесбург", ar: "جوهانسبرغ", he: "יוהנסבורג", fa: "ژوهانسبورگ", hi: "जोहान्सबर्ग", bn: "জোহানেসবার্গ", te: "జోహన్నెస్‌బర్గ్", ta: "ஜோகானஸ்பேர்க்", mr: "जोहान्सबर्ग", gu: "જોહાનિસબર્ગ", kn: "ಜೋಹಾನ್ಸ್‌ಬರ್ಗ್", ml: "ജോഹന്നാസ്ബർഗ്", pa: "ਜੋਹਾਨਿਸਬਰਗ", ur: "جوہانسبرگ", id: "Johannesburg", ms: "Johannesburg", th: "โจฮันเนสเบิร์ก", vi: "Johannesburg", km: "ចូហានណេសប៊ឺក", my: "ဂျိုဟန္နက်စဗတ်မြို့", zh: "约翰内斯堡", ja: "ヨハネスブルグ", ko: "요하네스버그", el: "Γιοχάνεσμπουργκ", bg: "Йоханесбург", cs: "Johannesburg", sk: "Johannesburg", hu: "Johannesburg", ro: "Johannesburg", hr: "Johannesburg", sr: "Јоханезбург", bs: "Johannesburg", sl: "Johannesburg", mk: "Јоханесбург", et: "Johannesburg", lv: "Johannesburga", lt: "Johanesburgas", da: "Johannesburg", fi: "Johannesburg", nb: "Johannesburg", sv: "Johannesburg", ca: "Johannesburg", gl: "Xohanesburgo", eu: "Johannesburg", af: "Johannesburg", sw: "Johannesburg", am: "ጆሃንስበርግ", ka: "იოჰანესბურგი", hy: "Յոհաննեսբուրգ", az: "Yohannesburq", uz: "Yoxannesburg", kk: "Йоханнесбург", tg: "Йоҳаннесбург", tk: "Ýohannesburg", ky: "Йоханнесбург" }, timezone: "Africa/Johannesburg" },
  { city: "Cape Town", translations: { fr: "Le Cap", es: "Ciudad del Cabo", de: "Kapstadt", pt: "Cidade do Cabo", it: "Città del Capo", nl: "Kaapstad", pl: "Kapsztad", uk: "Кейптаун", tr: "Cape Town", ru: "Кейптаун", ar: "كيب تاون", he: "קייפטאון", fa: "کیپ‌تاون", hi: "केप टाउन", bn: "কেপ টাউন", te: "కేప్ టౌన్", ta: "கேப் டவுன்", mr: "केप टाउन", gu: "કેપ ટાઉન", kn: "ಕೇಪ್ ಟೌನ್", ml: "കേപ്പ് ടൗൺ", pa: "ਕੇਪ ਟਾਊਨ", ur: "کیپ ٹاؤن", id: "Cape Town", ms: "Cape Town", th: "เคปทาวน์", vi: "Cape Town", km: "ខេបថោន", my: "ကိပ်တောင်းမြို့", zh: "开普敦", ja: "ケープタウン", ko: "케이프타운", el: "Κέιπ Τάουν", bg: "Кейптаун", cs: "Kapské Město", sk: "Kapské Mesto", hu: "Fokváros", ro: "Cape Town", hr: "Cape Town", sr: "Кејптаун", bs: "Cape Town", sl: "Cape Town", mk: "Кејптаун", et: "Kaplinn", lv: "Keiptauna", lt: "Keiptaunas", da: "Kapstaden", fi: "Kapkaupunki", nb: "Cape Town", sv: "Kapstaden", ca: "Ciutat del Cap", gl: "Cidade do Cabo", eu: "Lurmutur Hiria", af: "Kaapstad", sw: "Cape Town", am: "ኬፕ ታውን", ka: "კეიპტაუნი", hy: "Քեյփթաուն", az: "Keyptaun", uz: "Keyptaun", kk: "Кейптаун", tg: "Кейптаун", tk: "Keýptaun", ky: "Кейптаун" }, timezone: "Africa/Johannesburg" },
  { city: "Istanbul", translations: { fr: "Istanbul", es: "Estambul", de: "Istanbul", pt: "Istambul", it: "Istanbul", nl: "Istanboel", pl: "Stambuł", uk: "Стамбул", tr: "İstanbul", ru: "Стамбул", ar: "إسطنبول", he: "איסטנבול", fa: "استانبول", hi: "इस्तांबुल", bn: "ইস্তাম্বুল", te: "ఇస్తాంబుల్", ta: "இஸ்தான்புல்", mr: "इस्तंबूल", gu: "ઇસ્તંબુલ", kn: "ಇಸ್ತಾನ್‌ಬುಲ್", ml: "ഇസ്താംബുൾ", pa: "ਇਸਤਾਂਬੁਲ", ur: "استنبول", id: "Istanbul", ms: "Istanbul", th: "อิสตันบูล", vi: "Istanbul", km: "អ៊ីស្តង់ប៊ុល", my: "အစ္စတန်ဘူလ်မြို့", zh: "伊斯坦布尔", ja: "イスタンブール", ko: "이스탄불", el: "Κωνσταντινούπολη", bg: "Истанбул", cs: "Istanbul", sk: "Istanbul", hu: "Isztambul", ro: "Istanbul", hr: "Istanbul", sr: "Истанбул", bs: "Istanbul", sl: "Carigrad", mk: "Истанбул", et: "İstanbul", lv: "Stambula", lt: "Stambulas", da: "Istanbul", fi: "Istanbul", nb: "Istanbul", sv: "Istanbul", ca: "Istanbul", gl: "Istambul", eu: "Istanbul", af: "Istanboel", sw: "Istanbul", am: "ኢስታንቡል", ka: "სტამბოლი", hy: "Ստամբուլ", az: "İstanbul", uz: "Istanbul", kk: "Ыстанбұл", tg: "Истанбул", tk: "Stambul", ky: "Стамбул" }, timezone: "Europe/Istanbul" },
  { city: "Moscow", translations: { fr: "Moscou", es: "Moscú", de: "Moskau", pt: "Moscovo", it: "Mosca", nl: "Moskou", pl: "Moskwa", uk: "Москва", tr: "Moskova", ru: "Москва", ar: "موسكو", he: "מוסקבה", fa: "مسکو", hi: "मास्को", bn: "মস্কো", te: "మాస్కో", ta: "மாஸ்கோ", mr: "मॉस्को", gu: "મોસ્કો", kn: "ಮಾಸ್ಕೋ", ml: "മോസ്കോ", pa: "ਮਾਸਕੋ", ur: "ماسکو", id: "Moskwa", ms: "Moscow", th: "มอสโก", vi: "Moskva", km: "ម៉ូស្គូ", my: "မော်စကိုမြို့", zh: "莫斯科", ja: "モスクワ", ko: "모스크바", el: "Μόσχα", bg: "Москва", cs: "Moskva", sk: "Moskva", hu: "Moszkva", ro: "Moscova", hr: "Moskva", sr: "Москва", bs: "Moskva", sl: "Moskva", mk: "Москва", et: "Moskva", lv: "Maskava", lt: "Maskva", da: "Moskva", fi: "Moskova", nb: "Moskva", sv: "Moskva", ca: "Moscou", gl: "Moscova", eu: "Mosku", af: "Moskou", sw: "Moscow", am: "ሞስኮ", ka: "მოსკოვი", hy: "Մոսկվա", az: "Moskva", uz: "Moskva", kk: "Мәскеу", tg: "Маскав", tk: "Moskwa", ky: "Москва" }, timezone: "Europe/Moscow" },
  { city: "St. Petersburg", translations: { fr: "Saint-Pétersbourg", es: "San Petersburgo", de: "Sankt Petersburg", pt: "São Petersburgo", it: "San Pietroburgo", nl: "Sint-Petersburg", pl: "Petersburg", uk: "Санкт-Петербург", tr: "Sankt Peterburg", ru: "Санкт-Петербург", ar: "سانت بطرسبرغ", he: "סנקט פטרבורג", fa: "سن پترزبورگ", hi: "सेंट पीटर्सबर्ग", bn: "সেন্ট পিটার্সবার্গ", te: "సెయింట్ పీటర్స్‌బర్గ్", ta: "சென் பீட்டர்ஸ்பேர்க்", mr: "सेंट पीटर्सबर्ग", gu: "સેન્ટ પીટર્સબર્ગ", kn: "ಸೇಂಟ್ ಪೀಟರ್ಸ್‌ಬರ್ಗ್", ml: "സെന്റ് പീറ്റേഴ്സ്ബർഗ്", pa: "ਸੇਂਟ ਪੀਟਰਸਬਰਗ", ur: "سینٹ پیٹرز برگ", id: "Sankt Peterburg", ms: "Saint Petersburg", th: "เซนต์ปีเตอส์เบิร์ก", vi: "Sankt-Peterburg", km: "សាំងភីធឺស្បឺក", my: "စိန့်ပီတာစဘတ်မြို့", zh: "圣彼得堡", ja: "サンクトペテルブルク", ko: "상트페테르부르크", el: "Αγία Πετρούπολη", bg: "Санкт Петербург", cs: "Petrohrad", sk: "Petrohrad", hu: "Szentpétervár", ro: "Sankt Petersburg", hr: "Sankt Peterburg", sr: "Санкт Петербург", bs: "Sankt Peterburg", sl: "Sankt Peterburg", mk: "Санкт Петербург", et: "Peterburi", lv: "Sanktpēterburga", lt: "Sankt Peterburgas", da: "Sankt Petersborg", fi: "Pietari", nb: "St. Petersburg", sv: "Sankt Petersburg", ca: "Sant Petersburg", gl: "San Petersburgo", eu: "San Petersburgo", af: "Sint Petersburg", sw: "Saint Petersburg", am: "ሴንት ፒተርስበርግ", ka: "სანქტ-პეტერბურგი", hy: "Սանկտ Պետերբուրգ", az: "Sankt-Peterburq", uz: "Sankt-Peterburg", kk: "Санкт-Петербург", tg: "Санкт-Петербург", tk: "Sankt-Peterburg", ky: "Санкт-Петербург" }, timezone: "Europe/Moscow" },
  { city: "Riyadh", translations: { fr: "Riyad", es: "Riad", de: "Riad", pt: "Riade", it: "Riyad", nl: "Riyad", pl: "Rijad", uk: "Ер-Ріяд", tr: "Riyad", ru: "Эр-Рияд", ar: "الرياض", he: "ריאד", fa: "ریاض", hi: "रियाध", bn: "রিয়াদ", te: "రియాద్", ta: "ரியாத்", mr: "रियाध", gu: "રિયાધ", kn: "ರಿಯಾದ್", ml: "റിയാദ്", pa: "ਰਿਆਦ", ur: "ریاض", id: "Riyadh", ms: "Riyadh", th: "รียาด", vi: "Riyadh", km: "រីយ៉ាដ", my: "ရီယာ့ဒ်မြို့", zh: "利雅得", ja: "リヤド", ko: "리야드", el: "Ριάντ", bg: "Рияд", cs: "Rijád", sk: "Rijád", hu: "Rijád", ro: "Riad", hr: "Rijad", sr: "Ријад", bs: "Rijad", sl: "Rijad", mk: "Ријад", et: "Ar-Riyāḑ", lv: "Rijāda", lt: "Rijadas", da: "Riyadh", fi: "Riad", nb: "Riyadh", sv: "Riyadh", ca: "Riad", gl: "Riad", eu: "Riad", af: "Riaad", sw: "Riyadh", am: "ሪያድ", ka: "ერ-რიადი", hy: "Էր Ռիադ", az: "Ər-Riyad", uz: "Ar-Riyod", kk: "Эр-Рияд", tg: "Риёз", tk: "Riad", ky: "Эр-Рияд" }, timezone: "Asia/Riyadh" },
  { city: "Baghdad", translations: { fr: "Bagdad", es: "Bagdad", de: "Bagdad", pt: "Bagdá", it: "Baghdad", nl: "Bagdad", pl: "Bagdad", uk: "Багдад", tr: "Bağdat", ru: "Багдад", ar: "بغداد", he: "בגדאד", fa: "بغداد", hi: "बग़दाद", bn: "বাগদাদ", te: "బాగ్దాద్", ta: "பாக்தாத்", mr: "बगदाद", gu: "બગદાદ", kn: "ಬಾಗ್ದಾದ್", ml: "ബാഗ്ദാദ്", pa: "ਬਗ਼ਦਾਦ", ur: "بغداد", id: "Bagdad", ms: "Baghdad", th: "แบกแดด", vi: "Baghdad", km: "បាកដាដ", my: "ဘဂ္ဂဒက်မြို့", zh: "巴格达", ja: "バグダッド", ko: "바그다드", el: "Βαγδάτη", bg: "Багдад", cs: "Bagdád", sk: "Bagdad", hu: "Bagdad", ro: "Bagdad", hr: "Bagdad", sr: "Багдад", bs: "Bagdad", sl: "Bagdad", mk: "Багдад", et: "Bagdad", lv: "Bagdāde", lt: "Bagdadas", da: "Bagdad", fi: "Bagdad", nb: "Bagdad", sv: "Bagdad", ca: "Bagdad", gl: "Bagdad", eu: "Bagdad", af: "Bagdad", sw: "Baghdad", am: "ባግዳድ", ka: "ბაღდადი", hy: "Բաղդադ", az: "Bağdad", uz: "Bagʻdod", kk: "Бағдат", tg: "Бағдод", tk: "Bagdat", ky: "Багдад" }, timezone: "Asia/Baghdad" },
  { city: "Kuwait City", translations: { fr: "Koweït", es: "Kuwait", de: "Kuwait-Stadt", pt: "Cidade do Kuwait", it: "Kuwait City", nl: "Koeweit (stad)", pl: "Kuwejt (miasto)", uk: "Ель-Кувейт", tr: "Kuveyt şehri", ru: "Эль-Кувейт", ar: "مدينة الكويت", he: "כווית סיטי", fa: "کویت (شهر)", hi: "कुवैत नगर", bn: "কুয়েত সিটি", te: "కువైట్ నగరం", ta: "குவைத் நகரம்", mr: "कुवेत शहर", gu: "કુવૈત શહેર", kn: "ಕುವೈತ್ ನಗರ", ml: "കുവൈറ്റ് സിറ്റി", pa: "ਕੁਵੈਤ ਸ਼ਹਿਰ", ur: "کویت شہر", id: "Kota Kuwait", ms: "Bandar Kuwait", th: "คูเวตซิตี", vi: "Thành phố Kuwait", km: "ទីក្រុងគុយវ៉ែត", my: "ကူဝိတ်မြို့တော်", zh: "科威特城", ja: "クウェート市", ko: "쿠웨이트 시티", el: "Πόλη του Κουβέιτ", bg: "Кувейт (град)", cs: "Kuvajt (město)", sk: "Kuvajt (mesto)", hu: "Kuvaitváros", ro: "Kuweit (oraș)", hr: "Kuvajt (grad)", sr: "Кувајт (град)", bs: "Kuwait City", sl: "Kuvajt (mesto)", mk: "Кувајт (град)", et: "Al-Kuwayt", lv: "Kuveita (pilsēta)", lt: "Kuveitas (miestas)", da: "Kuwait City", fi: "Kuwait City", nb: "Kuwait by", sv: "Kuwait City", ca: "Ciutat de Kuwait", gl: "Cidade de Kuwait", eu: "Kuwait Hiria", af: "Koeweitstad", sw: "Kuwait City", am: "ኩዌት ከተማ", ka: "ელ-ქუვეითი", hy: "Քուվեյթ (քաղաք)", az: "Küveyt (şəhər)", uz: "Quvayt shahri", kk: "Кувейт (қала)", tg: "Кувайт (шаҳр)", tk: "Kuweýt şäheri", ky: "Кувейт шаары" }, timezone: "Asia/Kuwait" },
  { city: "Doha", translations: { fr: "Doha", es: "Doha", de: "Doha", pt: "Doha", it: "Doha", nl: "Doha", pl: "Doha", uk: "Доха", tr: "Doha", ru: "Доха", ar: "الدوحة", he: "דוחה", fa: "دوحه", hi: "दोहा", bn: "দোহা", te: "దోహా", ta: "தோகா", mr: "दोहा", gu: "દોહા", kn: "ದೋಹಾ", ml: "ദോഹ", pa: "ਦੋਹਾ", ur: "دوحہ", id: "Doha", ms: "Doha", th: "โดฮา", vi: "Doha", km: "ដូហា", my: "ဒိုဟာမြို့", zh: "多哈", ja: "ドーハ", ko: "도하", el: "Ντόχα", bg: "Доха", cs: "Dauhá", sk: "Dauha", hu: "Doha", ro: "Doha", hr: "Doha", sr: "Доха", bs: "Doha", sl: "Doha", mk: "Доха", et: "Doha", lv: "Doha", lt: "Doha", da: "Doha", fi: "Doha", nb: "Doha", sv: "Doha", ca: "Doha", gl: "Doha", eu: "Doha", af: "Doha", sw: "Doha", am: "ዶሃ", ka: "დოჰა", hy: "Դոհա", az: "Doha", uz: "Doha", kk: "Доһа", tg: "Давҳа", tk: "Doha", ky: "Доха" }, timezone: "Asia/Qatar" },
  { city: "Nairobi", translations: { fr: "Nairobi", es: "Nairobi", de: "Nairobi", pt: "Nairóbi", it: "Nairobi", nl: "Nairobi", pl: "Nairobi", uk: "Найробі", tr: "Nairobi", ru: "Найроби", ar: "نيروبي", he: "ניירובי", fa: "نایروبی", hi: "नैरोबी", bn: "নাইরোবি", te: "నైరోబీ", ta: "நைரோபி", mr: "नैरोबी", gu: "નૈરોબી", kn: "ನೈರೋಬಿ", ml: "നൈറോബി", pa: "ਨੈਰੋਬੀ", ur: "نیروبی", id: "Nairobi", ms: "Nairobi", th: "ไนโรบี", vi: "Nairobi", km: "ណៃរ៉ូប៊ី", my: "နိုင်ရိုဘီမြို့", zh: "内罗毕", ja: "ナイロビ", ko: "나이로비", el: "Ναϊρόμπι", bg: "Найроби", cs: "Nairobi", sk: "Nairobi", hu: "Nairobi", ro: "Nairobi", hr: "Nairobi", sr: "Најроби", bs: "Nairobi", sl: "Nairobi", mk: "Најроби", et: "Nairobi", lv: "Nairobi", lt: "Nairobis", da: "Nairobi", fi: "Nairobi", nb: "Nairobi", sv: "Nairobi", ca: "Nairobi", gl: "Nairobi", eu: "Nairobi", af: "Nairobi", sw: "Nairobi", am: "ናይሮቢ", ka: "ნაირობი", hy: "Նայրոբի", az: "Nayrobi", uz: "Nayrobi", kk: "Найроби", tg: "Найробӣ", tk: "Naýrobi", ky: "Найроби" }, timezone: "Africa/Nairobi" },
  { city: "Addis Ababa", translations: { fr: "Addis-Abeba", es: "Adís Abeba", de: "Addis Abeba", pt: "Adis Abeba", it: "Addis Abeba", nl: "Addis Abeba", pl: "Addis Abeba", uk: "Аддис-Абеба", tr: "Addis Ababa", ru: "Аддис-Абеба", ar: "أديس أبابا", he: "אדיס אבבה", fa: "آدیس آبابا", hi: "अदीस अबाबा", bn: "আদ্দিস আবাবা", te: "అడ్డిస్ అబాబా", ta: "அடிஸ் அபாபா", mr: "अदिस अबाबा", gu: "અદીસ અબાબા", kn: "ಅಡಿಸ್ ಅಬಾಬಾ", ml: "അഡിസ് അബാബ", pa: "ਅਦੀਸ ਅਬਾਬਾ", ur: "ادیس ابابا", id: "Addis Ababa", ms: "Addis Ababa", th: "อาดดิสอาบาบา", vi: "Addis Ababa", km: "អាឌីសាបាបា", my: "အာဒစ်အာဘာဘာမြို့", zh: "亚的斯亚贝巴", ja: "アディスアベバ", ko: "아디스아바바", el: "Αντίς Αμπέμπα", bg: "Адис Абеба", cs: "Addis Abeba", sk: "Addis Abeba", hu: "Addisz-Abeba", ro: "Addis Abeba", hr: "Adis Abeba", sr: "Адис Абеба", bs: "Adis Abeba", sl: "Adis Abeba", mk: "Адис Абеба", et: "Addis Abeba", lv: "Adisabeba", lt: "Adis Abeba", da: "Addis Ababa", fi: "Addis Abeba", nb: "Addis Abeba", sv: "Addis Abeba", ca: "Addis Abeba", gl: "Adís Abeba", eu: "Addis Abeba", af: "Addis Abeba", sw: "Addis Ababa", am: "አዲስ አበባ", ka: "ადის-აბება", hy: "Ադիս Աբեբա", az: "Əddis-Əbəbə", uz: "Addis-Abeba", kk: "Аддис-Абеба", tg: "Аддис-Абеба", tk: "Addis-Abeba", ky: "Аддис-Абеба" }, timezone: "Africa/Addis_Ababa" },
  { city: "Tehran", translations: { fr: "Téhéran", es: "Teherán", de: "Teheran", pt: "Teerã", it: "Teheran", nl: "Teheran", pl: "Teheran", uk: "Тегеран", tr: "Tahran", ru: "Тегеран", ar: "طهران", he: "טהראן", fa: "تهران", hi: "तेहरान", bn: "তেহরান", te: "టెహ్రాన్", ta: "தெஹ்ரான்", mr: "तेहरान", gu: "તેહરાન", kn: "ತೆಹ್ರಾನ್", ml: "ടെഹ്റാൻ", pa: "ਤਹਿਰਾਨ", ur: "تہران", id: "Teheran", ms: "Tehran", th: "เตหะราน", vi: "Tehran", km: "តេអេរ៉ង់", my: "တီဟီရန်မြို့", zh: "德黑兰", ja: "テヘラン", ko: "테헤란", el: "Τεχεράνη", bg: "Техеран", cs: "Teherán", sk: "Teherán", hu: "Teherán", ro: "Teheran", hr: "Teheran", sr: "Техеран", bs: "Teheran", sl: "Teheran", mk: "Техеран", et: "Teheran", lv: "Teherāna", lt: "Teheranas", da: "Teheran", fi: "Teheran", nb: "Teheran", sv: "Teheran", ca: "Teheran", gl: "Teherán", eu: "Teheran", af: "Teheran", sw: "Tehran", am: "ቴህራን", ka: "თეირანი", hy: "Թեհրան", az: "Tehran", uz: "Tehron", kk: "Тегеран", tg: "Теҳрон", tk: "Tähran", ky: "Тегеран" }, timezone: "Asia/Tehran" },
  { city: "Dubai", translations: { fr: "Dubaï", es: "Dubái", de: "Dubai", pt: "Dubai", it: "Dubai", nl: "Dubai", pl: "Dubaj", uk: "Дубай", tr: "Dubai", ru: "Дубай", ar: "دبي", he: "דובאי", fa: "دبی", hi: "दुबई", bn: "দুবাই", te: "దుబాయ్", ta: "துபாய்", mr: "दुबई", gu: "દુબઈ", kn: "ದುಬೈ", ml: "ദുബായ്", pa: "ਦੁਬਈ", ur: "دبئی", id: "Dubai", ms: "Dubai", th: "ดูไบ", vi: "Dubai", km: "ឌូបៃ", my: "ဒူဘိုင်းမြို့", zh: "迪拜", ja: "ドバイ", ko: "두바이", el: "Ντουμπάι", bg: "Дубай", cs: "Dubaj", sk: "Dubaj", hu: "Dubaj", ro: "Dubai", hr: "Dubai", sr: "Дубаи", bs: "Dubai", sl: "Dubaj", mk: "Дубаи", et: "Dubai", lv: "Dubaija", lt: "Dubajus", da: "Dubai", fi: "Dubai", nb: "Dubai", sv: "Dubai", ca: "Dubai", gl: "Dubai", eu: "Dubai", af: "Doebai", sw: "Dubai", am: "ዱባይ", ka: "დუბაი", hy: "Դուբայ", az: "Dubay", uz: "Dubay", kk: "Дубай", tg: "Дубай", tk: "Dubaý", ky: "Дубай" }, timezone: "Asia/Dubai" },
  { city: "Abu Dhabi", translations: { fr: "Abu Dhabi", es: "Abu Dabi", de: "Abu Dhabi", pt: "Abu Dhabi", it: "Abu Dhabi", nl: "Abu Dhabi", pl: "Abu Zabi", uk: "Абу-Дабі", tr: "Abu Dabi", ru: "Абу-Даби", ar: "أبو ظبي", he: "אבו דאבי", fa: "ابوظبی", hi: "अबू धाबी", bn: "আবুধাবি", te: "అబు దాబి", ta: "அபுதாபி", mr: "अबू धाबी", gu: "અબુ ધાબી", kn: "ಅಬುಧಾಬಿ", ml: "അബുദാബി", pa: "ਆਬੂ ਧਾਬੀ", ur: "ابوظہبی", id: "Abu Dhabi", ms: "Abu Dhabi", th: "อาบูดาบี", vi: "Abu Dhabi", km: "អាប៊ូដាប៊ី", my: "အဘူဒါဘီမြို့", zh: "阿布扎比", ja: "アブダビ", ko: "아부다비", el: "Άμπου Ντάμπι", bg: "Абу Даби", cs: "Abú Zabí", sk: "Abú Zabí", hu: "Abu-Dzabi", ro: "Abu Dhabi", hr: "Abu Dhabi", sr: "Абу Даби", bs: "Abu Dhabi", sl: "Abu Dabi", mk: "Абу Даби", et: "Abu Dhabi", lv: "Abū Dabī", lt: "Abu Dabis", da: "Abu Dhabi", fi: "Abu Dhabi", nb: "Abu Dhabi", sv: "Abu Dhabi", ca: "Abu Dhabi", gl: "Abu Zabi", eu: "Abu Dhabi", af: "Aboe Dhabi", sw: "Abu Dhabi", am: "አቡ ዳቢ", ka: "აბუ-დაბი", hy: "Աբու Դաբի", az: "Əbu-Dabi", uz: "Abu-Dabi", kk: "Абу-Даби", tg: "Абу-Забӣ", tk: "Abu-Dabi", ky: "Абу-Даби" }, timezone: "Asia/Dubai" },
  { city: "Muscat", translations: { fr: "Mascate", es: "Mascate", de: "Maskat", pt: "Mascate", it: "Mascate", nl: "Muscat", pl: "Maskat", uk: "Маскат", tr: "Maskat", ru: "Маскат", ar: "مسقط", he: "מאסקט", fa: "مسقط", hi: "मस्क़त", bn: "মাস্কাট", te: "మస్కట్", ta: "மஸ்கட்", mr: "मस्कत", gu: "મસ્કત", kn: "ಮಸ್ಕತ್", ml: "മസ്കറ്റ്", pa: "ਮਸਕਟ", ur: "مسقط", id: "Muskat", ms: "Muscat", th: "มัสกัต", vi: "Muscat", km: "ម៉ាស្កាត", my: "မတ်စကတ်မြို့", zh: "马斯喀特", ja: "マスカット", ko: "무스카트", el: "Μουσκάτ", bg: "Маскат", cs: "Maskat", sk: "Maskat", hu: "Maszkat", ro: "Muscat", hr: "Muscat", sr: "Маскат", bs: "Muskat", sl: "Maskat", mk: "Маскат", et: "Masqaţ", lv: "Maskata", lt: "Maskatas", da: "Muscat", fi: "Masqat", nb: "Muskat", sv: "Muskat", ca: "Masqat", gl: "Mascate", eu: "Maskat", af: "Maskat", sw: "Muscat", am: "ሙስካት", ka: "მასკატი", hy: "Մասկատ", az: "Maskat", uz: "Maskat", kk: "Маскат", tg: "Масқат", tk: "Maskat", ky: "Маскат" }, timezone: "Asia/Muscat" },
  { city: "Baku", translations: { fr: "Bakou", es: "Bakú", de: "Baku", pt: "Baku", it: "Baku", nl: "Bakoe", pl: "Baku", uk: "Баку", tr: "Bakü", ru: "Баку", ar: "باكو", he: "באקו", fa: "باکو", hi: "बाकू", bn: "বাকু", te: "బాకు", ta: "பாகு", mr: "बाकू", gu: "બાકુ", kn: "ಬಾಕು", ml: "ബാക്കു", pa: "ਬਾਕੂ", ur: "باکو", id: "Baku", ms: "Baku", th: "บากู", vi: "Baku", km: "បាគូ", my: "ဘာကူမြို့", zh: "巴库", ja: "バクー", ko: "바쿠", el: "Μπακού", bg: "Баку", cs: "Baku", sk: "Baku", hu: "Baku", ro: "Baku", hr: "Baku", sr: "Баку", bs: "Baku", sl: "Baku", mk: "Баку", et: "Bakuu", lv: "Baku", lt: "Baku", da: "Baku", fi: "Baku", nb: "Baku", sv: "Baku", ca: "Bakú", gl: "Bakú", eu: "Baku", af: "Bakoe", sw: "Baku", am: "ባኩ", ka: "ბაქო", hy: "Բաքու", az: "Bakı", uz: "Boku", kk: "Баку", tg: "Боку", tk: "Baku", ky: "Баку" }, timezone: "Asia/Baku" },
  { city: "Tbilisi", translations: { fr: "Tbilissi", es: "Tiflis", de: "Tiflis", pt: "Tbilisi", it: "Tbilisi", nl: "Tbilisi", pl: "Tbilisi", uk: "Тбілісі", tr: "Tiflis", ru: "Тбилиси", ar: "تبليسي", he: "טביליסי", fa: "تفلیس", hi: "त्बिलिसी", bn: "তিবিলিসি", te: "టిబిలిసి", ta: "திபிலீசி", mr: "त्बिलिसी", gu: "તિબિલિસી", kn: "ಟಿಬಿಲಿಸಿ", ml: "ടിബിലിസി", pa: "ਤਬੀਲਿਸੀ", ur: "تبلیسی", id: "Tbilisi", ms: "Tbilisi", th: "ทบิลิซี", vi: "Tbilisi", km: "ធីប៊ីលីស៊ី", my: "တဗီလီစီမြို့", zh: "第比利斯", ja: "トビリシ", ko: "트빌리시", el: "Τιφλίδα", bg: "Тбилиси", cs: "Tbilisi", sk: "Tbilisi", hu: "Tbiliszi", ro: "Tbilisi", hr: "Tbilisi", sr: "Тбилиси", bs: "Tbilisi", sl: "Tbilisi", mk: "Тбилиси", et: "Thbilisi", lv: "Tbilisi", lt: "Tbilisis", da: "Tbilisi", fi: "Tbilisi", nb: "Tbilisi", sv: "Tbilisi", ca: "Tbilisi", gl: "Tbilisi", eu: "Tbilisi", af: "Tbilisi", sw: "Tbilisi", am: "ትብሊሲ", ka: "თბილისი", hy: "Թբիլիսի", az: "Tbilisi", uz: "Tbilisi", kk: "Тбилиси", tg: "Тифлис", tk: "Tbilisi", ky: "Тбилиси" }, timezone: "Asia/Tbilisi" },
  { city: "Yerevan", translations: { fr: "Erevan", es: "Ereván", de: "Jerewan", pt: "Erevan", it: "Erevan", nl: "Jerevan", pl: "Erywań", uk: "Єреван", tr: "Erivan", ru: "Ереван", ar: "يريفان", he: "ירוואן", fa: "ایروان", hi: "येरवान", bn: "ইয়েরেভান", te: "యెరెవాన్", ta: "யெரெவான்", mr: "येरेव्हान", gu: "યેરેવાન", kn: "ಯೆರೆವಾನ್", ml: "യെറിവാൻ", pa: "ਯੇਰੇਵਾਨ", ur: "یریوان", id: "Yerevan", ms: "Yerevan", th: "เยเรวาน", vi: "Yerevan", km: "យេរេវ៉ាន", my: "ယေရေဗန်မြို့", zh: "埃里温", ja: "エレバン", ko: "예레반", el: "Γερεβάν", bg: "Ереван", cs: "Jerevan", sk: "Jerevan", hu: "Jereván", ro: "Erevan", hr: "Erevan", sr: "Јереван", bs: "Erevan", sl: "Erevan", mk: "Ереван", et: "Jerevan", lv: "Erevāna", lt: "Jerevanas", da: "Jerevan", fi: "Jerevan", nb: "Jerevan", sv: "Jerevan", ca: "Erevan", gl: "Iereván", eu: "Erevan", af: "Jerewan", sw: "Yerevan", am: "ዬሬቫን", ka: "ერევანი", hy: "Երևան", az: "İrəvan", uz: "Yerevan", kk: "Ереван", tg: "Ереван", tk: "Ýerewan", ky: "Ереван" }, timezone: "Asia/Yerevan" },

  // UTC+4:30 à UTC+7
  { city: "Kabul", translations: { fr: "Kaboul", es: "Kabul", de: "Kabul", pt: "Cabul", it: "Kabul", nl: "Kaboel", pl: "Kabul", uk: "Кабул", tr: "Kabil", ru: "Кабул", ar: "كابل", he: "קאבול", fa: "کابل", hi: "काबुल", bn: "কাবুল", te: "కాబూల్", ta: "காபூல்", mr: "काबुल", gu: "કાબુલ", kn: "ಕಾಬೂಲ್", ml: "കാബൂൾ", pa: "ਕਾਬੁਲ", ur: "کابل", id: "Kabul", ms: "Kabul", th: "คาบูล", vi: "Kabul", km: "កាប៊ុល", my: "ကာဘူးလ်မြို့", zh: "喀布尔", ja: "カブール", ko: "카불", el: "Καμπούλ", bg: "Кабул", cs: "Kábul", sk: "Kábul", hu: "Kabul", ro: "Kabul", hr: "Kabul", sr: "Кабул", bs: "Kabul", sl: "Kabul", mk: "Кабул", et: "Kabul", lv: "Kabula", lt: "Kabulas", da: "Kabul", fi: "Kabul", nb: "Kabul", sv: "Kabul", ca: "Kabul", gl: "Cabul", eu: "Kabul", af: "Kaboel", sw: "Kabul", am: "ካቡል", ka: "ქაბული", hy: "Քաբուլ", az: "Kabil", uz: "Kobul", kk: "Кабул", tg: "Кобул", tk: "Kabul", ky: "Кабул" }, timezone: "Asia/Kabul" },
  { city: "Karachi", translations: { fr: "Karachi", es: "Karachi", de: "Karachi", pt: "Carachi", it: "Karachi", nl: "Karachi", pl: "Karaczi", uk: "Карачі", tr: "Karaçi", ru: "Карачи", ar: "كراتشي", he: "קראצ'י", fa: "کراچی", hi: "कराची", bn: "করাচী", te: "కరాచీ", ta: "கராச்சி", mr: "कराची", gu: "કરાચી", kn: "ಕರಾಚಿ", ml: "കറാച്ചി", pa: "ਕਰਾਚੀ", ur: "کراچی", id: "Karachi", ms: "Karachi", th: "การาจี", vi: "Karachi", km: "ការ៉ាជី", my: "ကရာချိမြို့", zh: "卡拉奇", ja: "カラチ", ko: "카라치", el: "Καράτσι", bg: "Карачи", cs: "Karāčí", sk: "Karáči", hu: "Karacsi", ro: "Karachi", hr: "Karachi", sr: "Карачи", bs: "Karači", sl: "Karači", mk: "Карачи", et: "Karachi", lv: "Karāči", lt: "Karačis", da: "Karachi", fi: "Karachi", nb: "Karachi", sv: "Karachi", ca: "Karachi", gl: "Karachi", eu: "Karatxi", af: "Karatsji", sw: "Karachi", am: "ካራቺ", ka: "კარაჩი", hy: "Կարաչի", az: "Kəraçi", uz: "Karachi", kk: "Карачи", tg: "Карочӣ", tk: "Karaçi", ky: "Карачи" }, timezone: "Asia/Karachi" },
  { city: "Tashkent", translations: { fr: "Tachkent", es: "Taskent", de: "Taschkent", pt: "Tasquente", it: "Tashkent", nl: "Tasjkent", pl: "Taszkent", uk: "Ташкент", tr: "Taşkent", ru: "Ташкент", ar: "طشقند", he: "טשקנט", fa: "تاشکند", hi: "ताशकन्द", bn: "তাশখন্দ", te: "తాష్కెంట్", ta: "தாஷ்கந்து", mr: "ताश्कंद", gu: "તાશ્કંદ", kn: "ತಾಷ್ಕೆಂಟ್", ml: "താഷ്കെന്റ്", pa: "ਤਾਸ਼ਕੰਤ", ur: "تاشقند", id: "Tashkent", ms: "Tashkent", th: "ทาชเคนต์", vi: "Tashkent", km: "តាស៊ីកេន", my: "တာ့ရှ်ကန်မြို့", zh: "塔什干", ja: "タシケント", ko: "타슈켄트", el: "Τασκένδη", bg: "Ташкент", cs: "Taškent", sk: "Taškent", hu: "Taskent", ro: "Tașkent", hr: "Taškent", sr: "Ташкент", bs: "Taškent", sl: "Taškent", mk: "Ташкент", et: "Taškent", lv: "Taškenta", lt: "Taškentas", da: "Tasjkent", fi: "Taškent", nb: "Tasjkent", sv: "Tasjkent", ca: "Taixkent", gl: "Taxkent", eu: "Taxkent", af: "Tasjkent", sw: "Tashkent", am: "ታሽኬንት", ka: "ტაშკენტი", hy: "Տաշքենդ", az: "Daşkənd", uz: "Toshkent", kk: "Ташкент", tg: "Тошканд", tk: "Daşkent", ky: "Ташкент" }, timezone: "Asia/Tashkent" },
  { city: "Islamabad", translations: { fr: "Islamabad", es: "Islamabad", de: "Islamabad", pt: "Islamabade", it: "Islamabad", nl: "Islamabad", pl: "Islamabad", uk: "Ісламабад", tr: "İslamabad", ru: "Исламабад", ar: "إسلام آباد", he: "אסלאמאבאד", fa: "اسلام‌آباد", hi: "इस्लामाबाद", bn: "ইসলামাবাদ", te: "ఇస్లామాబాద్", ta: "இஸ்லாமாபாத்", mr: "इस्लामाबाद", gu: "ઇસ્લામાબાદ", kn: "ಇಸ್ಲಾಮಾಬಾದ್", ml: "ഇസ്ലാമാബാദ്", pa: "ਇਸਲਾਮਾਬਾਦ", ur: "اسلام آباد", id: "Islamabad", ms: "Islamabad", th: "อิสลามาบาด", vi: "Islamabad", km: "អ៊ីស្លាម៉ាបាដ", my: "အစ္စလာမာဘတ်မြို့", zh: "伊斯兰堡", ja: "イスラマバード", ko: "이슬라마바드", el: "Ισλαμαμπάντ", bg: "Исламабад", cs: "Islámábád", sk: "Islamabad", hu: "Iszlámábád", ro: "Islamabad", hr: "Islamabad", sr: "Исламабад", bs: "Islamabad", sl: "Islamabad", mk: "Исламабад", et: "Islamabad", lv: "Islāmābāda", lt: "Islamabadas", da: "Islamabad", fi: "Islamabad", nb: "Islamabad", sv: "Islamabad", ca: "Islamabad", gl: "Islamabad", eu: "Islamabad", af: "Islamabad", sw: "Islamabad", am: "ኢስላማባድ", ka: "ისლამაბადი", hy: "Իսլամաբադ", az: "İslamabad", uz: "Islomobod", kk: "Исламабад", tg: "Исломобод", tk: "Yslamabat", ky: "Исламабад" }, timezone: "Asia/Karachi" },
  { city: "Lahore", translations: { fr: "Lahore", es: "Lahore", de: "Lahore", pt: "Lahore", it: "Lahore", nl: "Lahore", pl: "Lahaur", uk: "Лахор", tr: "Lahor", ru: "Лахор", ar: "لاهور", he: "לאהור", fa: "لاهور", hi: "लाहौर", bn: "লাহোর", te: "లాహోర్", ta: "லாகூர்", mr: "लाहोर", gu: "લાહોર", kn: "ಲಾಹೋರ್", ml: "ലാഹോർ", pa: "ਲਹੌਰ", ur: "لاہور", id: "Lahore", ms: "Lahore", th: "ลาฮอร์", vi: "Lahore", km: "ឡាហ័រ", my: "လာဟိုးမြို့", zh: "拉合尔", ja: "ラホール", ko: "라호르", el: "Λαχόρη", bg: "Лахор", cs: "Láhaur", sk: "Láhaur", hu: "Lahor", ro: "Lahore", hr: "Lahore", sr: "Лахор", bs: "Lahore", sl: "Lahore", mk: "Лахор", et: "Lahore", lv: "Lahora", lt: "Lahoras", da: "Lahore", fi: "Lahore", nb: "Lahore", sv: "Lahore", ca: "Lahore", gl: "Lahore", eu: "Lahore", af: "Lahore", sw: "Lahore", am: "ላሆር", ka: "ლაჰორი", hy: "Լահոր", az: "Lahor", uz: "Lahor", kk: "Лахор", tg: "Лоҳур", tk: "Lahor", ky: "Лахор" }, timezone: "Asia/Karachi" },
  { city: "Mumbai", translations: { fr: "Mumbai", es: "Bombay", de: "Mumbai", pt: "Bombaim", it: "Mumbai", nl: "Mumbai", pl: "Mumbaj", uk: "Мумбаї", tr: "Mumbai", ru: "Мумбаи", ar: "مومباي", he: "מומבאי", fa: "بمبئی", hi: "मुंबई", bn: "মুম্বাই", te: "ముంబై", ta: "மும்பை", mr: "मुंबई", gu: "મુંબઈ", kn: "ಮುಂಬೈ", ml: "മുംബൈ", pa: "ਮੁੰਬਈ", ur: "ممبئی", id: "Mumbai", ms: "Mumbai", th: "มุมไบ", vi: "Mumbai", km: "មុមបៃ", my: "မွမ်ဘိုင်းမြို့", zh: "孟买", ja: "ムンバイ", ko: "뭄바이", el: "Μουμπάι", bg: "Мумбай", cs: "Bombaj", sk: "Bombaj", hu: "Mumbai", ro: "Mumbai", hr: "Mumbai", sr: "Мумбај", bs: "Mumbai", sl: "Mumbaj", mk: "Мумбај", et: "Mumbai", lv: "Mumbaja", lt: "Mumbajus", da: "Mumbai", fi: "Mumbai", nb: "Mumbai", sv: "Mumbai", ca: "Bombai", gl: "Bombai", eu: "Mumbai", af: "Moembaai", sw: "Mumbai", am: "ሙምባይ", ka: "მუმბაი", hy: "Մումբայ", az: "Mumbai", uz: "Mumbay", kk: "Мумбаи", tg: "Мумбай", tk: "Mumbaý", ky: "Мумбаи" }, timezone: "Asia/Kolkata" },
  { city: "New Delhi", translations: { fr: "New Delhi", es: "Nueva Delhi", de: "Neu-Delhi", pt: "Nova Deli", it: "Nuova Delhi", nl: "New Delhi", pl: "Nowe Delhi", uk: "Нью-Делі", tr: "Yeni Delhi", ru: "Нью-Дели", ar: "نيودلهي", he: "ניו דלהי", fa: "دهلی نو", hi: "नई दिल्ली", bn: "নয়াদিল্লি", te: "న్యూఢిల్లీ", ta: "புது தில்லி", mr: "नवी दिल्ली", gu: "નવી દિલ્હી", kn: "ನವದೆಹಲಿ", ml: "ന്യൂ ഡെൽഹി", pa: "ਨਵੀਂ ਦਿੱਲੀ", ur: "نئی دہلی", id: "New Delhi", ms: "New Delhi", th: "นิวเดลี", vi: "New Delhi", km: "ញូវដេលី", my: "နယူးဒေလီမြို့", zh: "新德里", ja: "ニューデリー", ko: "뉴델리", el: "Νέο Δελχί", bg: "Ню Делхи", cs: "Nové Dillí", sk: "Nové Dillí", hu: "Újdelhi", ro: "New Delhi", hr: "New Delhi", sr: "Њу Делхи", bs: "New Delhi", sl: "New Delhi", mk: "Њу Делхи", et: "New Delhi", lv: "Ņūdeli", lt: "Naujasis Delis", da: "New Delhi", fi: "New Delhi", nb: "New Delhi", sv: "New Delhi", ca: "Nova Delhi", gl: "Nova Deli", eu: "New Delhi", af: "Nieu-Delhi", sw: "New Delhi", am: "ኒው ዴሊ", ka: "ნიუ-დელი", hy: "Նյու Դելի", az: "Yeni Dehli", uz: "Nyu-Dehli", kk: "Нью-Дели", tg: "Деҳлии Нав", tk: "Nýu-Deli", ky: "Нью-Дели" }, timezone: "Asia/Kolkata" },
  { city: "Kolkata", translations: { fr: "Calcutta", es: "Calcuta", de: "Kalkutta", pt: "Calcutá", it: "Calcutta", nl: "Kolkata", pl: "Kalkuta", uk: "Колката", tr: "Kalküta", ru: "Калькутта", ar: "كلكتا", he: "קולקטה", fa: "کلکته", hi: "कोलकाता", bn: "কলকাতা", te: "కోల్‌కతా", ta: "கொல்கத்தா", mr: "कोलकाता", gu: "કોલકાતા", kn: "ಕೋಲ್ಕತ್ತಾ", ml: "കൊൽക്കത്ത", pa: "ਕੋਲਕਾਤਾ", ur: "کولکاتا", id: "Kolkata", ms: "Kolkata", th: "โกลกาตา", vi: "Kolkata", km: "កុលកាតា", my: "ကလကတ္တားမြို့", zh: "加尔各答", ja: "コルカタ", ko: "콜카타", el: "Καλκούτα", bg: "Колката", cs: "Kalkata", sk: "Kalkata", hu: "Kolkata", ro: "Kolkata", hr: "Kolkata", sr: "Колката", bs: "Kolkata", sl: "Kolkata", mk: "Колката", et: "Kolkata", lv: "Kolkata", lt: "Kolkata", da: "Kolkata", fi: "Kalkutta", nb: "Kolkata", sv: "Kolkata", ca: "Calcuta", gl: "Calcuta", eu: "Kalkuta", af: "Kolkata", sw: "Kolkata", am: "ኮልካታ", ka: "კოლკატა", hy: "Կալկաթա", az: "Kəlkətə", uz: "Kalkutta", kk: "Калькутта", tg: "Калкутта", tk: "Kolkata", ky: "Калькутта" }, timezone: "Asia/Kolkata" },
  { city: "Chennai", translations: { fr: "Chennai", es: "Chennai", de: "Chennai", pt: "Chennai", it: "Chennai", nl: "Chennai", pl: "Ćennaj", uk: "Ченнаї", tr: "Chennai", ru: "Ченнаи", ar: "تشيناي", he: "צ'נאי", fa: "چنای", hi: "चेन्नई", bn: "চেন্নাই", te: "చెన్నై", ta: "சென்னை", mr: "चेन्नई", gu: "ચેન્નઈ", kn: "ಚೆನ್ನೈ", ml: "ചെന്നൈ", pa: "ਚੇਨਈ", ur: "چینائی", id: "Chennai", ms: "Chennai", th: "เจนไน", vi: "Chennai", km: "ចេនណៃ", my: "ချန်နိုင်းမြို့", zh: "金奈", ja: "チェンナイ", ko: "첸나이", el: "Τσεννάι", bg: "Ченай", cs: "Čennaí", sk: "Čennaj", hu: "Csennai", ro: "Chennai", hr: "Chennai", sr: "Ченај", bs: "Chennai", sl: "Čenaj", mk: "Ченај", et: "Chennai", lv: "Čennai", lt: "Čenajus", da: "Chennai", fi: "Chennai", nb: "Chennai", sv: "Chennai", ca: "Chennai", gl: "Chennai", eu: "Chennai", af: "Chennai", sw: "Chennai", am: "ቼናይ", ka: "ჩენაი", hy: "Չեննայ", az: "Çennai", uz: "Chennay", kk: "Ченнаи", tg: "Ченнаи", tk: "Çennaý", ky: "Ченнаи" }, timezone: "Asia/Kolkata" },
  { city: "Bangalore", translations: { fr: "Bangalore", es: "Bangalore", de: "Bangalore", pt: "Bangalore", it: "Bangalore", nl: "Bangalore", pl: "Bengaluru", uk: "Бангалор", tr: "Bangalore", ru: "Бангалор", ar: "بنغالور", he: "בנגלור", fa: "بنگلور", hi: "बंगलौर", bn: "বেঙ্গালুরু", te: "బెంగళూరు", ta: "பெங்களூரு", mr: "बंगळूर", gu: "બેંગલોર", kn: "ಬೆಂಗಳೂರು", ml: "ബെംഗളൂരു", pa: "ਬੰਗਲੌਰ", ur: "بنگلور", id: "Bangalore", ms: "Bangalore", th: "บังคาลอร์", vi: "Bangalore", km: "បាងហ្គាឡរ", my: "ဘန်ဂလိုမြို့", zh: "班加罗尔", ja: "バンガロール", ko: "벵갈루루", el: "Μπανγκαλόρ", bg: "Бангалор", cs: "Bengalúr", sk: "Bangalúr", hu: "Bengaluru", ro: "Bangalore", hr: "Bangalore", sr: "Бангалор", bs: "Bangalore", sl: "Bangalore", mk: "Бангалор", et: "Bangalore", lv: "Bengalūru", lt: "Bangaloras", da: "Bangalore", fi: "Bangalore", nb: "Bangalore", sv: "Bangalore", ca: "Bangalore", gl: "Bangalore", eu: "Bangalore", af: "Bangalore", sw: "Bangalore", am: "ባንጋሎር", ka: "ბანგალორი", hy: "Բանգալոր", az: "Banqalor", uz: "Bangalor", kk: "Бангалор", tg: "Бангалор", tk: "Bangalor", ky: "Бангалор" }, timezone: "Asia/Kolkata" },
  { city: "Hyderabad", translations: { fr: "Hyderabad", es: "Hyderabad", de: "Hyderabad", pt: "Hyderabad", it: "Hyderabad", nl: "Hyderabad", pl: "Hajdarabad", uk: "Гайдарабад", tr: "Haydarabad", ru: "Хайдарабад", ar: "حيدر آباد", he: "היידראבאד", fa: "حیدرآباد", hi: "हैदराबाद", bn: "হায়দ্রাবাদ", te: "హైదరాబాదు", ta: "ஐதராபாத்து", mr: "हैदराबाद", gu: "હૈદરાબાદ", kn: "ಹೈದರಾಬಾದ್", ml: "ഹൈദരാബാദ്", pa: "ਹੈਦਰਾਬਾਦ", ur: "حیدرآباد، دکن", id: "Hyderabad", ms: "Hyderabad", th: "ไฮเดอราบาด", vi: "Hyderabad", km: "ហាយដឺរ៉ាបាដ", my: "ဟိုက်ဒရာဘတ်မြို့", zh: "海得拉巴", ja: "ハイデラバード", ko: "하이데라바드", el: "Χαϊντεραμπάντ", bg: "Хайдерабад", cs: "Hajdarábád", sk: "Hajdarábad", hu: "Haidarábád", ro: "Hyderabad", hr: "Hyderabad", sr: "Хајдерабад", bs: "Hyderabad", sl: "Hajderabad", mk: "Хајдерабад", et: "Hyderabad", lv: "Haidarabāda", lt: "Haidarabadas", da: "Hyderabad", fi: "Hyderabad", nb: "Hyderabad", sv: "Hyderabad", ca: "Hyderabad", gl: "Hyderabad", eu: "Hyderabad", af: "Hyderabad", sw: "Hyderabad", am: "ሃይደራባድ", ka: "ჰაიდარაბადი", hy: "Հայդարաբադ", az: "Heydərabad", uz: "Haydarobod", kk: "Хайдарабад", tg: "Ҳайдаробод", tk: "Haýdarabat", ky: "Хайдарабад" }, timezone: "Asia/Kolkata" },
  { city: "Colombo", translations: { fr: "Colombo", es: "Colombo", de: "Colombo", pt: "Colombo", it: "Colombo", nl: "Colombo", pl: "Kolombo", uk: "Коломбо", tr: "Kolombo", ru: "Коломбо", ar: "كولومبو", he: "קולומבו", fa: "کلمبو", hi: "कोलंबो", bn: "কলম্বো", te: "కొలంబో", ta: "கொழும்பு", mr: "कोलंबो", gu: "કોલંબો", kn: "ಕೊಲಂಬೊ", ml: "കൊളംബോ", pa: "ਕੋਲੰਬੋ", ur: "کولمبو", id: "Kolombo", ms: "Colombo", th: "โคลัมโบ", vi: "Colombo", km: "កូឡុំបូ", my: "ကိုလံဘိုမြို့", zh: "科伦坡", ja: "コロンボ", ko: "콜롬보", el: "Κολόμπο", bg: "Коломбо", cs: "Kolombo", sk: "Kolombo", hu: "Colombo", ro: "Colombo", hr: "Colombo", sr: "Коломбо", bs: "Colombo", sl: "Kolombo", mk: "Коломбо", et: "Colombo", lv: "Kolombo", lt: "Kolombas", da: "Colombo", fi: "Colombo", nb: "Colombo", sv: "Colombo", ca: "Colombo", gl: "Colombo", eu: "Kolonbo", af: "Colombo", sw: "Colombo", am: "ኮሎምቦ", ka: "კოლომბო", hy: "Կոլոմբո", az: "Kolombo", uz: "Kolombo", kk: "Коломбо", tg: "Коломбо", tk: "Kolombo", ky: "Коломбо" }, timezone: "Asia/Colombo" },
  { city: "Kathmandu", translations: { fr: "Katmandou", es: "Katmandú", de: "Kathmandu", pt: "Catmandu", it: "Kathmandu", nl: "Kathmandu", pl: "Katmandu", uk: "Катманду", tr: "Katmandu", ru: "Катманду", ar: "كاتماندو", he: "קטמנדו", fa: "کاتماندو", hi: "काठमांडू", bn: "কাঠমান্ডু", te: "ఖాట్మండు", ta: "காட்மாண்டூ", mr: "काठमांडू", gu: "કાઠમંડુ", kn: "ಕಠ್ಮಂಡು", ml: "കാഠ്മണ്ഡു", pa: "ਕਾਠਮਾਂਡੂ", ur: "کاٹھمنڈو", id: "Kathmandu", ms: "Kathmandu", th: "กาฐมาณฑุ", vi: "Kathmandu", km: "កដ្ឋមណ្ឌូ", my: "ခတ္တမန္ဒူမြို့", zh: "加德满都", ja: "カトマンズ", ko: "카트만두", el: "Κατμαντού", bg: "Катманду", cs: "Káthmándú", sk: "Káthmandu", hu: "Katmandu", ro: "Kathmandu", hr: "Katmandu", sr: "Катманду", bs: "Katmandu", sl: "Katmandu", mk: "Катманду", et: "Katmandu", lv: "Katmandu", lt: "Katmandu", da: "Kathmandu", fi: "Kathmandu", nb: "Katmandu", sv: "Katmandu", ca: "Katmandú", gl: "Katmandú", eu: "Katmandu", af: "Katmandu", sw: "Kathmandu", am: "ካትማንዱ", ka: "კატმანდუ", hy: "Կատմանդու", az: "Katmandu", uz: "Katmandu", kk: "Катманду", tg: "Катманду", tk: "Katmandu", ky: "Катманду" }, timezone: "Asia/Kathmandu" },
  { city: "Dhaka", translations: { fr: "Dacca", es: "Daca", de: "Dhaka", pt: "Daca", it: "Dacca", nl: "Dhaka", pl: "Dhaka", uk: "Дакка", tr: "Dakka", ru: "Дакка", ar: "دكا", he: "דאקה", fa: "داکا", hi: "ढाका", bn: "ঢাকা", te: "ఢాకా", ta: "டாக்கா", mr: "ढाका", gu: "ઢાકા", kn: "ಢಾಕಾ", ml: "ധാക്ക", pa: "ਢਾਕਾ", ur: "ڈھاکہ", id: "Dhaka", ms: "Dhaka", th: "ธากา", vi: "Dhaka", km: "ដាកា", my: "ဒါကာမြို့", zh: "达卡", ja: "ダッカ", ko: "다카", el: "Ντάκα", bg: "Дака", cs: "Dháka", sk: "Dháka", hu: "Dakka", ro: "Dhaka", hr: "Dhaka", sr: "Дака", bs: "Dhaka", sl: "Daka", mk: "Дака", et: "Dhaka", lv: "Daka", lt: "Daka", da: "Dhaka", fi: "Dhaka", nb: "Dhaka", sv: "Dhaka", ca: "Dacca", gl: "Dacca", eu: "Dhaka", af: "Dhaka", sw: "Dhaka", am: "ዳካ", ka: "დაკა", hy: "Դաքքա", az: "Dəkkə", uz: "Dakka", kk: "Дакка", tg: "Дакка", tk: "Dakka", ky: "Дакка" }, timezone: "Asia/Dhaka" },
  { city: "Almaty", translations: { fr: "Almaty", es: "Almaty", de: "Almaty", pt: "Almaty", it: "Almaty", nl: "Almaty", pl: "Ałmaty", uk: "Алмати", tr: "Almatı", ru: "Алматы", ar: "ألماتي", he: "אלמטי", fa: "آلماتی", hi: "अल्माटी", bn: "আলমাটি", te: "అల్మాటీ", ta: "அல்மாத்தி", mr: "अल्माटी", gu: "અલ્માટી", kn: "ಅಲ್ಮಾಟಿ", ml: "അൽമാട്ടി", pa: "ਅਲਮਾਟੀ", ur: "الماتی", id: "Almaty", ms: "Almaty", th: "อัลมาตี", vi: "Almaty", km: "អាល់ម៉ាទី", my: "အယ်လ်မာတီမြို့", zh: "阿拉木图", ja: "アルマトイ", ko: "알마티", el: "Αλμάτι", bg: "Алмати", cs: "Almaty", sk: "Alma-Ata", hu: "Almati", ro: "Almatî", hr: "Almaty", sr: "Алмати", bs: "Almati", sl: "Almati", mk: "Алмати", et: "Almatõ", lv: "Almati", lt: "Almata", da: "Almaty", fi: "Almaty", nb: "Almaty", sv: "Almaty", ca: "Almati", gl: "Almati", eu: "Almaty", af: "Almaty", sw: "Almaty", am: "አልማቲ", ka: "ალმათი", hy: "Ալմաթի", az: "Almatı", uz: "Olmaota", kk: "Алматы", tg: "Алматӣ", tk: "Almaty", ky: "Алматы" }, timezone: "Asia/Almaty" },
  { city: "Novosibirsk", translations: { fr: "Novossibirsk", es: "Novosibirsk", de: "Nowosibirsk", pt: "Novosibirsk", it: "Novosibirsk", nl: "Novosibirsk", pl: "Nowosybirsk", uk: "Новосибірськ", tr: "Novosibirsk", ru: "Новосибирск", ar: "نوفوسيبيرسك", he: "נובוסיבירסק", fa: "نووسیبیرسک", hi: "नोवोसिबिर्स्क", bn: "নোভোসিবিরস্ক", te: "నోవోసిబిర్క్స్", ta: "நோவோசிபீர்ஸ்க்", mr: "नोवोसिबिर्स्क", gu: "નોવોસિબિર્સ્ક", kn: "ನೊವೊಸಿಬಿರ್ಸ್ಕ್", ml: "നോവോസിബിർസ്ക്", pa: "ਨੋਵੋਸਿਬਿਰਸਕ", ur: "نووسیبیرسک", id: "Novosibirsk", ms: "Novosibirsk", th: "โนโวซีบีสค์", vi: "Novosibirsk", km: "ណូវ៉ូស៊ីបៀរស្គ៍", my: "နိုဗိုဆီဗာစခ်မြို့", zh: "新西伯利亚", ja: "ノヴォシビルスク", ko: "노보시비르스크", el: "Νοβοσιμπίρσκ", bg: "Новосибирск", cs: "Novosibirsk", sk: "Novosibirsk", hu: "Novoszibirszk", ro: "Novosibirsk", hr: "Novosibirsk", sr: "Новосибирск", bs: "Novosibirsk", sl: "Novosibirsk", mk: "Новосибирск", et: "Novosibirsk", lv: "Novosibirska", lt: "Novosibirskas", da: "Novosibirsk", fi: "Novosibirsk", nb: "Novosibirsk", sv: "Novosibirsk", ca: "Novossibirsk", gl: "Novosibirsk", eu: "Novosibirsk", af: "Nowosibirsk", sw: "Novosibirsk", am: "ኖቮሲቢርስክ", ka: "ნოვოსიბირსკი", hy: "Նովոսիբիրսկ", az: "Novosibirsk", uz: "Novosibirsk", kk: "Новосібір", tg: "Новосибирск", tk: "Nowosibirsk", ky: "Новосибирск" }, timezone: "Asia/Novosibirsk" },
  { city: "Yangon", translations: { fr: "Rangoun", es: "Rangún", de: "Rangun", pt: "Rangum", it: "Yangon", nl: "Yangon", pl: "Rangun", uk: "Янгон", tr: "Yangon", ru: "Янгон", ar: "يانغون", he: "יאנגון", fa: "یانگون", hi: "यांगून", bn: "ইয়াঙ্গুন", te: "యాంగోన్", ta: "யங்கோன்", mr: "यांगून", gu: "યાંગોન", kn: "ಯಾಂಗೊನ್", ml: "യാംഗോൺ", pa: "ਯਾਂਗੂਨ", ur: "یانگون", id: "Yangon", ms: "Yangon", th: "ย่างกุ้ง", vi: "Yangon", km: "យ៉ាងហ្គូន", my: "ရန်ကုန်မြို့", zh: "仰光", ja: "ヤンゴン", ko: "양곤", el: "Γιανγκόν", bg: "Янгон", cs: "Rangún", sk: "Rangún", hu: "Yangon", ro: "Yangon", hr: "Yangon", sr: "Јангон", bs: "Yangon", sl: "Jangon", mk: "Јангон", et: "Yangon", lv: "Jangona", lt: "Jangonas", da: "Yangon", fi: "Yangon", nb: "Yangon", sv: "Rangoon", ca: "Yangon", gl: "Yangon", eu: "Yangon", af: "Jangoen", sw: "Yangon", am: "ያንጎን", ka: "იანგონი", hy: "Յանգոն", az: "Yanqon", uz: "Yangon", kk: "Янгон", tg: "Янгон", tk: "Ýangon", ky: "Янгон" }, timezone: "Asia/Yangon" },
  { city: "Bangkok", translations: { fr: "Bangkok", es: "Bangkok", de: "Bangkok", pt: "Banguecoque", it: "Bangkok", nl: "Bangkok", pl: "Bangkok", uk: "Бангкок", tr: "Bangkok", ru: "Бангкок", ar: "بانكوك", he: "בנגקוק", fa: "بانکوک", hi: "बैंकॉक", bn: "ব্যাংকক", te: "బ్యాంకాక్", ta: "பேங்காக்", mr: "बँकॉक", gu: "બેંગકોક", kn: "ಬ್ಯಾಂಕಾಕ್", ml: "ബാങ്കോക്ക്", pa: "ਬੈਂਕਾਕ", ur: "بینکاک", id: "Bangkok", ms: "Bangkok", th: "กรุงเทพมหานคร", vi: "Bangkok", km: "បាងកក", my: "ဘန်ကောက်မြို့", zh: "曼谷", ja: "バンコク", ko: "방콕", el: "Μπανγκόκ", bg: "Банкок", cs: "Bangkok", sk: "Bangkok", hu: "Bangkok", ro: "Bangkok", hr: "Bangkok", sr: "Бангкок", bs: "Bangkok", sl: "Bangkok", mk: "Бангкок", et: "Bangkok", lv: "Bangkoka", lt: "Bankokas", da: "Bangkok", fi: "Bangkok", nb: "Bangkok", sv: "Bangkok", ca: "Bangkok", gl: "Bangkok", eu: "Bangkok", af: "Bangkok", sw: "Bangkok", am: "ባንኮክ", ka: "ბანგკოკი", hy: "Բանգկոկ", az: "Banqkok", uz: "Bangkok", kk: "Бангкок", tg: "Бангкок", tk: "Bangkok", ky: "Бангкок" }, timezone: "Asia/Bangkok" },
  { city: "Jakarta", translations: { fr: "Jakarta", es: "Yakarta", de: "Jakarta", pt: "Jacarta", it: "Giacarta", nl: "Jakarta", pl: "Dżakarta", uk: "Джакарта", tr: "Cakarta", ru: "Джакарта", ar: "جاكرتا", he: "ג'קרטה", fa: "جاکارتا", hi: "जकार्ता", bn: "জাকার্তা", te: "జకార్తా", ta: "ஜகார்த்தா", mr: "जाकार्ता", gu: "જકાર્તા", kn: "ಜಕಾರ್ತಾ", ml: "ജക്കാർത്ത", pa: "ਜਕਾਰਤਾ", ur: "جکارتہ", id: "Jakarta", ms: "Jakarta", th: "จาการ์ตา", vi: "Jakarta", km: "ហ្សាកាតា", my: "ဂျကာတာမြို့", zh: "雅加达", ja: "ジャカルタ", ko: "자카르타", el: "Τζακάρτα", bg: "Джакарта", cs: "Jakarta", sk: "Jakarta", hu: "Jakarta", ro: "Jakarta", hr: "Jakarta", sr: "Џакарта", bs: "Jakarta", sl: "Džakarta", mk: "Џакарта", et: "Jakarta", lv: "Džakarta", lt: "Džakarta", da: "Jakarta", fi: "Jakarta", nb: "Jakarta", sv: "Jakarta", ca: "Jakarta", gl: "Iacarta", eu: "Jakarta", af: "Djakarta", sw: "Jakarta", am: "ጃካርታ", ka: "ჯაკარტა", hy: "Ջակարտա", az: "Cakarta", uz: "Jakarta", kk: "Джакарта", tg: "Ҷакарта", tk: "Jakarta", ky: "Жакарта" }, timezone: "Asia/Jakarta" },
  { city: "Hanoi", translations: { fr: "Hanoï", es: "Hanói", de: "Hanoi", pt: "Hanói", it: "Hanoi", nl: "Hanoi", pl: "Hanoi", uk: "Ханой", tr: "Hanoi", ru: "Ханой", ar: "هانوي", he: "האנוי", fa: "هانوی", hi: "हनोई", bn: "হ্যানয়", te: "హనోయ్", ta: "ஹனோய்", mr: "हनोई", gu: "હનોઈ", kn: "ಹನೋಯಿ", ml: "ഹാനോയ്", pa: "ਹਨੋਈ", ur: "ہنوئی", id: "Hanoi", ms: "Hanoi", th: "ฮานอย", vi: "Hà Nội", km: "ហាណូយ", my: "ဟနွိုင်းမြို့", zh: "河内", ja: "ハノイ", ko: "하노이", el: "Ανόι", bg: "Ханой", cs: "Hanoj", sk: "Hanoj", hu: "Hanoi", ro: "Hanoi", hr: "Hanoi", sr: "Ханој", bs: "Hanoi", sl: "Hanoj", mk: "Ханој", et: "Hanoi", lv: "Hanoja", lt: "Hanojus", da: "Hanoi", fi: "Hanoi", nb: "Hanoi", sv: "Hanoi", ca: "Hanoi", gl: "Hanoi", eu: "Hanoi", af: "Hanoi", sw: "Hanoi", am: "ሃኖይ", ka: "ჰანოი", hy: "Հանոյ", az: "Hanoy", uz: "Xanoy", kk: "Ханой", tg: "Ханой", tk: "Hanoý", ky: "Ханой" }, timezone: "Asia/Ho_Chi_Minh" },
  { city: "Ho Chi Minh City", translations: { fr: "Hô Chi Minh-Ville", es: "Ciudad Ho Chi Minh", de: "Ho-Chi-Minh-Stadt", pt: "Cidade de Ho Chi Minh", it: "Ho Chi Minh", nl: "Ho Chi Minhstad", pl: "Ho Chi Minh", uk: "Хошимін", tr: "Ho Chi Minh Kenti", ru: "Хошимин", ar: "مدينة هو تشي منه", he: "הו צ'י מין סיטי", fa: "هوشی‌مین", hi: "हो ची मिन्ह शहर", bn: "হো চি মিন সিটি", te: "హో చి మిన్ నగరం", ta: "ஹோ சி மின் நகரம்", mr: "हो चि मिन्ह सिटी", gu: "હો ચી મિન્હ સિટી", kn: "ಹೋ ಚಿ ಮಿನ್ ನಗರ", ml: "ഹോ ചി മിൻ സിറ്റി", pa: "ਹੋ ਚੀ ਮਿਨ ਸ਼ਹਿਰ", ur: "ہو چی من شہر", id: "Kota Ho Chi Minh", ms: "Bandar Raya Ho Chi Minh", th: "นครโฮจิมินห์", vi: "Thành phố Hồ Chí Minh", km: "ទីក្រុងហូជីមិញ", my: "ဟိုချီမင်းမြို့", zh: "胡志明市", ja: "ホーチミン市", ko: "호찌민 시", el: "Πόλη Χο Τσι Μιν", bg: "Хошимин", cs: "Ho Či Minovo Město", sk: "Hočiminovo Mesto", hu: "Ho Si Minh-város", ro: "Ho Și Min", hr: "Ho Ši Min", sr: "Хо Ши Мин", bs: "Ho Chi Minh City", sl: "Hošiminh", mk: "Хо Ши Мин", et: "Hồ Chí Minh", lv: "Hošimina", lt: "Hošiminas", da: "Ho Chi Minh City", fi: "Hồ Chí Minhin kaupunki", nb: "Ho Chi Minh-byen", sv: "Ho Chi Minh-staden", ca: "Ciutat Ho Chi Minh", gl: "Cidade Ho Chi Minh", eu: "Ho Chi Minh Hiria", af: "Ho Chi Minh-stad", sw: "Ho Chi Minh City", am: "ሆ ቺ ሚን ከተማ", ka: "ხოშიმინი", hy: "Հոշիմին", az: "Hoşimin", uz: "Xoshimin", kk: "Хошимин", tg: "Хошимин", tk: "Hoşimin", ky: "Хошимин" }, timezone: "Asia/Ho_Chi_Minh" },
  { city: "Phnom Penh", translations: { fr: "Phnom Penh", es: "Phnom Penh", de: "Phnom Penh", pt: "Phnom Penh", it: "Phnom Penh", nl: "Phnom Penh", pl: "Phnom Penh", uk: "Пномпень", tr: "Phnom Penh", ru: "Пномпень", ar: "بنوم بنه", he: "פנום פן", fa: "پنوم‌پن", hi: "नोम पेन्ह", bn: "নমপেন", te: "నామ్ పెన్", ta: "புனோம் பென்", mr: "फ्नोम पेन्ह", gu: "પનોમ પેન્હ", kn: "ನೋಮ್ ಪೆನ್", ml: "നോം പെൻ", pa: "ਪਨਾਮ ਪੈਨ", ur: "پنوم پن", id: "Phnom Penh", ms: "Phnom Penh", th: "พนมเปญ", vi: "Phnôm Pênh", km: "ភ្នំពេញ", my: "ဖနွမ်းပင်မြို့", zh: "金边", ja: "プノンペン", ko: "프놈펜", el: "Πνομ Πεν", bg: "Пном Пен", cs: "Phnompenh", sk: "Phnom Pénh", hu: "Phnompen", ro: "Phnom Penh", hr: "Phnom Penh", sr: "Пном Пен", bs: "Phnom Penh", sl: "Phnom Penh", mk: "Пном Пен", et: "Phnom Penh", lv: "Pnompeņa", lt: "Pnompenis", da: "Phnom Penh", fi: "Phnom Penh", nb: "Phnom Penh", sv: "Phnom Penh", ca: "Phnom Penh", gl: "Phnom Penh", eu: "Phnom Penh", af: "Phnom Penh", sw: "Phnom Penh", am: "ፕኖም ፔን", ka: "პნომპენი", hy: "Պնոմպեն", az: "Pnompen", uz: "Pnompen", kk: "Пномпень", tg: "Пномпен", tk: "Pnompen", ky: "Пномпень" }, timezone: "Asia/Phnom_Penh" },
  { city: "Krasnoyarsk", translations: { fr: "Krasnoïarsk", es: "Krasnoyarsk", de: "Krasnojarsk", pt: "Krasnoyarsk", it: "Krasnojarsk", nl: "Krasnojarsk", pl: "Krasnojarsk", uk: "Красноярськ", tr: "Krasnoyarsk", ru: "Красноярск", ar: "كراسنويارسك", he: "קרסנויארסק", fa: "کراسنویارسک", hi: "क्रास्नोयार्स्क", bn: "ক্রাসনোয়ারস্ক", te: "క్రాస్నోయార్స్క్", ta: "கிராஸ்னயார்ஸ்க்", mr: "क्रास्नोयार्स्क", gu: "ક્રાસ્નોયાર્સ્ક", kn: "ಕ್ರಾಸ್ನೊಯಾರ್ಸ್ಕ್", ml: "ക്രാസ്നോയാർസ്ക്", pa: "ਕ੍ਰਾਸਨੋਯਾਰਸਕ", ur: "کراسنویارسک", id: "Krasnoyarsk", ms: "Krasnoyarsk", th: "ครัสโนยาสค์", vi: "Krasnoyarsk", km: "ក្រាសណូយ៉ាស្គ៍", my: "ခရတ်စနိုရာစခ်မြို့", zh: "克拉斯诺亚尔斯克", ja: "クラスノヤルスク", ko: "크라스노야르스크", el: "Κρασνογιάρσκ", bg: "Красноярск", cs: "Krasnojarsk", sk: "Krasnojarsk", hu: "Krasznojarszk", ro: "Krasnoiarsk", hr: "Krasnojarsk", sr: "Краснојарск", bs: "Krasnojarsk", sl: "Krasnojarsk", mk: "Краснојарск", et: "Krasnojarsk", lv: "Krasnojarska", lt: "Krasnojarskas", da: "Krasnojarsk", fi: "Krasnojarsk", nb: "Krasnojarsk", sv: "Krasnojarsk", ca: "Krasnoiarsk", gl: "Krasnoiarsk", eu: "Krasnoiarsk", af: "Krasnojarsk", sw: "Krasnoyarsk", am: "ክራስኖያርስክ", ka: "კრასნოიარსკი", hy: "Կրասնոյարսկ", az: "Krasnoyarsk", uz: "Krasnoyarsk", kk: "Красноярск", tg: "Красноярск", tk: "Krasnoýarsk", ky: "Красноярск" }, timezone: "Asia/Krasnoyarsk" },

  // UTC+8 à UTC+14
  { city: "Singapore", translations: { fr: "Singapour", es: "Singapur", de: "Singapur", pt: "Singapura", it: "Singapore", nl: "Singapore", pl: "Singapur", uk: "Сінгапур", tr: "Singapur", ru: "Сингапур", ar: "سنغافورة", he: "סינגפור", fa: "سنگاپور", hi: "सिंगापुर", bn: "সিঙ্গাপুর", te: "సింగపూర్", ta: "சிங்கப்பூர்", mr: "सिंगापूर", gu: "સિંગાપુર", kn: "ಸಿಂಗಾಪುರ", ml: "സിംഗപ്പൂർ", pa: "ਸਿੰਗਾਪੁਰ", ur: "سنگاپور", id: "Singapura", ms: "Singapura", th: "สิงคโปร์", vi: "Singapore", km: "សិង្ហបុរី", my: "စင်ကာပူမြို့", zh: "新加坡", ja: "シンガポール", ko: "싱가포르", el: "Σιγκαπούρη", bg: "Сингапур", cs: "Singapur", sk: "Singapur", hu: "Szingapúr", ro: "Singapore", hr: "Singapur", sr: "Сингапур", bs: "Singapur", sl: "Singapur", mk: "Сингапур", et: "Singapur", lv: "Singapūra", lt: "Singapūras", da: "Singapore", fi: "Singapore", nb: "Singapore", sv: "Singapore", ca: "Singapur", gl: "Singapur", eu: "Singapur", af: "Singapoer", sw: "Singapuri", am: "ሲንጋፖር", ka: "სინგაპური", hy: "Սինգապուր", az: "Sinqapur", uz: "Singapur", kk: "Сингапур", tg: "Сингапур", tk: "Singapur", ky: "Сингапур" }, timezone: "Asia/Singapore" },
  { city: "Hong Kong", translations: { fr: "Hong Kong", es: "Hong Kong", de: "Hongkong", pt: "Hong Kong", it: "Hong Kong", nl: "Hongkong", pl: "Hongkong", uk: "Гонконг", tr: "Hong Kong", ru: "Гонконг", ar: "هونغ كونغ", he: "הונג קונג", fa: "هنگ کنگ", hi: "हाँग काँग", bn: "হংকং", te: "హాంగ్‌కాంగ్", ta: "ஹொங்கொங்", mr: "हाँग काँग", gu: "હોંગ કોંગ", kn: "ಹಾಂಗ್ ಕಾಂಗ್", ml: "ഹോങ്കോങ്", pa: "ਹਾਂਗਕਾਂਗ", ur: "ہانگ کانگ", id: "Hong Kong", ms: "Hong Kong", th: "ฮ่องกง", vi: "Hồng Kông", km: "ហុងកុង", my: "ဟောင်ကောင်မြို့", zh: "香港", ja: "香港", ko: "홍콩", el: "Χονγκ Κονγκ", bg: "Хонконг", cs: "Hongkong", sk: "Hongkong", hu: "Hongkong", ro: "Hong Kong", hr: "Hong Kong", sr: "Хонгконг", bs: "Hong Kong", sl: "Hongkong", mk: "Хонгконг", et: "Hongkong", lv: "Honkonga", lt: "Honkongas", da: "Hongkong", fi: "Hongkong", nb: "Hongkong", sv: "Hongkong", ca: "Hong Kong", gl: "Hong Kong", eu: "Hong Kong", af: "Hongkong", sw: "Hong Kong", am: "ሆንግ ኮንግ", ka: "ჰონგ-კონგი", hy: "Հոնկոնգ", az: "Honkonq", uz: "Gonkong", kk: "Гонконг", tg: "Ҳонгконг", tk: "Gonkong", ky: "Гонконг" }, timezone: "Asia/Hong_Kong" },
  { city: "Beijing", translations: { fr: "Pékin", es: "Pekín", de: "Peking", pt: "Pequim", it: "Pechino", nl: "Peking", pl: "Pekin", uk: "Пекін", tr: "Pekin", ru: "Пекин", ar: "بكين", he: "בייג'ינג", fa: "پکن", hi: "बीजिंग", bn: "বেইজিং", te: "బీజింగ్", ta: "பெய்ஜிங்", mr: "बीजिंग", gu: "બેઇજિંગ", kn: "ಬೀಜಿಂಗ್", ml: "ബെയ്ജിങ്", pa: "ਬੀਜਿੰਗ", ur: "بیجنگ", id: "Beijing", ms: "Beijing", th: "ปักกิ่ง", vi: "Bắc Kinh", km: "ប៉េកាំង", my: "ပေကျင်းမြို့", zh: "北京", ja: "北京", ko: "베이징", el: "Πεκίνο", bg: "Пекин", cs: "Peking", sk: "Peking", hu: "Peking", ro: "Beijing", hr: "Peking", sr: "Пекинг", bs: "Peking", sl: "Peking", mk: "Пекинг", et: "Peking", lv: "Pekina", lt: "Pekinas", da: "Beijing", fi: "Peking", nb: "Beijing", sv: "Peking", ca: "Pequín", gl: "Pequín", eu: "Pekin", af: "Beijing", sw: "Beijing", am: "ቤዪጂንግ", ka: "პეკინი", hy: "Պեկին", az: "Pekin", uz: "Pekin", kk: "Бейжің", tg: "Пекин", tk: "Pekin", ky: "Бээжин" }, timezone: "Asia/Shanghai" },
  { city: "Shanghai", translations: { fr: "Shanghai", es: "Shanghái", de: "Shanghai", pt: "Xangai", it: "Shanghai", nl: "Shanghai", pl: "Szanghaj", uk: "Шанхай", tr: "Şanghay", ru: "Шанхай", ar: "شانغهاي", he: "שאנגחאי", fa: "شانگهای", hi: "शंघाई", bn: "সাংহাই", te: "షాంఘై", ta: "சாங்காய்", mr: "शांघाय", gu: "શાંઘાઈ", kn: "ಶಾಂಘೈ", ml: "ഷാങ്ഹായ്", pa: "ਸ਼ੰਘਾਈ", ur: "شنگھائی", id: "Shanghai", ms: "Shanghai", th: "เซี่ยงไฮ้", vi: "Thượng Hải", km: "សៀងហៃ", my: "ရှန်ဟိုင်းမြို့", zh: "上海", ja: "上海", ko: "상하이", el: "Σαγκάη", bg: "Шанхай", cs: "Šanghaj", sk: "Šanghaj", hu: "Sanghaj", ro: "Shanghai", hr: "Šangaj", sr: "Шангај", bs: "Šangaj", sl: "Šanghaj", mk: "Шангај", et: "Shanghai", lv: "Šanhaja", lt: "Šanchajus", da: "Shanghai", fi: "Shanghai", nb: "Shanghai", sv: "Shanghai", ca: "Xangai", gl: "Xangai", eu: "Shanghai", af: "Sjanghai", sw: "Shanghai", am: "ሻንጋይ", ka: "შანხაი", hy: "Շանհայ", az: "Şanxay", uz: "Shanxay", kk: "Шанхай", tg: "Шанхай", tk: "Şanhaý", ky: "Шанхай" }, timezone: "Asia/Shanghai" },
  { city: "Taipei", translations: { fr: "Taipei", es: "Taipéi", de: "Taipeh", pt: "Taipé", it: "Taipei", nl: "Taipei", pl: "Tajpej", uk: "Тайбей", tr: "Taipei", ru: "Тайбэй", ar: "تايبيه", he: "טאיפיי", fa: "تایپه", hi: "ताइपे", bn: "তাইপেই", te: "తైపీ", ta: "தாய்பெய்", mr: "तैपै", gu: "તાઈપેઈ", kn: "ತೈಪೆ", ml: "തായ്പേയ്", pa: "ਤਾਇਪੇ", ur: "تائپے", id: "Taipei", ms: "Taipei", th: "ไทเป", vi: "Đài Bắc", km: "តៃប៉ិ", my: "ထိုင်ပေမြို့", zh: "台北", ja: "台北", ko: "타이베이", el: "Ταϊπέι", bg: "Тайпе", cs: "Tchaj-pej", sk: "Tchaj-pej", hu: "Tajpej", ro: "Taipei", hr: "Taipei", sr: "Тајпеј", bs: "Taipei", sl: "Tajpej", mk: "Тајпеј", et: "Taipei", lv: "Taipeja", lt: "Taipėjus", da: "Taipei", fi: "Taipei", nb: "Taipei", sv: "Taipei", ca: "Taipei", gl: "Taipei", eu: "Taipei", af: "Taipei", sw: "Taipei", am: "ታይፔ", ka: "ტაიპეი", hy: "Թայբեյ", az: "Taybey", uz: "Taybey", kk: "Тайбэй", tg: "Тайбэй", tk: "Taýpeý", ky: "Тайбэй" }, timezone: "Asia/Taipei" },
  { city: "Kuala Lumpur", translations: { fr: "Kuala Lumpur", es: "Kuala Lumpur", de: "Kuala Lumpur", pt: "Kuala Lumpur", it: "Kuala Lumpur", nl: "Kuala Lumpur", pl: "Kuala Lumpur", uk: "Куала-Лумпур", tr: "Kuala Lumpur", ru: "Куала-Лумпур", ar: "كوالالمبور", he: "קואלה לומפור", fa: "کوالالامپور", hi: "कुआलालम्पुर", bn: "কুয়ালালামপুর", te: "కౌలాలంపూర్", ta: "கோலாலம்பூர்", mr: "क्वालालंपूर", gu: "કુઆલા લમ્પુર", kn: "ಕೌಲಾಲಂಪುರ್", ml: "കോലാലമ്പൂർ", pa: "ਕੁਆਲਾ ਲੁੰਪੁਰ", ur: "کوالا لمپور", id: "Kuala Lumpur", ms: "Kuala Lumpur", th: "กัวลาลัมเปอร์", vi: "Kuala Lumpur", km: "គូឡាឡាំពួរ", my: "ကွာလာလမ်ပူမြို့", zh: "吉隆坡", ja: "クアラルンプール", ko: "쿠알라룸푸르", el: "Κουάλα Λουμπούρ", bg: "Куала Лумпур", cs: "Kuala Lumpur", sk: "Kuala Lumpur", hu: "Kuala Lumpur", ro: "Kuala Lumpur", hr: "Kuala Lumpur", sr: "Куала Лумпур", bs: "Kuala Lumpur", sl: "Kuala Lumpur", mk: "Куала Лумпур", et: "Kuala Lumpur", lv: "Kualalumpura", lt: "Kvala Lumpūras", da: "Kuala Lumpur", fi: "Kuala Lumpur", nb: "Kuala Lumpur", sv: "Kuala Lumpur", ca: "Kuala Lumpur", gl: "Kuala Lumpur", eu: "Kuala Lumpur", af: "Kuala Lumpur", sw: "Kuala Lumpur", am: "ኳላልምፑር", ka: "კუალა-ლუმპური", hy: "Կուալա Լումպուր", az: "Kuala-Lumpur", uz: "Kuala-Lumpur", kk: "Куала-Лумпур", tg: "Куала-Лумпур", tk: "Kuala-Lumpur", ky: "Куала-Лумпур" }, timezone: "Asia/Kuala_Lumpur" },
  { city: "Manila", translations: { fr: "Manille", es: "Manila", de: "Manila", pt: "Manila", it: "Manila", nl: "Manilla", pl: "Manila", uk: "Маніла", tr: "Manila", ru: "Манила", ar: "مانيلا", he: "מנילה", fa: "مانیل", hi: "मनिला", bn: "ম্যানিলা", te: "మనిలా", ta: "மணிலா", mr: "मनिला", gu: "મનિલા", kn: "ಮನಿಲಾ", ml: "മനില", pa: "ਮਨੀਲਾ", ur: "منیلا", id: "Manila", ms: "Manila", th: "มะนิลา", vi: "Manila", km: "ម៉ានីល", my: "မနီလာမြို့", zh: "马尼拉", ja: "マニラ", ko: "마닐라", el: "Μανίλα", bg: "Манила", cs: "Manila", sk: "Manila", hu: "Manila", ro: "Manila", hr: "Manila", sr: "Манила", bs: "Manila", sl: "Manila", mk: "Манила", et: "Manila", lv: "Manila", lt: "Manila", da: "Manila", fi: "Manila", nb: "Manila", sv: "Manila", ca: "Manila", gl: "Manila", eu: "Manila", af: "Manila", sw: "Manila", am: "ማኒላ", ka: "მანილა", hy: "Մանիլա", az: "Manila", uz: "Manila", kk: "Манила", tg: "Манила", tk: "Manila", ky: "Манила" }, timezone: "Asia/Manila" },
  { city: "Perth", translations: { fr: "Perth", es: "Perth", de: "Perth", pt: "Perth", it: "Perth", nl: "Perth", pl: "Perth", uk: "Перт", tr: "Perth", ru: "Перт", ar: "بيرث", he: "פרת'", fa: "پرت", hi: "पर्थ", bn: "পার্থ", te: "పెర్త్", ta: "பேர்த்", mr: "पर्थ", gu: "પર્થ", kn: "ಪರ್ತ್", ml: "പെർത്ത്", pa: "ਪਰਥ", ur: "پرتھ", id: "Perth", ms: "Perth", th: "เพิร์ท", vi: "Perth", km: "ភឺត", my: "ပါ့သ်မြို့", zh: "珀斯", ja: "パース", ko: "퍼스", el: "Περθ", bg: "Пърт", cs: "Perth", sk: "Perth", hu: "Perth", ro: "Perth", hr: "Perth", sr: "Перт", bs: "Perth", sl: "Perth", mk: "Перт", et: "Perth", lv: "Pērta", lt: "Pertas", da: "Perth", fi: "Perth", nb: "Perth", sv: "Perth", ca: "Perth", gl: "Perth", eu: "Perth", af: "Perth", sw: "Perth", am: "ፐርዝ", ka: "პერთი", hy: "Պերթ", az: "Pert", uz: "Pert", kk: "Перт", tg: "Перт", tk: "Pert", ky: "Перт" }, timezone: "Australia/Perth" },
  { city: "Irkutsk", translations: { fr: "Irkoutsk", es: "Irkutsk", de: "Irkutsk", pt: "Irkutsk", it: "Irkutsk", nl: "Irkoetsk", pl: "Irkuck", uk: "Іркутськ", tr: "İrkutsk", ru: "Иркутск", ar: "إيركوتسك", he: "אירקוטסק", fa: "ایرکوتسک", hi: "इरकुत्स्क", bn: "ইর্কুটস্ক", te: "ఇర్కుట్స్క్", ta: "இர்கூத்ஸ்க்", mr: "इरकुत्स्क", gu: "ઇર્કુત્સ્ક", kn: "ಇರ್ಕುಟ್ಸ್ಕ್", ml: "ഇർകുട്സ്ക്", pa: "ਇਰਕੁਤਸਕ", ur: "ارکتسک", id: "Irkutsk", ms: "Irkutsk", th: "อีร์คุตสค์", vi: "Irkutsk", km: "អ៊ីរគូតស្គ៍", my: "အီရ်ကူတ်စ်မြို့", zh: "伊尔库茨克", ja: "イルクーツク", ko: "이르쿠츠크", el: "Ιρκούτσκ", bg: "Иркутск", cs: "Irkutsk", sk: "Irkutsk", hu: "Irkutszk", ro: "Irkutsk", hr: "Irkutsk", sr: "Иркутск", bs: "Irkutsk", sl: "Irkutsk", mk: "Иркутск", et: "Irkutsk", lv: "Irkutska", lt: "Irkutskas", da: "Irkutsk", fi: "Irkutsk", nb: "Irkutsk", sv: "Irkutsk", ca: "Irkutsk", gl: "Irkutsk", eu: "Irkutsk", af: "Irkoetsk", sw: "Irkutsk", am: "ኢርኩትስክ", ka: "ირკუტსკი", hy: "Իրկուտսկ", az: "İrkutsk", uz: "Irkutsk", kk: "Иркутск", tg: "Иркутск", tk: "Irkutsk", ky: "Иркутск" }, timezone: "Asia/Irkutsk" },
  { city: "Ulaanbaatar", translations: { fr: "Oulan-Bator", es: "Ulán Bator", de: "Ulaanbaatar", pt: "Ulan Bator", it: "Ulan Bator", nl: "Ulaanbaatar", pl: "Ułan Bator", uk: "Улан-Батор", tr: "Ulan Batur", ru: "Улан-Батор", ar: "أولان باتور", he: "אולן בטור", fa: "اولان‌باتور", hi: "उलान बतोर", bn: "উলানবাটর", te: "ఉలాన్‌బాతర్", ta: "உலான் பத்தூர்", mr: "उलानबातर", gu: "ઉલાનબાતાર", kn: "ಉಲಾನ್‌ಬಾತರ್", ml: "ഉലാൻബാറ്റർ", pa: "ਉਲਾਨ ਬਾਤਰ", ur: "اولان‌ باتور", id: "Ulan Bator", ms: "Ulan Bator", th: "อูลานบาตาร์", vi: "Ulaanbaatar", km: "អ៊ូល៉ង់បាទ័រ", my: "ဦလန်ဘာတာမြို့", zh: "乌兰巴托", ja: "ウランバートル", ko: "울란바토르", el: "Ουλάν Μπατόρ", bg: "Улан Батор", cs: "Ulánbátar", sk: "Ulanbátar", hu: "Ulánbátor", ro: "Ulaanbaatar", hr: "Ulan Bator", sr: "Улан Батор", bs: "Ulan Bator", sl: "Ulan Bator", mk: "Улан Батор", et: "Ulaanbaatar", lv: "Ulanbatora", lt: "Ulan Batoras", da: "Ulan Bator", fi: "Ulan Bator", nb: "Ulan Bator", sv: "Ulan Bator", ca: "Ulan Bator", gl: "Ulán Bátor", eu: "Ulan Bator", af: "Ulaanbaatar", sw: "Ulan Bator", am: "ኡላን ባቶር", ka: "ულან-ბატორი", hy: "Ուլան Բատոր", az: "Ulan-Bator", uz: "Ulan-Bator", kk: "Ұланбатыр", tg: "Улан-Батор", tk: "Ulan-Bator", ky: "Улан-Баатыр" }, timezone: "Asia/Ulaanbaatar" },
  { city: "Seoul", translations: { fr: "Séoul", es: "Seúl", de: "Seoul", pt: "Seul", it: "Seul", nl: "Seoel", pl: "Seul", uk: "Сеул", tr: "Seul", ru: "Сеул", ar: "سول", he: "סיאול", fa: "سئول", hi: "सियोल", bn: "সিওল", te: "సియోల్", ta: "சியோல்", mr: "सोल", gu: "સિઓલ", kn: "ಸಿಯೋಲ್", ml: "സോൾ", pa: "ਸਿਓਲ", ur: "سیول", id: "Seoul", ms: "Seoul", th: "โซล", vi: "Seoul", km: "សេអ៊ូល", my: "ဆိုးလ်မြို့", zh: "首尔", ja: "ソウル", ko: "서울", el: "Σεούλ", bg: "Сеул", cs: "Soul", sk: "Soul", hu: "Szöul", ro: "Seul", hr: "Seul", sr: "Сеул", bs: "Seul", sl: "Seul", mk: "Сеул", et: "Soul", lv: "Seula", lt: "Seulas", da: "Seoul", fi: "Soul", nb: "Seoul", sv: "Seoul", ca: "Seül", gl: "Seúl", eu: "Seul", af: "Seoel", sw: "Seoul", am: "ሴኡል", ka: "სეული", hy: "Սեուլ", az: "Seul", uz: "Seul", kk: "Сеул", tg: "Сеул", tk: "Seul", ky: "Сеул" }, timezone: "Asia/Seoul" },
  { city: "Tokyo", translations: { fr: "Tokyo", es: "Tokio", de: "Tokio", pt: "Tóquio", it: "Tokyo", nl: "Tokio", pl: "Tokio", uk: "Токіо", tr: "Tokyo", ru: "Токио", ar: "طوكيو", he: "טוקיו", fa: "توکیو", hi: "टोक्यो", bn: "টোকিও", te: "టోక్యో", ta: "டோக்கியோ", mr: "तोक्यो", gu: "ટોક્યો", kn: "ಟೋಕಿಯೊ", ml: "ടോക്കിയോ", pa: "ਟੋਕੀਓ", ur: "توکیو", id: "Tokyo", ms: "Tokyo", th: "โตเกียว", vi: "Tokyo", km: "តូក្យូ", my: "တိုကျိုမြို့", zh: "东京", ja: "東京", ko: "도쿄", el: "Τόκιο", bg: "Токио", cs: "Tokio", sk: "Tokio", hu: "Tokió", ro: "Tokyo", hr: "Tokio", sr: "Токио", bs: "Tokio", sl: "Tokio", mk: "Токио", et: "Tōkyō", lv: "Tokija", lt: "Tokijas", da: "Tokyo", fi: "Tokio", nb: "Tokyo", sv: "Tokyo", ca: "Tòquio", gl: "Toquio", eu: "Tokio", af: "Tokio", sw: "Tokyo", am: "ቶክዮ", ka: "ტოკიო", hy: "Տոկիո", az: "Tokio", uz: "Tokio", kk: "Токио", tg: "Токио", tk: "Tokio", ky: "Токио" }, timezone: "Asia/Tokyo" },
  { city: "Osaka", translations: { fr: "Osaka", es: "Osaka", de: "Osaka", pt: "Osaka", it: "Osaka", nl: "Osaka", pl: "Osaka", uk: "Осака", tr: "Osaka", ru: "Осака", ar: "أوساكا", he: "אוסקה", fa: "اوساکا", hi: "ओसाका", bn: "ওসাকা", te: "ఒసాకా", ta: "ஒசாக்கா", mr: "ओसाका", gu: "ઓસાકા", kn: "ಒಸಾಕಾ", ml: "ഒസാക്ക", pa: "ਓਸਾਕਾ", ur: "اوساکا", id: "Osaka", ms: "Osaka", th: "โอซากะ", vi: "Osaka", km: "អូសាកា", my: "အိုဆာကာမြို့", zh: "大阪", ja: "大阪", ko: "오사카", el: "Οσάκα", bg: "Осака", cs: "Ósaka", sk: "Osaka", hu: "Oszaka", ro: "Osaka", hr: "Osaka", sr: "Осака", bs: "Osaka", sl: "Osaka", mk: "Осака", et: "Ōsaka", lv: "Osaka", lt: "Osaka", da: "Osaka", fi: "Osaka", nb: "Osaka", sv: "Osaka", ca: "Osaka", gl: "Osaca", eu: "Osaka", af: "Osaka", sw: "Osaka", am: "ኦሳካ", ka: "ოსაკა", hy: "Օսակա", az: "Osaka", uz: "Osaka", kk: "Осака", tg: "Осака", tk: "Osaka", ky: "Осака" }, timezone: "Asia/Tokyo" },
  { city: "Sapporo", translations: { fr: "Sapporo", es: "Sapporo", de: "Sapporo", pt: "Sapporo", it: "Sapporo", nl: "Sapporo", pl: "Sapporo", uk: "Саппоро", tr: "Sapporo", ru: "Саппоро", ar: "سابورو", he: "סאפורו", fa: "ساپورو", hi: "साप्पोरो", bn: "সাপ্পোরো", te: "సపోరో", ta: "சப்போரோ", mr: "सप्पोरो", gu: "સાપોરો", kn: "ಸಪೊರೊ", ml: "സപ്പോറോ", pa: "ਸਾਪੋਰੋ", ur: "ساپورو", id: "Sapporo", ms: "Sapporo", th: "ซัปโปโระ", vi: "Sapporo", km: "សាប៉ូរ៉ូ", my: "ဆပ်ပိုရိုမြို့", zh: "札幌", ja: "札幌", ko: "삿포로", el: "Σαππόρο", bg: "Сапоро", cs: "Sapporo", sk: "Sapporo", hu: "Szapporo", ro: "Sapporo", hr: "Sapporo", sr: "Сапоро", bs: "Sapporo", sl: "Saporo", mk: "Сапоро", et: "Sapporo", lv: "Saporo", lt: "Saporas", da: "Sapporo", fi: "Sapporo", nb: "Sapporo", sv: "Sapporo", ca: "Sapporo", gl: "Sapporo", eu: "Sapporo", af: "Sapporo", sw: "Sapporo", am: "ሳፖሮ", ka: "საპორო", hy: "Սապորո", az: "Sapporo", uz: "Sapporo", kk: "Саппоро", tg: "Саппоро", tk: "Sapporo", ky: "Саппоро" }, timezone: "Asia/Tokyo" },
  { city: "Yakutsk", translations: { fr: "Iakoutsk", es: "Yakutsk", de: "Jakutsk", pt: "Yakutsk", it: "Jakutsk", nl: "Jakoetsk", pl: "Jakuck", uk: "Якутськ", tr: "Yakutsk", ru: "Якутск", ar: "ياكوتسك", he: "יקוטסק", fa: "یاکوتسک", hi: "याकूत्स्क", bn: "ইয়াকুটস্ক", te: "యాకుట్స్క్", ta: "யாகுட்ஸ்க்", mr: "याकुत्स्क", gu: "યાકુત્સ્ક", kn: "ಯಾಕುಟ್ಸ್ಕ್", ml: "യാകുട്സ്ക്", pa: "ਯਾਕੂਤਸਕ", ur: "یاقوتسک", id: "Yakutsk", ms: "Yakutsk", th: "ยาคุตสค์", vi: "Yakutsk", km: "យ៉ាគូតស្គ៍", my: "ယာကုစ်မြို့", zh: "雅库茨克", ja: "ヤクーツク", ko: "야쿠츠크", el: "Γιακούτσκ", bg: "Якутск", cs: "Jakutsk", sk: "Jakutsk", hu: "Jakutszk", ro: "Yakutsk", hr: "Jakutsk", sr: "Јакутск", bs: "Jakutsk", sl: "Jakutsk", mk: "Јакутск", et: "Jakutsk", lv: "Jakutska", lt: "Jakutskas", da: "Jakutsk", fi: "Jakutsk", nb: "Jakutsk", sv: "Jakutsk", ca: "Iakutsk", gl: "Yakutsk", eu: "Jakutsk", af: "Jakoetsk", sw: "Yakutsk", am: "ያኩትስክ", ka: "იაკუტსკი", hy: "Յակուտսկ", az: "Yakutsk", uz: "Yakutsk", kk: "Якутск", tg: "Якутск", tk: "Ýakutsk", ky: "Якутск" }, timezone: "Asia/Yakutsk" },
  { city: "Pyongyang", translations: { fr: "Pyongyang", es: "Pyongyang", de: "Pjöngjang", pt: "Pyongyang", it: "Pyongyang", nl: "Pyongyang", pl: "Pjongjang", uk: "Пхеньян", tr: "Pyongyang", ru: "Пхеньян", ar: "بيونغ يانغ", he: "פיונגיאנג", fa: "پیونگ‌یانگ", hi: "प्योंगयांग", bn: "পিয়ং ইয়াং", te: "ప్యోంగ్యాంగ్", ta: "பியொங்யாங்", mr: "प्याँगयांग", gu: "પ્યોંગયાંગ", kn: "ಪ್ಯೊಂಗ್ಯಾಂಗ್", ml: "പ്യോങ്യാങ്", pa: "ਪਿਓਂਗਯਾਂਗ", ur: "پیانگ یانگ", id: "Pyongyang", ms: "Pyongyang", th: "เปียงยาง", vi: "Bình Nhưỡng", km: "ព្យុងយ៉ាង", my: "ပြုံယမ်းမြို့", zh: "平壤", ja: "平壌", ko: "평양", el: "Πιονγιάνγκ", bg: "Пхенян", cs: "Pchjongjang", sk: "Pchjongjang", hu: "Phenjan", ro: "Phenian", hr: "Pjongjang", sr: "Пјонгјанг", bs: "Pjongjang", sl: "Pjongjang", mk: "Пјонгјанг", et: "Pyongyang", lv: "Phenjana", lt: "Pchenjanas", da: "Pyongyang", fi: "Pjongjang", nb: "Pyongyang", sv: "Pyongyang", ca: "Pyongyang", gl: "Pyongyang", eu: "Piongiang", af: "Pjongjang", sw: "Pyongyang", am: "ፒዮንግያንግ", ka: "ფხენიანი", hy: "Փհենյան", az: "Pxenyan", uz: "Pxenyan", kk: "Пхеньян", tg: "Пхенян", tk: "Phenýan", ky: "Пхеньян" }, timezone: "Asia/Pyongyang" },
  { city: "Darwin", translations: { fr: "Darwin", es: "Darwin", de: "Darwin", pt: "Darwin", it: "Darwin", nl: "Darwin", pl: "Darwin", uk: "Дарвін", tr: "Darwin", ru: "Дарвин", ar: "داروين", he: "דרווין", fa: "داروین", hi: "डार्विन", bn: "ডারউইন", te: "డార్విన్", ta: "டார்வின்", mr: "डार्विन", gu: "ડાર્વિન", kn: "ಡಾರ್ವಿನ್", ml: "ഡാർവിൻ", pa: "ਡਾਰਵਿਨ", ur: "ڈارون", id: "Darwin", ms: "Darwin", th: "ดาร์วิน", vi: "Darwin", km: "ដាវីន", my: "ဒါဝင်မြို့", zh: "达尔文", ja: "ダーウィン", ko: "다윈", el: "Ντάργουιν", bg: "Даруин", cs: "Darwin", sk: "Darwin", hu: "Darwin", ro: "Darwin", hr: "Darwin", sr: "Дарвин", bs: "Darwin", sl: "Darwin", mk: "Дарвин", et: "Darwin", lv: "Dārvina", lt: "Darvinas", da: "Darwin", fi: "Darwin", nb: "Darwin", sv: "Darwin", ca: "Darwin", gl: "Darwin", eu: "Darwin", af: "Darwin", sw: "Darwin", am: "ዳርዊን", ka: "დარვინი", hy: "Դարվին", az: "Darvin", uz: "Darvin", kk: "Дарвин", tg: "Дарвин", tk: "Darwin", ky: "Дарвин" }, timezone: "Australia/Darwin" },
  { city: "Adelaide", translations: { fr: "Adélaïde", es: "Adelaida", de: "Adelaide", pt: "Adelaide", it: "Adelaide", nl: "Adelaide", pl: "Adelaide", uk: "Аделаїда", tr: "Adelaide", ru: "Аделаида", ar: "أديلايد", he: "אדלייד", fa: "آدلاید", hi: "एडिलेड", bn: "অ্যাডিলেড", te: "అడిలైడ్", ta: "அடிலெயிட்", mr: "ॲडलेड", gu: "એડિલેઇડ", kn: "ಅಡಿಲೇಡ್", ml: "അഡ്ലെയ്ഡ്", pa: "ਐਡੀਲੇਡ", ur: "ایڈیلیڈ", id: "Adelaide", ms: "Adelaide", th: "แอดิเลด", vi: "Adelaide", km: "អាដេឡែដ", my: "အက်ဒလိတ်မြို့", zh: "阿德莱德", ja: "アデレード", ko: "애들레이드", el: "Αδελαΐδα", bg: "Аделаида", cs: "Adelaide", sk: "Adelaide", hu: "Adelaide", ro: "Adelaide", hr: "Adelaide", sr: "Аделејд", bs: "Adelaide", sl: "Adelaide", mk: "Аделаида", et: "Adelaide", lv: "Adelaida", lt: "Adelaidė", da: "Adelaide", fi: "Adelaide", nb: "Adelaide", sv: "Adelaide", ca: "Adelaida", gl: "Adelaida", eu: "Adelaide", af: "Adelaide", sw: "Adelaide", am: "አደላይድ", ka: "ადელაიდა", hy: "Ադելաիդա", az: "Adelaida", uz: "Adelaida", kk: "Аделаида", tg: "Аделаида", tk: "Adelaida", ky: "Аделаида" }, timezone: "Australia/Adelaide" },
  { city: "Sydney", translations: { fr: "Sydney", es: "Sídney", de: "Sydney", pt: "Sydney", it: "Sydney", nl: "Sydney", pl: "Sydney", uk: "Сідней", tr: "Sidney", ru: "Сидней", ar: "سيدني", he: "סידני", fa: "سیدنی", hi: "सिडनी", bn: "সিডনি", te: "సిడ్నీ", ta: "சிட்னி", mr: "सिडनी", gu: "સિડની", kn: "ಸಿಡ್ನಿ", ml: "സിഡ്നി", pa: "ਸਿਡਨੀ", ur: "سڈنی", id: "Sydney", ms: "Sydney", th: "ซิดนีย์", vi: "Sydney", km: "ស៊ីដនី", my: "ဆစ်ဒနီမြို့", zh: "悉尼", ja: "シドニー", ko: "시드니", el: "Σίδνεϊ", bg: "Сидни", cs: "Sydney", sk: "Sydney", hu: "Sydney", ro: "Sydney", hr: "Sydney", sr: "Сиднеј", bs: "Sydney", sl: "Sydney", mk: "Сиднеј", et: "Sydney", lv: "Sidneja", lt: "Sidnėjus", da: "Sydney", fi: "Sydney", nb: "Sydney", sv: "Sydney", ca: "Sydney", gl: "Sydney", eu: "Sydney", af: "Sydney", sw: "Sydney", am: "ሲድኒ", ka: "სიდნეი", hy: "Սիդնեյ", az: "Sidney", uz: "Sidney", kk: "Сидней", tg: "Сидней", tk: "Sidneý", ky: "Сидней" }, timezone: "Australia/Sydney" },
  { city: "Melbourne", translations: { fr: "Melbourne", es: "Melbourne", de: "Melbourne", pt: "Melbourne", it: "Melbourne", nl: "Melbourne", pl: "Melbourne", uk: "Мельбурн", tr: "Melbourne", ru: "Мельбурн", ar: "ملبورن", he: "מלבורן", fa: "ملبورن", hi: "मेलबर्न", bn: "মেলবোর্ন", te: "మెల్బోర్న్", ta: "மெல்பேர்ண்", mr: "मेलबर्न", gu: "મેલબોર્ન", kn: "ಮೆಲ್ಬರ್ನ್", ml: "മെൽബൺ", pa: "ਮੈਲਬਰਨ", ur: "ملبورن", id: "Melbourne", ms: "Melbourne", th: "เมลเบิร์น", vi: "Melbourne", km: "មែលប៊ន", my: "မယ်လ်ဘုန်းမြို့", zh: "墨尔本", ja: "メルボルン", ko: "멜버른", el: "Μελβούρνη", bg: "Мелбърн", cs: "Melbourne", sk: "Melbourne", hu: "Melbourne", ro: "Melbourne", hr: "Melbourne", sr: "Мелбурн", bs: "Melbourne", sl: "Melbourne", mk: "Мелбурн", et: "Melbourne", lv: "Melburna", lt: "Melburnas", da: "Melbourne", fi: "Melbourne", nb: "Melbourne", sv: "Melbourne", ca: "Melbourne", gl: "Melbourne", eu: "Melbourne", af: "Melbourne", sw: "Melbourne", am: "ሜልበርን", ka: "მელბურნი", hy: "Մելբուռն", az: "Melburn", uz: "Melburn", kk: "Мельбурн", tg: "Мелбурн", tk: "Melburn", ky: "Мельбурн" }, timezone: "Australia/Melbourne" },
  { city: "Brisbane", translations: { fr: "Brisbane", es: "Brisbane", de: "Brisbane", pt: "Brisbane", it: "Brisbane", nl: "Brisbane", pl: "Brisbane", uk: "Брисбен", tr: "Brisbane", ru: "Брисбен", ar: "بريزبان", he: "בריזבן", fa: "بریزبن", hi: "ब्रिस्बेन", bn: "ব্রিসবেন", te: "బ్రిస్బేన్", ta: "பிரிஸ்பேன்", mr: "ब्रिस्बेन", gu: "બ્રિસ્બેન", kn: "ಬ್ರಿಸ್ಬೇನ್", ml: "ബ്രിസ്ബെയ്ൻ", pa: "ਬ੍ਰਿਸਬੇਨ", ur: "برسبین", id: "Brisbane", ms: "Brisbane", th: "บริสเบน", vi: "Brisbane", km: "ប្រ៊ីសបេន", my: "ဘရစ်စဘိန်းမြို့", zh: "布里斯班", ja: "ブリスベン", ko: "브리즈번", el: "Μπρίσμπεϊν", bg: "Бризбейн", cs: "Brisbane", sk: "Brisbane", hu: "Brisbane", ro: "Brisbane", hr: "Brisbane", sr: "Бризбејн", bs: "Brisbane", sl: "Brisbane", mk: "Бризбејн", et: "Brisbane", lv: "Brisbena", lt: "Brisbanas", da: "Brisbane", fi: "Brisbane", nb: "Brisbane", sv: "Brisbane", ca: "Brisbane", gl: "Brisbane", eu: "Brisbane", af: "Brisbane", sw: "Brisbane", am: "ብሪስቤን", ka: "ბრიზბენი", hy: "Բրիսբեն", az: "Brisben", uz: "Brisben", kk: "Брисбен", tg: "Брисбен", tk: "Brisben", ky: "Брисбен" }, timezone: "Australia/Brisbane" },
  { city: "Hobart", translations: { fr: "Hobart", es: "Hobart", de: "Hobart", pt: "Hobart", it: "Hobart", nl: "Hobart", pl: "Hobart", uk: "Гобарт", tr: "Hobart", ru: "Хобарт", ar: "هوبارت", he: "הובארט", fa: "هوبارت", hi: "होबार्ट", bn: "হোবার্ট", te: "హోబర్ట్", ta: "ஹோபார்ட்", mr: "होबार्ट", gu: "હોબાર્ટ", kn: "ಹೋಬಾರ್ಟ್", ml: "ഹോബാർട്ട്", pa: "ਹੋਬਾਰਟ", ur: "ہوبارٹ", id: "Hobart", ms: "Hobart", th: "โฮบาร์ต", vi: "Hobart", km: "ហូបាត", my: "ဟိုဘတ်မြို့", zh: "霍巴特", ja: "ホバート", ko: "호바트", el: "Χόμπαρτ", bg: "Хобарт", cs: "Hobart", sk: "Hobart", hu: "Hobart", ro: "Hobart", hr: "Hobart", sr: "Хобарт", bs: "Hobart", sl: "Hobart", mk: "Хобарт", et: "Hobart", lv: "Hobārta", lt: "Hobartas", da: "Hobart", fi: "Hobart", nb: "Hobart", sv: "Hobart", ca: "Hobart", gl: "Hobart", eu: "Hobart", af: "Hobart", sw: "Hobart", am: "ሆባርት", ka: "ჰობარტი", hy: "Հոբարթ", az: "Hobart", uz: "Xobart", kk: "Хобарт", tg: "Ҳобарт", tk: "Hobart", ky: "Хобарт" }, timezone: "Australia/Hobart" },
  { city: "Canberra", translations: { fr: "Canberra", es: "Canberra", de: "Canberra", pt: "Camberra", it: "Canberra", nl: "Canberra", pl: "Canberra", uk: "Канберра", tr: "Kanberra", ru: "Канберра", ar: "كانبرا", he: "קנברה", fa: "کانبرا", hi: "कैनबरा", bn: "ক্যানবেরা", te: "కాన్‌బెర్రా", ta: "கான்பரா", mr: "कॅनबेरा", gu: "કેનબેરા", kn: "ಕ್ಯಾನ್ಬೆರಾ", ml: "കാൻബറ", pa: "ਕੈਨਬਰਾ", ur: "کینبرا", id: "Canberra", ms: "Canberra", th: "แคนเบอร์รา", vi: "Canberra", km: "កង់បេរ៉ា", my: "ကင်ဘာရာမြို့", zh: "堪培拉", ja: "キャンベラ", ko: "캔버라", el: "Κανμπέρα", bg: "Канбера", cs: "Canberra", sk: "Canberra", hu: "Canberra", ro: "Canberra", hr: "Canberra", sr: "Канбера", bs: "Canberra", sl: "Canberra", mk: "Канбера", et: "Canberra", lv: "Kanbera", lt: "Kanbera", da: "Canberra", fi: "Canberra", nb: "Canberra", sv: "Canberra", ca: "Canberra", gl: "Camberra", eu: "Canberra", af: "Canberra", sw: "Canberra", am: "ካንቤራ", ka: "კანბერა", hy: "Կանբերա", az: "Kanberra", uz: "Kanberra", kk: "Канберра", tg: "Канберра", tk: "Kanberra", ky: "Канберра" }, timezone: "Australia/Sydney" },
  { city: "Vladivostok", translations: { fr: "Vladivostok", es: "Vladivostok", de: "Wladiwostok", pt: "Vladivostok", it: "Vladivostok", nl: "Vladivostok", pl: "Władywostok", uk: "Владивосток", tr: "Vladivostok", ru: "Владивосток", ar: "فلاديفوستوك", he: "ולדיווסטוק", fa: "ولادی‌وستوک", hi: "व्लादिवोस्तोक", bn: "ভ্লাদিভোস্টক", te: "వ్లాడివోస్టోక్", ta: "விளாதிவொஸ்டொக்", mr: "व्लादिवोस्तोक", gu: "વ્લાદિવોસ્તોક", kn: "ವ್ಲಾಡಿವೋಸ್ಟಾಕ್", ml: "വ്ലാഡിവോസ്റ്റോക്ക്", pa: "ਵਲਾਦੀਵੋਸਤੋਕ", ur: "ولادیوستوک", id: "Vladivostok", ms: "Vladivostok", th: "วลาดีวอสตอค", vi: "Vladivostok", km: "វ្លាឌីវ៉ូស្តុក", my: "ဗလာဒီဗော့စတော့မြို့", zh: "符拉迪沃斯托克", ja: "ウラジオストク", ko: "블라디보스토크", el: "Βλαδιβοστόκ", bg: "Владивосток", cs: "Vladivostok", sk: "Vladivostok", hu: "Vlagyivosztok", ro: "Vladivostok", hr: "Vladivostok", sr: "Владивосток", bs: "Vladivostok", sl: "Vladivostok", mk: "Владивосток", et: "Vladivostok", lv: "Vladivostoka", lt: "Vladivostokas", da: "Vladivostok", fi: "Vladivostok", nb: "Vladivostok", sv: "Vladivostok", ca: "Vladivostok", gl: "Vladivostok", eu: "Vladivostok", af: "Wladiwostok", sw: "Vladivostok", am: "ቭላዲቮስቶክ", ka: "ვლადივოსტოკი", hy: "Վլադիվոստոկ", az: "Vladivostok", uz: "Vladivostok", kk: "Владивосток", tg: "Владивосток", tk: "Wladiwostok", ky: "Владивосток" }, timezone: "Asia/Vladivostok" },
  { city: "Port Moresby", translations: { fr: "Port Moresby", es: "Port Moresby", de: "Port Moresby", pt: "Port Moresby", it: "Port Moresby", nl: "Port Moresby", pl: "Port Moresby", uk: "Порт-Морсбі", tr: "Port Moresby", ru: "Порт-Морсби", ar: "بورت مورسبي", he: "פורט מורסבי", fa: "پورت مورزبی", hi: "पोर्ट मोरेस्बी", bn: "পোর্ট মোর্সবি", te: "పోర్ట్ మోర్స్బీ", ta: "போர்ட் மோர்ஸ்பி", mr: "पोर्ट मॉरेस्बी", gu: "પોર્ટ મોરેસ્બી", kn: "ಪೋರ್ಟ್ ಮೊರೆಸ್ಬಿ", ml: "പോർട്ട് മോറെസ്ബി", pa: "ਪੋਰਟ ਮੋਰਸਬੀ", ur: "پورٹ مورسبی", id: "Port Moresby", ms: "Port Moresby", th: "พอร์ตมอร์สบี", vi: "Port Moresby", km: "ផតម៉ូរ៉េសប៊ី", my: "ပို့တ်မော့စဘီမြို့", zh: "莫尔兹比港", ja: "ポートモレスビー", ko: "포트모르즈비", el: "Πορτ Μόρεσμπι", bg: "Порт Морсби", cs: "Port Moresby", sk: "Port Moresby", hu: "Port Moresby", ro: "Port Moresby", hr: "Port Moresby", sr: "Порт Морзби", bs: "Port Moresby", sl: "Port Moresby", mk: "Порт Морсби", et: "Port Moresby", lv: "Portmorsbi", lt: "Port Morsbis", da: "Port Moresby", fi: "Port Moresby", nb: "Port Moresby", sv: "Port Moresby", ca: "Port Moresby", gl: "Port Moresby", eu: "Port Moresby", af: "Port Moresby", sw: "Port Moresby", am: "ፖርት ሞርስቢ", ka: "პორტ-მორზბი", hy: "Պորտ Մորսբի", az: "Port-Morsbi", uz: "Port-Morsbi", kk: "Порт-Морсби", tg: "Порт-Морсби", tk: "Port-Morsbi", ky: "Порт-Морсби" }, timezone: "Pacific/Port_Moresby" },
  { city: "Guam", translations: { fr: "Guam", es: "Guam", de: "Guam", pt: "Guam", it: "Guam", nl: "Guam", pl: "Guam", uk: "Гуам", tr: "Guam", ru: "Гуам", ar: "غوام", he: "גואם", fa: "گوام", hi: "गुआम", bn: "গুয়াম", te: "గ్వామ్", ta: "குவாம்", mr: "गुआम", gu: "ગુઆમ", kn: "ಗುವಾಮ್", ml: "ഗുവാം", pa: "ਗੁਆਮ", ur: "گوام", id: "Guam", ms: "Guam", th: "กวม", vi: "Guam", km: "ហ្គាំ", my: "ဂူအမ်ကျွန်း", zh: "关岛", ja: "グアム", ko: "괌", el: "Γκουάμ", bg: "Гуам", cs: "Guam", sk: "Guam", hu: "Guam", ro: "Guam", hr: "Guam", sr: "Гвам", bs: "Guam", sl: "Guam", mk: "Гуам", et: "Guam", lv: "Guama", lt: "Guamas", da: "Guam", fi: "Guam", nb: "Guam", sv: "Guam", ca: "Guam", gl: "Guam", eu: "Guam", af: "Guam", sw: "Guam", am: "ጉዋም", ka: "გუამი", hy: "Գուամ", az: "Quam", uz: "Guam", kk: "Гуам", tg: "Гуам", tk: "Guam", ky: "Гуам" }, timezone: "Pacific/Guam" },
  { city: "Noumea", translations: { fr: "Nouméa", es: "Numea", de: "Nouméa", pt: "Numeá", it: "Nouméa", nl: "Nouméa", pl: "Numea", uk: "Нумеа", tr: "Nouméa", ru: "Нумеа", ar: "نوميا", he: "נומאה", fa: "نومئا", hi: "नूमेया", bn: "নুমেয়া", te: "నౌమియా", ta: "நூமியா", mr: "नूमेआ", gu: "નૌમિયા", kn: "ನೌಮಿಯಾ", ml: "നൗമിയ", pa: "ਨੂਮੀਆ", ur: "نومیا", id: "Nouméa", ms: "Nouméa", th: "นูเมอา", vi: "Nouméa", km: "នូមេអា", my: "နူးမေးယားမြို့", zh: "努美阿", ja: "ヌメア", ko: "누메아", el: "Νουμεά", bg: "Нумеа", cs: "Nouméa", sk: "Nouméa", hu: "Nouméa", ro: "Nouméa", hr: "Nouméa", sr: "Нумеа", bs: "Nouméa", sl: "Nouméa", mk: "Нумеа", et: "Nouméa", lv: "Numea", lt: "Numėja", da: "Nouméa", fi: "Nouméa", nb: "Nouméa", sv: "Nouméa", ca: "Nouméa", gl: "Numea", eu: "Noumea", af: "Nouméa", sw: "Nouméa", am: "ኑሜአ", ka: "ნუმეა", hy: "Նումեա", az: "Numea", uz: "Numea", kk: "Нумеа", tg: "Нумеа", tk: "Numea", ky: "Нумеа" }, timezone: "Pacific/Noumea" },
  { city: "Magadan", translations: { fr: "Magadan", es: "Magadán", de: "Magadan", pt: "Magadan", it: "Magadan", nl: "Magadan", pl: "Magadan", uk: "Магадан", tr: "Magadan", ru: "Магадан", ar: "ماجادان", he: "מגדן", fa: "ماگادان", hi: "मगादान", bn: "মাগাদান", te: "మగడాన్", ta: "மகதனே", mr: "मगादान", gu: "મગદાન", kn: "ಮಗಡಾನ್", ml: "മഗദാൻ", pa: "ਮਗਦਾਨ", ur: "ماگادان", id: "Magadan", ms: "Magadan", th: "มากาดาน", vi: "Magadan", km: "ម៉ាហ្គាដាន", my: "မဂဒန်မြို့", zh: "马加丹", ja: "マガダン", ko: "마가단", el: "Μαγκαντάν", bg: "Магадан", cs: "Magadan", sk: "Magadan", hu: "Magadan", ro: "Magadan", hr: "Magadan", sr: "Магадан", bs: "Magadan", sl: "Magadan", mk: "Магадан", et: "Magadan", lv: "Magadana", lt: "Magadanas", da: "Magadan", fi: "Magadan", nb: "Magadan", sv: "Magadan", ca: "Magadan", gl: "Magadan", eu: "Magadan", af: "Magadan", sw: "Magadan", am: "ማጋዳን", ka: "მაგადანი", hy: "Մագադան", az: "Maqadan", uz: "Magadan", kk: "Магадан", tg: "Магадан", tk: "Magadan", ky: "Магадан" }, timezone: "Asia/Magadan" },
  { city: "Solomon Islands", translations: { fr: "Îles Salomon", es: "Islas Salomón", de: "Salomonen", pt: "Ilhas Salomão", it: "Isole Salomone", nl: "Salomonseilanden", pl: "Wyspy Salomona", uk: "Соломонові Острови", tr: "Solomon Adaları", ru: "Соломоновы Острова", ar: "جزر سليمان", he: "איי שלמה", fa: "جزایر سلیمان", hi: "सोलोमन द्वीप", bn: "সলোমন দ্বীপপুঞ্জ", te: "సోలమన్ దీవులు", ta: "சொலமன் தீவுகள்", mr: "सॉलोमन बेटे", gu: "સોલોમન ટાપુઓ", kn: "ಸೊಲೊಮನ್ ದ್ವೀಪಗಳು", ml: "സോളമൻ ദ്വീപുകൾ", pa: "ਸੋਲੋਮਨ ਟਾਪੂ", ur: "جزائر سلیمان", id: "Kepulauan Solomon", ms: "Kepulauan Solomon", th: "หมู่เกาะโซโลมอน", vi: "Quần đảo Solomon", km: "កោះសូឡូម៉ុង", my: "ဆော်လမွန်ကျွန်းစု", zh: "所罗门群岛", ja: "ソロモン諸島", ko: "솔로몬 제도", el: "Νήσοι Σολομώντος", bg: "Соломонови острови", cs: "Šalamounovy ostrovy", sk: "Šalamúnove ostrovy", hu: "Salamon-szigetek", ro: "Insulele Solomon", hr: "Salomonski Otoci", sr: "Соломонова Острва", bs: "Solomonska Ostrva", sl: "Salomonovi otoki", mk: "Соломонски Острови", et: "Saalomoni Saared", lv: "Zālamana salas", lt: "Saliamono Salos", da: "Salomonøerne", fi: "Salomonsaaret", nb: "Salomonøyene", sv: "Salomonöarna", ca: "Illes Salomó", gl: "Illas Salomón", eu: "Salomon Uharteak", af: "Solomoneilande", sw: "Visiwa vya Solomon", am: "ሰለሞን ደሴቶች", ka: "სოლომონის კუნძულები", hy: "Սողոմոնյան Կղզիներ", az: "Solomon adaları", uz: "Solomon orollari", kk: "Соломон аралдары", tg: "Ҷазираҳои Соломон", tk: "Solomon adalary", ky: "Соломон аралдары" }, timezone: "Pacific/Guadalcanal" },
  { city: "Auckland", translations: { fr: "Auckland", es: "Auckland", de: "Auckland", pt: "Auckland", it: "Auckland", nl: "Auckland", pl: "Auckland", uk: "Окленд", tr: "Auckland", ru: "Окленд", ar: "أوكلاند", he: "אוקלנד", fa: "آوکلند", hi: "ऑकलैंड", bn: "অকল্যান্ড", te: "ఆక్లాండ్", ta: "ஓக்லாந்து", mr: "ऑकलंड", gu: "ઓકલેન્ડ", kn: "ಆಕ್ಲೆಂಡ್", ml: "ഓക്‌ലൻഡ്", pa: "ਆਕਲੈਂਡ", ur: "آکلینڈ", id: "Auckland", ms: "Auckland", th: "ออกแลนด์", vi: "Auckland", km: "អូកឡិន", my: "အော့ကလန်မြို့", zh: "奥克兰", ja: "オークランド", ko: "오클랜드", el: "Όκλαντ", bg: "Окланд", cs: "Auckland", sk: "Auckland", hu: "Auckland", ro: "Auckland", hr: "Auckland", sr: "Окланд", bs: "Auckland", sl: "Auckland", mk: "Окленд", et: "Auckland", lv: "Oklenda", lt: "Oklandas", da: "Auckland", fi: "Auckland", nb: "Auckland", sv: "Auckland", ca: "Auckland", gl: "Auckland", eu: "Auckland", af: "Auckland", sw: "Auckland", am: "ኦክላንድ", ka: "ოკლენდი", hy: "Օքլենդ", az: "Oklend", uz: "Oklend", kk: "Окленд", tg: "Окленд", tk: "Oklend", ky: "Окленд" }, timezone: "Pacific/Auckland" },
  { city: "Wellington", translations: { fr: "Wellington", es: "Wellington", de: "Wellington", pt: "Wellington", it: "Wellington", nl: "Wellington", pl: "Wellington", uk: "Веллінгтон", tr: "Wellington", ru: "Веллингтон", ar: "ويلينغتون", he: "ולינגטון", fa: "ولینگتون", hi: "वेलिंग्टन", bn: "ওয়েলিংটন", te: "వెల్లింగ్టన్", ta: "வெலிங்டன்", mr: "वेलिंग्टन", gu: "વેલિંગ્ટન", kn: "ವೆಲ್ಲಿಂಗ್ಟನ್", ml: "വെല്ലിംഗ്ടൺ", pa: "ਵੈਲਿੰਗਟਨ", ur: "ویلنگٹن", id: "Wellington", ms: "Wellington", th: "เวลลิงตัน", vi: "Wellington", km: "វែលីងតុន", my: "ဝယ်လင်တန်မြို့", zh: "惠灵顿", ja: "ウェリントン", ko: "웰링턴", el: "Ουέλλινγκτον", bg: "Уелингтън", cs: "Wellington", sk: "Wellington", hu: "Wellington", ro: "Wellington", hr: "Wellington", sr: "Велингтон", bs: "Wellington", sl: "Wellington", mk: "Велингтон", et: "Wellington", lv: "Velingtona", lt: "Velingtonas", da: "Wellington", fi: "Wellington", nb: "Wellington", sv: "Wellington", ca: "Wellington", gl: "Wellington", eu: "Wellington", af: "Wellington", sw: "Wellington", am: "ዌሊንግተን", ka: "ველინგტონი", hy: "Վելինգթոն", az: "Vellinqton", uz: "Vellington", kk: "Веллингтон", tg: "Веллингтон", tk: "Wellington", ky: "Веллингтон" }, timezone: "Pacific/Auckland" },
  { city: "Christchurch", translations: { fr: "Christchurch", es: "Christchurch", de: "Christchurch", pt: "Christchurch", it: "Christchurch", nl: "Christchurch", pl: "Christchurch", uk: "Крайстчерч", tr: "Christchurch", ru: "Крайстчерч", ar: "كرايستشرش", he: "קרייסטצ'רץ'", fa: "کرایست‌چرچ", hi: "क्राइस्टचर्च", bn: "ক্রাইস্টচার্চ", te: "క్రైస్ట్‌చర్చ్", ta: "கிறைஸ்ட்சேர்ச்", mr: "क्राइस्टचर्च", gu: "ક્રાઇસ્ટચર્ચ", kn: "ಕ್ರೈಸ್ಟ್‌ಚರ್ಚ್", ml: "ക്രൈസ്റ്റ്ചർച്ച്", pa: "ਕ੍ਰਾਈਸਟਚਰਚ", ur: "کرائسٹ چرچ", id: "Christchurch", ms: "Christchurch", th: "ไครสต์เชิร์ช", vi: "Christchurch", km: "គ្រីស្ទឆឺច", my: "ခရစ်ချပ်မြို့", zh: "基督城", ja: "クライストチャーチ", ko: "크라이스트처치", el: "Κράισττσερτς", bg: "Крайстчърч", cs: "Christchurch", sk: "Christchurch", hu: "Christchurch", ro: "Christchurch", hr: "Christchurch", sr: "Крајстчерч", bs: "Christchurch", sl: "Christchurch", mk: "Крајстчерч", et: "Christchurch", lv: "Kraistčērča", lt: "Kraistčerčas", da: "Christchurch", fi: "Christchurch", nb: "Christchurch", sv: "Christchurch", ca: "Christchurch", gl: "Christchurch", eu: "Christchurch", af: "Christchurch", sw: "Christchurch", am: "ክራይስትቸርች", ka: "კრაისტჩერჩი", hy: "Քրայսթչորչ", az: "Kraystçerç", uz: "Kraystcherch", kk: "Крайстчерч", tg: "Крайстчерч", tk: "Kraýstçerç", ky: "Крайстчерч" }, timezone: "Pacific/Auckland" },
  { city: "Fiji", translations: { fr: "Fidji", es: "Fiyi", de: "Fidschi", pt: "Fiji", it: "Figi", nl: "Fiji", pl: "Fidżi", uk: "Фіджі", tr: "Fiji", ru: "Фиджи", ar: "فيجي", he: "פיג'י", fa: "فیجی", hi: "फ़िजी", bn: "ফিজি", te: "ఫిజీ", ta: "பிஜி", mr: "फिजी", gu: "ફીજી", kn: "ಫಿಜಿ", ml: "ഫിജി", pa: "ਫ਼ਿਜੀ", ur: "فجی", id: "Fiji", ms: "Fiji", th: "ฟีจี", vi: "Fiji", km: "ហ្វីជី", my: "ဖီဂျီနိုင်ငံ", zh: "斐济", ja: "フィジー", ko: "피지", el: "Φίτζι", bg: "Фиджи", cs: "Fidži", sk: "Fidži", hu: "Fidzsi-szigetek", ro: "Fiji", hr: "Fidži", sr: "Фиџи", bs: "Fidži", sl: "Fidži", mk: "Фиџи", et: "Fidži", lv: "Fidži", lt: "Fidžis", da: "Fiji", fi: "Fidži", nb: "Fiji", sv: "Fiji", ca: "Fiji", gl: "Fixi", eu: "Fiji", af: "Fidji", sw: "Fiji", am: "ፊጂ", ka: "ფიჯი", hy: "Ֆիջի", az: "Fici", uz: "Fiji", kk: "Фиджи", tg: "Фиҷи", tk: "Fiji", ky: "Фижи" }, timezone: "Pacific/Fiji" },
  { city: "Kamchatka", translations: { fr: "Kamtchatka", es: "Kamchatka", de: "Kamtschatka", pt: "Kamchatka", it: "Kamčatka", nl: "Kamtsjatka", pl: "Kamczatka", uk: "Камчатка", tr: "Kamçatka", ru: "Камчатка", ar: "كامشاتكا", he: "קמצ'טקה", fa: "کامچاتکا", hi: "कमचातका", bn: "কামচাটকা", te: "కమ్చట్కా", ta: "கம்சாத்கா", mr: "कामचत्का", gu: "કામચાટકા", kn: "ಕಮ್ಚಟ್ಕಾ", ml: "കാംചാറ്റ്ക", pa: "ਕਮਚਾਤਕਾ", ur: "کامچاٹکا", id: "Kamchatka", ms: "Kamchatka", th: "คัมชัตกา", vi: "Kamchatka", km: "កាំឆាតកា", my: "ကမ်ချသကာကျွန်းဆွယ်", zh: "堪察加", ja: "カムチャツカ", ko: "캄차카", el: "Καμτσάτκα", bg: "Камчатка", cs: "Kamčatka", sk: "Kamčatka", hu: "Kamcsatka", ro: "Kamceatka", hr: "Kamčatka", sr: "Камчатка", bs: "Kamčatka", sl: "Kamčatka", mk: "Камчатка", et: "Kamtšatka", lv: "Kamčatka", lt: "Kamčiatka", da: "Kamtjatka", fi: "Kamtšatka", nb: "Kamtsjatka", sv: "Kamtjatka", ca: "Kamtxatka", gl: "Kamchatka", eu: "Kamtxatka", af: "Kamtsjatka", sw: "Kamchatka", am: "ካምቻትካ", ka: "კამჩატკა", hy: "Կամչատկա", az: "Kamçatka", uz: "Kamchatka", kk: "Камчатка", tg: "Камчатка", tk: "Kamçatka", ky: "Камчатка" }, timezone: "Asia/Kamchatka" },
  { city: "Marshall Islands", translations: { fr: "Îles Marshall", es: "Islas Marshall", de: "Marshallinseln", pt: "Ilhas Marshall", it: "Isole Marshall", nl: "Marshalleilanden", pl: "Wyspy Marshalla", uk: "Маршаллові Острови", tr: "Marshall Adaları", ru: "Маршалловы Острова", ar: "جزر مارشال", he: "איי מרשל", fa: "جزایر مارشال", hi: "मार्शल द्वीपसमूह", bn: "মার্শাল দ্বীপপুঞ্জ", te: "మార్షల్ దీవులు", ta: "மார்ஷல் தீவுகள்", mr: "मार्शल बेटे", gu: "માર્શલ ટાપુઓ", kn: "ಮಾರ್ಷಲ್ ದ್ವೀಪಗಳು", ml: "മാർഷൽ ദ്വീപുകൾ", pa: "ਮਾਰਸ਼ਲ ਟਾਪੂ", ur: "جزائر مارشل", id: "Kepulauan Marshall", ms: "Kepulauan Marshall", th: "หมู่เกาะมาร์แชลล์", vi: "Quần đảo Marshall", km: "កោះម៉ាសាល់", my: "မာရှယ်အိုင်းလန်းနိုင်ငံ", zh: "马绍尔群岛", ja: "マーシャル諸島", ko: "마셜 제도", el: "Νήσοι Μάρσαλ", bg: "Маршалови острови", cs: "Marshallovy ostrovy", sk: "Marshallove ostrovy", hu: "Marshall-szigetek", ro: "Insulele Marshall", hr: "Maršalovi Otoci", sr: "Маршалска Острва", bs: "Maršalska Ostrva", sl: "Marshallovi otoki", mk: "Маршалски Острови", et: "Marshalli Saared", lv: "Māršala salas", lt: "Maršalo Salos", da: "Marshalløerne", fi: "Marshallinsaaret", nb: "Marshalløyene", sv: "Marshallöarna", ca: "Illes Marshall", gl: "Illas Marshall", eu: "Marshall Uharteak", af: "Marshalleilande", sw: "Visiwa vya Marshall", am: "ማርሻል ደሴቶች", ka: "მარშალის კუნძულები", hy: "Մարշալյան կղզիներ", az: "Marşall adaları", uz: "Marshall orollari", kk: "Маршалл аралдары", tg: "Ҷазираҳои Маршалл", tk: "Marşall adalary", ky: "Маршалл аралдары" }, timezone: "Pacific/Majuro" },
  { city: "Chatham Islands", translations: { fr: "Îles Chatham", es: "Islas Chatham", de: "Chatham-Inseln", pt: "Ilhas Chatham", it: "Isole Chatham", nl: "Chathameilanden", pl: "Wyspy Chatham", uk: "Острови Чатем", tr: "Chatham Adaları", ru: "Острова Чатем", ar: "جزر تشاتام", he: "איי צ'טהאם", fa: "جزایر چاتهام", hi: "चैथम द्वीपसमूह", bn: "চ্যাথাম দ্বীপপুঞ্জ", te: "చాథమ్ దీవులు", ta: "சதாம் தீவுகள்", mr: "चॅथम बेटे", gu: "ચેટહામ ટાપુઓ", kn: "ಚಾಥಮ್ ದ್ವೀಪಗಳು", ml: "ചാതം ദ്വീപുകൾ", pa: "ਚੈਥਮ ਟਾਪੂ", ur: "جزائر چیٹہم", id: "Kepulauan Chatham", ms: "Kepulauan Chatham", th: "หมู่เกาะแชทัม", vi: "Quần đảo Chatham", km: "កោះឆាតហាំ", my: "ချေသမ်ကျွန်းစု", zh: "查塔姆群岛", ja: "チャタム諸島", ko: "채텀 제도", el: "Νήσοι Τσάταμ", bg: "Чатъмски острови", cs: "Chathamské ostrovy", sk: "Chathamské ostrovy", hu: "Chatham-szigetek", ro: "Insulele Chatham", hr: "Otočje Chatham", sr: "Острва Чатам", bs: "Ostrva Chatham", sl: "Chathamski otoki", mk: "Четемски Острови", et: "Chathami saared", lv: "Četema salas", lt: "Čatamo salos", da: "Chathamøerne", fi: "Chathamsaaret", nb: "Chathamøyene", sv: "Chathamöarna", ca: "Illes Chatham", gl: "Illas Chatham", eu: "Chatham uharteak", af: "Chathameilande", sw: "Visiwa vya Chatham", am: "ቻተም ደሴቶች", ka: "ჩატემის კუნძულები", hy: "Չաթեմ կղզիներ", az: "Çatem adaları", uz: "Chatem orollari", kk: "Чатем архипелагы", tg: "Ҷазираҳои Чатем", tk: "Çatem adalary", ky: "Чатем аралдары" }, timezone: "Pacific/Chatham" },
  { city: "Samoa", translations: { fr: "Samoa", es: "Samoa", de: "Samoa", pt: "Samoa", it: "Samoa", nl: "Samoa", pl: "Samoa", uk: "Самоа", tr: "Samoa", ru: "Самоа", ar: "ساموا", he: "סמואה", fa: "ساموآ", hi: "समोआ", bn: "সামোয়া", te: "సమోవా", ta: "சமோவா", mr: "सामो‌आ", gu: "સમોઆ", kn: "ಸಮೋವಾ", ml: "സമോവ", pa: "ਸਮੋਆ", ur: "سامووا", id: "Samoa", ms: "Samoa", th: "ซามัว", vi: "Samoa", km: "សាម័រ", my: "ဆမိုးအားနိုင်ငံ", zh: "萨摩亚", ja: "サモア", ko: "사모아", el: "Σαμόα", bg: "Самоа", cs: "Samoa", sk: "Samoa", hu: "Szamoa", ro: "Samoa", hr: "Samoa", sr: "Самоа", bs: "Samoa", sl: "Samoa", mk: "Самоа", et: "Samoa", lv: "Samoa", lt: "Samoa", da: "Samoa", fi: "Samoa", nb: "Samoa", sv: "Samoa", ca: "Samoa", gl: "Samoa", eu: "Samoa", af: "Samoa", sw: "Samoa", am: "ሳሞአ", ka: "სამოა", hy: "Սամոա", az: "Samoa", uz: "Samoa", kk: "Самоа", tg: "Самоа", tk: "Samoa", ky: "Самоа" }, timezone: "Pacific/Apia" },
  { city: "Tonga", translations: { fr: "Tonga", es: "Tonga", de: "Tonga", pt: "Tonga", it: "Tonga", nl: "Tonga", pl: "Tonga", uk: "Тонга", tr: "Tonga", ru: "Тонга", ar: "تونغا", he: "טונגה", fa: "تونگا", hi: "टोंगा", bn: "টোঙ্গা", te: "టోంగా", ta: "தொங்கா", mr: "टोंगा", gu: "ટોંગા", kn: "ಟೋಂಗಾ", ml: "ടോംഗ", pa: "ਟੋਂਗਾ", ur: "ٹونگا", id: "Tonga", ms: "Tonga", th: "ตองงา", vi: "Tonga", km: "តុងហ្គា", my: "တုံဂါနိုင်ငံ", zh: "汤加", ja: "トンガ", ko: "통가", el: "Τόνγκα", bg: "Тонга", cs: "Tonga", sk: "Tonga", hu: "Tonga", ro: "Tonga", hr: "Tonga", sr: "Тонга", bs: "Tonga", sl: "Tonga", mk: "Тонга", et: "Tonga", lv: "Tonga", lt: "Tonga", da: "Tonga", fi: "Tonga", nb: "Tonga", sv: "Tonga", ca: "Tonga", gl: "Tonga", eu: "Tonga", af: "Tonga", sw: "Tonga", am: "ቶንጋ", ka: "ტონგა", hy: "Տոնգա", az: "Tonqa", uz: "Tonga", kk: "Тонга", tg: "Тонга", tk: "Tonga", ky: "Тонга" }, timezone: "Pacific/Tongatapu" },
  { city: "Kiribati", translations: { fr: "Kiribati", es: "Kiribati", de: "Kiribati", pt: "Kiribati", it: "Kiribati", nl: "Kiribati", pl: "Kiribati", uk: "Кірибаті", tr: "Kiribati", ru: "Кирибати", ar: "كيريباتي", he: "קיריבטי", fa: "کیریباتی", hi: "किरिबाती", bn: "কিরিবাতি", te: "కిరిబాటి", ta: "கிரிபட்டி", mr: "किरिबाटी", gu: "કિરીબાટી", kn: "ಕಿರಿಬಾಟಿ", ml: "കിരീബാസ്", pa: "ਕਿਰੀਬਾਸ", ur: "کیریباتی", id: "Kiribati", ms: "Kiribati", th: "คิริบาส", vi: "Kiribati", km: "គីរីបាទី", my: "ကီရီဘတ်စ်နိုင်ငံ", zh: "基里巴斯", ja: "キリバス", ko: "키리바시", el: "Κιριμπάτι", bg: "Кирибати", cs: "Kiribati", sk: "Kiribati", hu: "Kiribati", ro: "Kiribati", hr: "Kiribati", sr: "Кирибати", bs: "Kiribati", sl: "Kiribati", mk: "Кирибати", et: "Kiribati", lv: "Kiribati", lt: "Kiribatis", da: "Kiribati", fi: "Kiribati", nb: "Kiribati", sv: "Kiribati", ca: "Kiribati", gl: "Kiribati", eu: "Kiribati", af: "Kiribati", sw: "Kiribati", am: "ኪሪባቲ", ka: "კირიბატი", hy: "Կիրիբատի", az: "Kiribati", uz: "Kiribati", kk: "Кирибати", tg: "Кирибати", tk: "Kiribati", ky: "Кирибати" }, timezone: "Pacific/Kiritimati" },
];

// Clé de stockage pour les fuseaux horaires
const WORLD_CLOCK_STORAGE_KEY = "timetools_world_clock_timezones"
const WORLD_CLOCK_FORMAT_KEY = "timetools_world_clock_format"

// Define the ClockDisplay component
interface ClockDisplayProps {
  timezone: string | undefined;
  city: string;
  lang: string;
  use24HourFormat: boolean;
  formatTime: (date: Date) => string;
  formatDate: (date: Date) => string;
  getTranslatedCityName: (city: string, translated?: boolean) => string;
  t: any; // Translations object
  isLocal?: boolean;
  translated?: boolean;
  timezoneData: Record<string, TimezoneData>;
  removeTimeZone: (id: string) => void;
  id: string;
}

const ClockDisplay = memo(({
  timezone,
  city,
  lang,
  use24HourFormat,
  formatTime,
  formatDate,
  getTranslatedCityName,
  t,
  isLocal,
  translated,
  timezoneData,
  removeTimeZone,
  id
}: ClockDisplayProps) => {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    let timeoutId: NodeJS.Timeout | null = null;

    const tick = () => {
      // Calculate the current time in the specific timezone
      try {
        const now = new Date();
        if (!timezone && !isLocal) {
          setCurrentTime(now); // Fallback for unknown timezone
        } else {
          const effectiveTimezone = isLocal ? Intl.DateTimeFormat().resolvedOptions().timeZone : timezone;
          // Use toLocaleString for conversion, then parse back to Date object
          // This handles DST correctly based on the IANA timezone string
          const timeString = now.toLocaleString('en-US', { timeZone: effectiveTimezone });
          const dateInTZ = new Date(timeString);
          setCurrentTime(dateInTZ);
        }
      } catch (error) {
        console.warn(`Failed to get time for timezone ${timezone}, falling back to local time`, error);
        setCurrentTime(new Date()); // Fallback to local time on error
      }


      // Calculate delay until the next second
      const nowForDelay = new Date(); // Get fresh date for delay calculation
      const delay = 1000 - nowForDelay.getMilliseconds();

      // Schedule the next tick precisely
      timeoutId = setTimeout(tick, delay);
    };

    // Start the first tick slightly delayed to align better
    const initialDelay = 1000 - new Date().getMilliseconds();
    timeoutId = setTimeout(tick, initialDelay);

    // Cleanup function
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [timezone, isLocal]); // Re-run effect if timezone changes

  return (
    <Card className={`overflow-hidden ${isLocal ? "border-primary" : ""}`}>
      <CardContent className="p-4">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-bold text-lg">
              {isLocal ? t.localTime || "Local Time" : getTranslatedCityName(city, translated)}
            </h3>
            <p className="text-sm text-muted-foreground">
              {timezone ? timezone.replace(/_/g, ' ') : (
                isLocal ? Intl.DateTimeFormat().resolvedOptions().timeZone : 'Unknown Timezone'
              )}
            </p>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => removeTimeZone(id)}
            className="h-8 w-8"
            disabled={isLocal}
          >
            <Trash2 className="h-4 w-4" />
            <span className="sr-only">{t.delete}</span>
          </Button>
        </div>
        <div className="mt-4">
          <p className="text-3xl font-mono font-bold">{formatTime(currentTime)}</p>
          <div className="mt-1 flex flex-wrap items-center gap-x-2 text-sm text-muted-foreground">
            <span>{formatDate(currentTime)}</span>
            <div className="flex items-center gap-1">
              <span className="text-xs">
                {(() => {
                  if (isLocal) {
                    const offsetMinutes = -new Date().getTimezoneOffset();
                    const hours = Math.floor(offsetMinutes / 60);
                    const minutes = Math.abs(offsetMinutes % 60);
                    return `UTC${hours >= 0 ? '+' : ''}${hours}${minutes ? `:${minutes.toString().padStart(2, '0')}` : ''}`;
                  }
                  if (timezoneData[city]?.utc_offset) {
                    return `UTC${timezoneData[city].utc_offset}`;
                  }
                  // Fallback offset calculation if API data is missing
                  try {
                    if (!timezone) return '';
                    const now = new Date();
                    const tzDate = new Date(now.toLocaleString('en-US', { timeZone: timezone }));
                    const utcDate = new Date(now.toLocaleString('en-US', { timeZone: 'UTC' }));
                    const diffMinutes = (tzDate.getTime() - utcDate.getTime()) / 60000;
                    const hours = Math.floor(diffMinutes / 60);
                    const minutes = Math.abs(Math.floor(diffMinutes % 60));
                    return `UTC${hours >= 0 ? '+' : ''}${hours}${minutes ? `:${minutes.toString().padStart(2, '0')}` : ''}`;
                  } catch {
                    return '';
                  }
                })()}
              </span>
              {(() => {
                const isDST = (() => {
                  try {
                    const now = new Date();
                    const effectiveTimezone = isLocal ? Intl.DateTimeFormat().resolvedOptions().timeZone : timezone;
                    if (!effectiveTimezone) return false;

                    const jan = new Date(Date.UTC(now.getFullYear(), 0, 1));
                    const jul = new Date(Date.UTC(now.getFullYear(), 6, 1));

                    // Get offsets using Intl.DateTimeFormat which is more reliable for DST
                    const formatter = Intl.DateTimeFormat('en-US', { timeZone: effectiveTimezone, timeZoneName: 'shortOffset' });
                    const janOffsetString = formatter.formatToParts(jan).find(part => part.type === 'timeZoneName')?.value;
                    const julOffsetString = formatter.formatToParts(jul).find(part => part.type === 'timeZoneName')?.value;
                    const currentOffsetString = formatter.formatToParts(now).find(part => part.type === 'timeZoneName')?.value;

                    // Basic check: if current offset differs from *both* Jan and Jul offsets, it might be DST
                    // A more robust check compares offsets directly, but parsing GMT strings is tricky.
                    // This simpler check assumes DST means a different offset than standard time (usually Jan).
                    return currentOffsetString !== janOffsetString;

                  } catch (error) {
                    console.error('Error calculating DST:', error);
                    return false;
                  }
                })();

                if (isDST !== undefined) {
                  return (
                    <span
                      className={`text-[10px] px-1.5 rounded flex items-center gap-1 ${
                        isDST
                          ? 'bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-300'
                          : 'bg-sky-100 text-sky-700 dark:bg-sky-900 dark:text-sky-300'
                      }`}
                      title={isDST ? t.daylightTime : t.standardTime}
                    >
                      <span className="h-1.5 w-1.5 rounded-full bg-current" />
                      {isDST ? t.dst : t.std}
                    </span>
                  );
                }
                return null;
              })()}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});
ClockDisplay.displayName = 'ClockDisplay'; // Add display name for DevTools


// Remplacer la fonction WorldClock par cette version améliorée
export function WorldClock({ lang }: WorldClockProps) {
  const t = getTranslations(lang)
  // Remove the main time state: const [time, setTime] = useState(new Date())
  const [newCity, setNewCity] = useState("")
  const [timeZones, setTimeZones] = useState<TimeZone[]>([])
  const [use24HourFormat, setUse24HourFormat] = useState(true)
  const [filteredCities, setFilteredCities] = useState<any[]>([])
  const [timezoneData, setTimezoneData] = useState<Record<string, TimezoneData>>({}); // Keep this for offsets
  const [loadingTimezones, setLoadingTimezones] = useState(false);
  const [lastFetchTime, setLastFetchTime] = useState<number>(0);
  const [fetchError, setFetchError] = useState<string | null>(null);
  const [selectedTimezone, setSelectedTimezone] = useState<string | null>(null)
  const [timezoneList, setTimezoneList] = useState<string[]>([])
  const [isToolFullscreen, setIsToolFullscreen] = useState(false)

  // Effect to update timezone list
  useEffect(() => {
    const timezones = worldCities
      .map((city) => city.timezone)
      .filter((timezone, index, self) =>
        timezone &&
        !timezone.startsWith('UTC+') &&
        !timezone.startsWith('UTC-') &&
        self.indexOf(timezone) === index
      );

    // Trier les fuseaux horaires par leur offset actuel
    const sortedTimezones = timezones.sort((a, b) => {
      try {
        const now = new Date();
        const tzDateA = new Date(now.toLocaleString('en-US', { timeZone: a }));
        const tzDateB = new Date(now.toLocaleString('en-US', { timeZone: b }));
        const utcDate = new Date(now.toLocaleString('en-US', { timeZone: 'UTC' }));

        const offsetA = (tzDateA.getTime() - utcDate.getTime()) / (60 * 1000);
        const offsetB = (tzDateB.getTime() - utcDate.getTime()) / (60 * 1000);

        return offsetA - offsetB;
      } catch {
        return a.localeCompare(b);
      }
    });

    setTimezoneList(sortedTimezones);
  }, []);

  // Ajouter l'heure locale par défaut au chargement initial et charger les fuseaux horaires sauvegardés
  useEffect(() => {
    // Obtenir le fuseau horaire local
    const localOffset = -(new Date().getTimezoneOffset() / 60)
    const offsetStr = localOffset >= 0 ? `+${localOffset}` : `${localOffset}`

    // Trouver une ville dans le même fuseau horaire si possible
    const citiesInSameTimezone = worldCities.filter((city) => {
      const tzOffset = getUtcOffset(city.timezone);
      return tzOffset === localOffset;
    });
    const localCity = citiesInSameTimezone.length > 0 ? citiesInSameTimezone[0] : null

    // Create local timezone
    const localTimeZone: TimeZone = {
      id: "local",
      city: "",
      translated: false,
      isLocal: true,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      utcOffset: offsetStr
    }

    // Charger les fuseaux horaires sauvegardés
    const savedTimeZones = getFromLocalStorage<TimeZone[]>(WORLD_CLOCK_STORAGE_KEY, [])
    const savedFormat = getFromLocalStorage<boolean>(WORLD_CLOCK_FORMAT_KEY, true)

    // Fonction pour initialiser les fuseaux horaires
    const initializeTimeZones = async () => {
      let zones;
      if (savedTimeZones.length > 0) {
        // S'assurer que l'heure locale est toujours présente
        const hasLocalTimeZone = savedTimeZones.some((tz) => tz.isLocal);
        zones = hasLocalTimeZone ? savedTimeZones : [localTimeZone, ...savedTimeZones];
      } else {
        // Add default popular timezones
        zones = [
          localTimeZone,
          { id: "1", city: "London", timezone: "Europe/London", translated: true },
          { id: "2", city: "Paris", timezone: "Europe/Paris", translated: true },
          { id: "3", city: "New York", timezone: "America/New_York", translated: true },
          { id: "4", city: "Tokyo", timezone: "Asia/Tokyo", translated: true }
        ];
      }

      // Initialiser les timeZones avant de fetcher les données
      setTimeZones(zones);

      // Fetch initial timezone data
      await fetchAllTimezones();
    };

    initializeTimeZones();

    // Charger le format d'heure sauvegardé
    setUse24HourFormat(savedFormat)
  }, [])

  // Sauvegarder les fuseaux horaires lorsqu'ils changent
  useEffect(() => {
    if (timeZones.length > 0) {
      saveToLocalStorage(WORLD_CLOCK_STORAGE_KEY, timeZones)
    }
  }, [timeZones])

  // Sauvegarder le format d'heure lorsqu'il change
  useEffect(() => {
    saveToLocalStorage(WORLD_CLOCK_FORMAT_KEY, use24HourFormat)
  }, [use24HourFormat])

  // Remove useSecondTick hook and its invocation

  useEffect(() => {
    if (newCity.trim() === "") {
      setFilteredCities([])
      return
    }

    const filtered = worldCities.filter(
      (city) =>
        city.city.toLowerCase().includes(newCity.toLowerCase()) ||
        (city.translations[lang as keyof typeof city.translations] &&
          city.translations[lang as keyof typeof city.translations].toLowerCase().includes(newCity.toLowerCase())),
    )
    setFilteredCities(filtered.slice(0, 10)) // Limiter à 10 résultats pour ne pas surcharger l'interface
  }, [newCity, lang])

  // Obtenir les fuseaux horaires uniques pour le regroupement
  const getUniqueTimezones = (data: Record<string, TimezoneData>) => {
    const timezones = worldCities.map((city) => city.timezone).filter(timezone =>
      timezone && !timezone.startsWith('UTC+') && !timezone.startsWith('UTC-')
    )
    const uniqueTimezones = [...new Set(timezones)];

    // Sort timezones based on their UTC offset from API data or fallback
    return uniqueTimezones.sort((a, b) => {
      // Try to get offsets from API data first
      const tzDataA = Object.values(data).find(tz => tz.timezone === a);
      const tzDataB = Object.values(data).find(tz => tz.timezone === b);

      let offsetA = 0;
      let offsetB = 0;

      if (tzDataA?.utc_offset) {
        offsetA = parseInt(tzDataA.utc_offset.replace(':', '.'));
      } else {
        try {
          const now = new Date();
          const tzDate = new Date(now.toLocaleString('en-US', { timeZone: a }));
          const utcDate = new Date(now.toLocaleString('en-US', { timeZone: 'UTC' }));
          const diffMinutes = (tzDate.getTime() - utcDate.getTime()) / 60000;
          offsetA = Math.floor(diffMinutes / 60);
        } catch (e) {
          const match = a.match(/GMT([+-]\d+)/);
          if (match) {
            offsetA = parseInt(match[1]);
          }
        }
      }

      if (tzDataB?.utc_offset) {
        offsetB = parseInt(tzDataB.utc_offset.replace(':', '.'));
      } else {
        try {
          const now = new Date();
          const tzDate = new Date(now.toLocaleString('en-US', { timeZone: b }));
          const utcDate = new Date(now.toLocaleString('en-US', { timeZone: 'UTC' }));
          const diffMinutes = (tzDate.getTime() - utcDate.getTime()) / 60000;
          offsetB = Math.floor(diffMinutes / 60);
        } catch (e) {
          const match = b.match(/GMT([+-]\d+)/);
          if (match) {
            offsetB = parseInt(match[1]);
          }
        }
      }

      return offsetA - offsetB;
    });
  }

  // Remplacer la fonction addTimeZone pour vérifier les doublons
  const addTimeZone = async (cityData?: any) => {
    if (cityData) {
      // Vérifier si cette ville existe déjà dans la liste
      const cityExists = timeZones.some((tz) => tz.city === cityData.city && !tz.isLocal)
      if (cityExists) {
        // La ville existe déjà, ne pas l'ajouter à nouveau
        setNewCity("")
        setFilteredCities([])
        setSelectedTimezone(null)
        return
      }

      // Ajouter une ville depuis la liste de suggestions avec les données de timezone
      const newTimeZone: TimeZone = {
        id: Date.now().toString(),
        city: cityData.city,
        timezone: cityData.timezone,
        translated: true,
      }

      // Fetch timezone data for the new city immediately
      const tzData = await fetchTimezoneData(cityData.timezone);
      if (tzData) {
        setTimezoneData(prev => ({
          ...prev,
          [cityData.city]: tzData
        }));
      }

      setTimeZones(prev => [...prev, newTimeZone]);
      setNewCity("");
      setFilteredCities([]);
      setSelectedTimezone(null);
    } else if (newCity.trim() !== "") {
      // Rechercher une correspondance approximative dans la liste des villes
      const matchingCity = worldCities.find(
        (city) =>
          city.city.toLowerCase().includes(newCity.toLowerCase()) ||
          (city.translations[lang as keyof typeof city.translations] &&
            city.translations[lang as keyof typeof city.translations].toLowerCase().includes(newCity.toLowerCase())),
      )

      if (matchingCity) {
        addTimeZone(matchingCity)
      } else {
        // Réinitialiser le champ de recherche si aucune correspondance n'est trouvée
        setNewCity("")
      }
    }
  }

  const removeTimeZone = (id: string) => {
    setTimeZones(timeZones.filter((tz) => tz.id !== id))
  }

  const FETCH_INTERVAL = 60000; // 1 minute
  const RETRY_COUNT = 3;

  // Helper to check if a timezone string is a valid IANA timezone
  const isValidIANATimezone = (timezone: string): boolean => {
    return !timezone.startsWith('UTC+') && !timezone.startsWith('UTC-');
  };

  // Helper to get a fallback IANA timezone for UTC offsets
  const getFallbackTimezone = (offset: number): string | null => {
    // Handle fractional offsets explicitly
    const fractionalMap: Record<number, string> = {
      '-9.5': 'Pacific/Marquesas',
      '-3.5': 'America/St_Johns',
      '3.5': 'Asia/Tehran',
      '4.5': 'Asia/Kabul',
      '5.5': 'Asia/Kolkata',
      '5.75': 'Asia/Kathmandu',
      '6.5': 'Asia/Yangon',
      '9.5': 'Australia/Darwin',
      '12.75': 'Pacific/Chatham'
    };

    // Check for exact fractional matches first
    if (fractionalMap[offset]) {
      return fractionalMap[offset];
    }

    // Then fall back to standard hour offsets
    const standardMap: Record<number, string> = {
      '-11': 'Pacific/Midway',
      '-10': 'Pacific/Honolulu',
      '-9': 'America/Anchorage',
      '-8': 'America/Los_Angeles',
      '-7': 'America/Denver',
      '-6': 'America/Chicago',
      '-5': 'America/New_York',
      '-4': 'America/Halifax',
      '-3': 'America/Sao_Paulo',
      '-2': 'Atlantic/South_Georgia',
      '-1': 'Atlantic/Azores',
      '0': 'Europe/London',
      '1': 'Europe/Paris',
      '2': 'Europe/Kyiv',
      '3': 'Europe/Moscow',
      '4': 'Asia/Dubai',
      '5': 'Asia/Karachi',
      '6': 'Asia/Dhaka',
      '7': 'Asia/Bangkok',
      '8': 'Asia/Singapore',
      '9': 'Asia/Tokyo',
      '10': 'Australia/Sydney',
      '11': 'Pacific/Noumea',
      '12': 'Pacific/Auckland',
      '13': 'Pacific/Apia',
      '14': 'Pacific/Kiritimati'
    };

    return standardMap[Math.floor(offset)] || null;
  };

  const fetchTimezoneData = async (timezone: string, retries = RETRY_COUNT) => {
    try {
      // If timezone is in UTC+X format, try to get a fallback IANA timezone
      if (!isValidIANATimezone(timezone)) {
        const offset = parseFloat(timezone.replace('UTC', '').replace(':', '.'));
        const fallbackTimezone = getFallbackTimezone(offset);
        if (!fallbackTimezone) {
          console.warn(`No fallback timezone found for offset ${offset}`);
          return null;
        }
        timezone = fallbackTimezone;
      }

      // First verify if timezone is valid
      try {
        // Test if timezone is valid by attempting to use it
        new Date().toLocaleString('en-US', { timeZone: timezone });
      } catch (e) {
        console.warn(`Invalid timezone ${timezone}, using fallback`);
        // Try to find an alternative timezone based on the offset
        const city = worldCities.find(c => c.timezone === timezone);
        if (city) {
          const offset = getUtcOffset(city.timezone);
          const fallback = getFallbackTimezone(offset);
          if (fallback) {
            timezone = fallback;
          } else {
            timezone = 'Etc/UTC';
          }
        } else {
          timezone = 'Etc/UTC';
        }
      }

      // Use a local Date object to calculate current time in timezone
      const now = new Date();
      try {
        return {
          datetime: now.toLocaleString('en-US', { timeZone: timezone }),
          timezone: timezone,
          dst: false,
          utc_offset: (() => {
            const tzDate = new Date(now.toLocaleString('en-US', { timeZone: timezone }));
            const utcDate = new Date(now.toLocaleString('en-US', { timeZone: 'UTC' }));
            const diffMinutes = (tzDate.getTime() - utcDate.getTime()) / 60000;
            const hours = Math.floor(diffMinutes / 60);
            const minutes = Math.abs(diffMinutes % 60);
            return `${hours >= 0 ? '+' : ''}${hours}${minutes ? `:${minutes.toString().padStart(2, '0')}` : ''}`;
          })()
        };
      } catch (error) {
        console.warn(`Using fallback for timezone ${timezone}`);
        return {
          datetime: now.toISOString(),
          timezone: 'UTC',
          dst: false,
          utc_offset: '+00:00'
        };
      }
    } catch (error) {
      console.error(`Failed to handle timezone ${timezone}:`, error);
      return null;
    }
  };

  const fetchAllTimezones = async () => {
    const now = Date.now();
    if (now - lastFetchTime < FETCH_INTERVAL) return;

    setLoadingTimezones(true);
    try {
      const newData: Record<string, TimezoneData> = {};
      // Get all unique IANA timezones from worldCities for the dropdown
      const allTimezones = new Set(worldCities.map(city => city.timezone).filter(Boolean));
      const uniqueTimezones = new Set([
        ...timeZones
          .filter(tz => !tz.isLocal && tz.city)
          .map(tz => {
            const cityData = worldCities.find(c => c.city === tz.city);
            return cityData?.timezone;
          })
          .filter(Boolean),
        ...allTimezones
      ]);

      await Promise.all(Array.from(uniqueTimezones).map(async (timezone) => {
        if (!timezone) return;
        const data = await fetchTimezoneData(timezone);
        if (data) {
          // Store the data for all cities in this timezone
          timeZones.forEach(tz => {
            const cityData = worldCities.find(c => c.city === tz.city);
            if (cityData?.timezone === timezone) {
              newData[tz.city] = data;
            }
          });
        }
      }));

      setTimezoneData(prev => ({...prev, ...newData}));
      setLastFetchTime(now);
      setFetchError(null);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch timezone data';
      console.error('Error fetching timezone data:', error);
      setFetchError(errorMessage);

      // En cas d'erreur, utiliser les données locales pour les offsets
      const newData: Record<string, TimezoneData> = {};
      timeZones.forEach(tz => {
        if (tz.city && !tz.isLocal) {
          const cityData = worldCities.find(c => c.city === tz.city);
          if (cityData?.timezone) {
            const now = new Date();
            const offset = getUtcOffset(cityData.timezone);
            const hours = Math.floor(offset);
            const minutes = Math.abs((offset - hours) * 60);
            newData[tz.city] = {
              datetime: now.toISOString(),
              timezone: cityData.timezone,
              dst: false,
              utc_offset: `${hours >= 0 ? '+' : ''}${hours}${minutes ? `:${minutes.toString().padStart(2, '0')}` : ''}`
            };
          }
        }
      });

      // Mettre à jour avec les données locales
      setTimezoneData(prev => ({...prev, ...newData}));
    } finally {
      setLoadingTimezones(false);
    }
  };

  // Remove getTimeInTimeZone function

  // Effect pour charger les données initiales
  useEffect(() => {
    const initializeTimezones = async () => {
      await fetchAllTimezones();
    };
    initializeTimezones();

    const interval = setInterval(fetchAllTimezones, FETCH_INTERVAL);
    return () => clearInterval(interval);
  }, []); // Ne dépend pas de timeZones pour éviter les re-renders inutiles

  // Effect pour mettre à jour les données quand les fuseaux horaires changent
  useEffect(() => {
    const updateTimezoneData = async () => {
      if (timeZones.length > 0) {
        await fetchAllTimezones();
      }
    };
    updateTimezoneData();
  }, [timeZones]);

  const formatTime = (date: Date) => {
    if (use24HourFormat) {
      return date.toLocaleTimeString(lang, { hour: "2-digit", minute: "2-digit", second: "2-digit", hour12: false })
    } else {
      return date.toLocaleTimeString(lang, { hour: "2-digit", minute: "2-digit", second: "2-digit", hour12: true })
    }
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString(lang, { weekday: "long", year: "numeric", month: "long", day: "numeric" })
  }

  const getTranslatedCityName = (city: string, translated = false) => {
    if (!translated) return city

    const cityData = worldCities.find((c) => c.city === city)
    if (cityData && cityData.translations[lang as keyof typeof cityData.translations]) {
      return cityData.translations[lang as keyof typeof cityData.translations]
    }
    return city
  }

  // Fonction utilitaire pour sauvegarder dans localStorage
  function saveToLocalStorage<T>(key: string, data: T): void {
    try {
      if (typeof window !== "undefined") {
        const serializedData = JSON.stringify(data)
        localStorage.setItem(key, serializedData)
      }
    } catch (error) {
      console.error("Error saving to localStorage:", error)
    }
  }

  // Fonction utilitaire pour charger depuis localStorage
  function getFromLocalStorage<T>(key: string, defaultValue: T): T {
    try {
      if (typeof window !== "undefined") {
        const serializedData = localStorage.getItem(key)
        if (serializedData === null) {
          return defaultValue
        }
        return JSON.parse(serializedData) as T
      }
      return defaultValue
    } catch (error) {
      console.error("Error getting from localStorage:", error)
      return defaultValue
    }
  }

  const worldClockContent = (
    <div className="mb-6">
      {/* Apply responsive classes to the main container */}
      <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4 mb-4">
        {/* Input/Select Container */}
        <div className="flex-1 relative">
          {/* Apply responsive classes to the Input/Select group */}
          <div className="flex flex-col sm:flex-row gap-2">
            <div className="flex-1 relative">
              <Input
                placeholder={t.addCity}
                value={newCity}
                onChange={(e) => {
                  setNewCity(e.target.value)
                  setSelectedTimezone(null)
                }}
                onKeyDown={async (e) => {
                  if (e.key === "Enter" && newCity.trim() !== "") {
                    await addTimeZone();
                  }
                }}
              />

              {filteredCities.length > 0 && (
                <div className="absolute z-10 w-full bg-background border rounded-md mt-1 shadow-lg max-h-60 overflow-y-auto">
                  {filteredCities.map((city, index) => (
                    <div
                      key={index}
                      className="p-2 hover:bg-muted cursor-pointer flex justify-between"
                      onClick={async () => {
                        await addTimeZone(city);
                      }}
                    >
                      <span>{city.translations[lang as keyof typeof city.translations] || city.city}</span>
                      <span className="text-muted-foreground">{city.timezone}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <Select
              value={selectedTimezone || ""}
              disabled={loadingTimezones}
              onOpenChange={async (open) => {
                if (open && !loadingTimezones) {
                  await fetchAllTimezones();
                }
              }}
              onValueChange={async (value) => {
                if (value) {
                  setSelectedTimezone(value);
                  setNewCity("");
                  // Ensure we have timezone data
                  await fetchAllTimezones();
                }
              }}
            >
              <SelectTrigger className="w-full sm:w-[180px]"> {/* Make trigger full width on small screens */}
                <SelectValue placeholder={t.selectTimezone || "Select timezone"} />
              </SelectTrigger>
              <SelectContent className="max-h-80 z-50" side="bottom" align="end">
                {getUniqueTimezones(timezoneData).map((timezone) => (
                  <SelectItem key={timezone} value={timezone}>
                    <div className="flex justify-between items-center gap-2" dir="ltr">
                      <span className="text-left flex-1 whitespace-nowrap">{timezone.split('/').join(' / ')}</span>
                      <span className="text-xs text-muted-foreground ml-2">
                        {(() => {
                          try {
                            const now = new Date();
                            const tzDate = new Date(now.toLocaleString('en-US', { timeZone: timezone }));
                            const utcDate = new Date(now.toLocaleString('en-US', { timeZone: 'UTC' }));
                            const diffMinutes = (tzDate.getTime() - utcDate.getTime()) / 60000;
                            const hours = Math.floor(diffMinutes / 60);
                            const minutes = Math.abs(Math.floor(diffMinutes % 60));
                            return `UTC${hours >= 0 ? '+' : ''}${hours}${minutes ? `:${minutes.toString().padStart(2, '0')}` : ''}`;
                          } catch {
                            return '';
                          }
                        })()}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div> {/* Close Input/Select Inner Group */}

          {/* Suggestions for selected timezone (associated with outer container) */}
          {selectedTimezone && (
            <div className="absolute z-10 w-full bg-background border rounded-md mt-1 shadow-lg max-h-60 overflow-y-auto"> {/* Keep original positioning logic */}
              <div className="p-2 font-medium border-b">{selectedTimezone}</div>
              {worldCities
                .filter((city) => city.timezone === selectedTimezone)
                .map((city, index) => (
                  <div
                    key={index}
                    className="p-2 hover:bg-muted cursor-pointer flex justify-between"
                  onClick={async () => {
                    await addTimeZone(city);
                  }}
                  >
                    <span>{city.translations[lang as keyof typeof city.translations] || city.city}</span>
                    <span className="text-muted-foreground flex items-center gap-1">
                      <span>{city.timezone.split('/').slice(-1)[0].replace('_', ' ')}</span>
                      {(() => {
                        const tzData = Object.values(timezoneData).find(tz => tz.timezone === city.timezone);
                        return tzData ? (
                          <span className="text-xs">
                            (UTC{tzData.utc_offset})
                          </span>
                        ) : null;
                      })()}
                    </span>
                  </div>
                ))}
            </div>
            )}
        </div> {/* Close Input/Select Container */}

        {/* Adjust margin and alignment for the Switch */}
        <div className="mt-4 md:mt-0 md:ml-4 flex items-center gap-2 self-start md:self-center">
          <Switch id="time-format" checked={use24HourFormat} onCheckedChange={setUse24HourFormat} />
          <Label htmlFor="time-format" className="text-sm whitespace-nowrap">
            {use24HourFormat ? t.hour24 : t.hour12}
          </Label>
        </div>
      </div> {/* Close Main Responsive Container */}

      {/* Clock Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...timeZones].sort((a, b) => {
          // L'heure locale est toujours en premier
          if (a.isLocal) return -1;
          if (b.isLocal) return 1;

          // Si nous avons des données API pour les deux villes
          const tzDataA = timezoneData[a.city];
          const tzDataB = timezoneData[b.city];

          if (tzDataA && tzDataB) {
            // Convertir les offsets en nombres pour la comparaison (gérer les minutes)
            const [hoursA, minsA = 0] = tzDataA.utc_offset.split(':').map(Number);
            const [hoursB, minsB = 0] = tzDataB.utc_offset.split(':').map(Number);
            const totalA = hoursA + (minsA / 60);
            const totalB = hoursB + (minsB / 60);
            return totalA - totalB;
          }

          // Si une seule ville a des données API, la mettre en premier
          if (tzDataA) return -1;
          if (tzDataB) return 1;

          // Fallback : tri alphabétique par ville
          return a.city.localeCompare(b.city);
        }).map((tz) => (
          // Render the new ClockDisplay component
          <ClockDisplay
            key={tz.id}
            id={tz.id}
            timezone={tz.timezone}
            city={tz.city}
            lang={lang}
            use24HourFormat={use24HourFormat}
            formatTime={formatTime}
            formatDate={formatDate}
            getTranslatedCityName={getTranslatedCityName}
            t={t}
            isLocal={tz.isLocal}
            translated={tz.translated}
            timezoneData={timezoneData}
            removeTimeZone={removeTimeZone}
          />
        ))}
      </div>
    </div>
  )

  return (
    <>
      <Card className={`w-full max-w-6xl mx-auto ${loadingTimezones ? 'opacity-95' : ''} relative`}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                {t.worldClock}
                {loadingTimezones && (
                  <div className="ml-2 text-xs text-muted-foreground animate-pulse">
                    Syncing...
                  </div>
                )}
              </CardTitle>
              {fetchError && (
                <div className="text-sm text-destructive mt-2">
                  {fetchError}
                </div>
              )}
            </div>
            <ToolControls
              onFullscreenToggle={setIsToolFullscreen}
              variant="compact"
            />
          </div>
        </CardHeader>
        <CardContent>
          {worldClockContent}
        </CardContent>
      </Card>

      {/* Wrapper plein écran */}
      <ToolFullscreenWrapper
        isFullscreen={isToolFullscreen}
        onClose={() => setIsToolFullscreen(false)}
        toolName={t.worldClock}
      >
        <Card className="w-full border-0 shadow-none bg-transparent">
          <CardContent>
            {worldClockContent}
          </CardContent>
        </Card>
      </ToolFullscreenWrapper>
    </>
  );
}
