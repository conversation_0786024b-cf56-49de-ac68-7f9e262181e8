@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import des styles RTL pour les icônes */
@import url('../styles/rtl-icons.css');
/* Import des styles RTL pour les mini-applications */
@import url('../styles/rtl-applications.css');
/* Import des styles RTL pour les onglets */
@import url('../styles/rtl-tabs.css');
/* Import des styles RTL pour les sélecteurs */
@import url('../styles/rtl-select.css');

/* Styles de base pour RTL */
[dir="rtl"] {
  text-align: right;
}

/* Inversions de direction pour flexbox */
[dir="rtl"] .flex-row {
  flex-direction: row-reverse;
}

[dir="rtl"] .justify-start {
  justify-content: flex-end;
}

[dir="rtl"] .justify-end {
  justify-content: flex-start;
}

[dir="rtl"] .items-start {
  align-items: flex-end;
}

[dir="rtl"] .items-end {
  align-items: flex-start;
}

/* Espacement */
[dir="rtl"] .space-x-1 > :not([hidden]) ~ :not([hidden]),
[dir="rtl"] .space-x-2 > :not([hidden]) ~ :not([hidden]),
[dir="rtl"] .space-x-3 > :not([hidden]) ~ :not([hidden]),
[dir="rtl"] .space-x-4 > :not([hidden]) ~ :not([hidden]),
[dir="rtl"] .space-x-5 > :not([hidden]) ~ :not([hidden]),
[dir="rtl"] .space-x-6 > :not([hidden]) ~ :not([hidden]),
[dir="rtl"] .space-x-8 > :not([hidden]) ~ :not([hidden]),
[dir="rtl"] .space-x-10 > :not([hidden]) ~ :not([hidden]),
[dir="rtl"] .space-x-12 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

/* Marges */
[dir="rtl"] .ml-1, [dir="rtl"] .ml-2, [dir="rtl"] .ml-3,
[dir="rtl"] .ml-4, [dir="rtl"] .ml-5, [dir="rtl"] .ml-6,
[dir="rtl"] .ml-8, [dir="rtl"] .ml-10, [dir="rtl"] .ml-12 {
  margin-right: var(--tw-margin-left);
  margin-left: 0;
}

[dir="rtl"] .mr-1, [dir="rtl"] .mr-2, [dir="rtl"] .mr-3,
[dir="rtl"] .mr-4, [dir="rtl"] .mr-5, [dir="rtl"] .mr-6,
[dir="rtl"] .mr-8, [dir="rtl"] .mr-10, [dir="rtl"] .mr-12 {
  margin-left: var(--tw-margin-right);
  margin-right: 0;
}

[dir="rtl"] .ml-auto {
  margin-right: auto;
  margin-left: 0;
}

[dir="rtl"] .mr-auto {
  margin-left: auto;
  margin-right: 0;
}

/* Paddings */
[dir="rtl"] .pl-1, [dir="rtl"] .pl-2, [dir="rtl"] .pl-3,
[dir="rtl"] .pl-4, [dir="rtl"] .pl-5, [dir="rtl"] .pl-6 {
  padding-right: var(--tw-padding-left);
  padding-left: 0;
}

[dir="rtl"] .pr-1, [dir="rtl"] .pr-2, [dir="rtl"] .pr-3,
[dir="rtl"] .pr-4, [dir="rtl"] .pr-5, [dir="rtl"] .pr-6 {
  padding-left: var(--tw-padding-right);
  padding-right: 0;
}

/* Bordures */
[dir="rtl"] .border-l {
  border-left: 0;
  border-right: 1px solid var(--border);
}

[dir="rtl"] .border-r {
  border-right: 0;
  border-left: 1px solid var(--border);
}

/* Arrondis */
[dir="rtl"] .rounded-l {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: var(--radius);
  border-bottom-right-radius: var(--radius);
}

[dir="rtl"] .rounded-r {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: var(--radius);
  border-bottom-left-radius: var(--radius);
}

/* Transitions pour le mode RTL */
.rtl-transition {
  transition: all 0.3s ease-in-out;
}

/* Styles spécifiques pour les éléments de formulaire RTL */
[dir="rtl"] input[type="text"],
[dir="rtl"] input[type="email"],
[dir="rtl"] input[type="password"],
[dir="rtl"] input[type="search"],
[dir="rtl"] textarea,
[dir="rtl"] select,
[dir="rtl"] [role="combobox"],
[dir="rtl"] [role="listbox"] {
  text-align: right;
  direction: rtl;
}

/* Styles pour les listes déroulantes en RTL */
[dir="rtl"] [role="listbox"],
[dir="rtl"] [role="option"] {
  text-align: right;
}

/* Styles pour les indicateurs de sélection en RTL */
[dir="rtl"] [role="option"] [role="presentation"],
[dir="rtl"] [role="option"] svg {
  right: 0.5rem;
  left: auto;
  display: inline-block !important;
}

/* Styles pour le trigger du select en RTL */
[dir="rtl"] [role="combobox"] {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
}

[dir="rtl"] [role="combobox"] > div {
  text-align: right;
  width: 100%;
}

[dir="rtl"] [role="combobox"] > svg {
  margin-right: 0;
  margin-left: auto;
}

/* Inversions pour les icônes */
[dir="rtl"] .rtl-mirror,
[dir="rtl"] .rtl-inverted {
  transform: scaleX(-1);
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }
}

/* Styles pour la barre de défilement en mode plein écran */
.fullscreen-scrollable::-webkit-scrollbar {
  width: 12px;
}

.fullscreen-scrollable::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 6px;
}

.fullscreen-scrollable::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  border: 2px solid transparent;
  background-clip: content-box;
}

.fullscreen-scrollable::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
  background-clip: content-box;
}

/* Styles pour le mode sombre */
.dark .fullscreen-scrollable::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.dark .fullscreen-scrollable::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

.dark .fullscreen-scrollable::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.4);
}

/* Support pour Firefox */
.fullscreen-scrollable {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) rgba(0, 0, 0, 0.1);
}

.dark .fullscreen-scrollable {
  scrollbar-color: rgba(255, 255, 255, 0.2) rgba(255, 255, 255, 0.1);
}

/* Styles RTL pour le mode plein écran */
[dir="rtl"] .fullscreen-scrollable {
  text-align: right;
}

/* Contrôles plein écran RTL */
[dir="rtl"] .fullscreen-controls {
  flex-direction: row-reverse;
}

/* Boutons plein écran RTL */
[dir="rtl"] .fullscreen-button-group {
  flex-direction: row-reverse;
}

/* Titre plein écran RTL */
[dir="rtl"] .fullscreen-title {
  text-align: right;
}
