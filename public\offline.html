<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Timer Kit</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 20px;
        }
        
        .container {
            max-width: 500px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.8;
        }
        
        h1 {
            font-size: 2rem;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        p {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .retry-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 30px;
            border-radius: 50px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .retry-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        
        .features {
            margin-top: 30px;
            text-align: left;
        }
        
        .feature {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .feature::before {
            content: "⏱️";
            margin-right: 10px;
        }
        
        @media (max-width: 480px) {
            .container {
                padding: 30px 20px;
            }
            
            h1 {
                font-size: 1.5rem;
            }
            
            p {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">📱</div>
        <h1>You're Offline</h1>
        <p>It looks like you're not connected to the internet. Some features may not be available, but you can still use the basic timer functions.</p>
        
        <button class="retry-btn" onclick="window.location.reload()">
            Try Again
        </button>
        
        <div class="features">
            <div class="feature">Basic timer and stopwatch still work</div>
            <div class="feature">Your data is saved locally</div>
            <div class="feature">Full functionality returns when online</div>
        </div>
    </div>

    <script>
        // Check if we're back online and reload
        window.addEventListener('online', function() {
            window.location.reload();
        });
        
        // Try to navigate to home page
        function goHome() {
            window.location.href = '/';
        }
        
        // Add click handler to retry button
        document.querySelector('.retry-btn').addEventListener('click', function() {
            if (navigator.onLine) {
                window.location.reload();
            } else {
                goHome();
            }
        });
    </script>
</body>
</html>
